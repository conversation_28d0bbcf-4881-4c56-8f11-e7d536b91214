<template>
  <el-dialog class="no-header-dialog" :lock-scroll="false" :append-to-body="true" v-model="open" width="860px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false">
    <div ref="rootRef" class="online-service-wrap">
    <div class="talk-send-wrap">
      <div class="talk-title-wrap">
        <div class="title">{{toUserName || '-'}}</div>
        <div class="buttons">
          <span @click="closeSocket" class="over-btn">结束沟通</span>
        </div>
      </div>
      <div class="container-wrap">
        <div class="container-left">
          <!-- 可上下滑滚动区域 -->
          <div id="scrollLoader-container" class="talk-content-wrap scroll-style">
            <div v-if="topLoading" class="loading">
              <div class="loader">加载历史记录...</div>
            </div>
            <!-- 消息内容列表容器 -->
            <div class="message-container">
              <!-- 消息内容列表 -->
              <div v-if="messageList && messageList.length > 0" class="message">
                <ul>
                  <li v-for="message in messageList" :key="message.id"
                    :class="isMine(message) ? 'an-move-right' : 'an-move-left'">
                    <!-- 时间 -->
                    <div class="time"><span v-if="message.createAt" v-text="message.createAt" /></div>
                    <!-- 系统提示 -->
                    <div v-if="message.type == '10000'" class="time system">
                      <span v-html="message.content" />
                    </div>
                    <div v-else :class="'main' + (isMine(message) ? ' self' : '')">
                      <!-- 头像 -->
                      <img class="avatar" :src="isMine(message) ? ConsultUserIcon : ConsultServiceIcon" alt="头像图片">
                      <!-- 文本 -->
                      <!-- <div v-if="message.type === '1'" v-emotion="message.content" class="text" /> -->
                      <div v-if="message.type == '1' || message.type == '3'" v-html="genString2emoji(toBr(message.content))" class="text" />
                      <!-- 图片 -->
                      <div v-else-if="message.type == '2'" class="text-image">
                        <el-image :src="message.content" :preview-src-list="[message.content]" class="image" alt="聊天图片" />
                      </div>
                      <!-- 其他 -->
                      <div v-else class="text" v-text="'[暂未支持的消息类型:' + message.type + ']\n\r' + message.content" />
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <!-- 输入区 -->
          <div class="send-wrap">
            <div class="op-wrap">
              <el-popover placement="top" width="400" trigger="click">
                <BxcEmojiPicker class="emoji-picker" v-model:value="replyStr" button height='200px' />
                <template #reference>
                  <img class="op-img" src="@/assets/images/online-service/emoji.png" alt="" />
                </template>
              </el-popover>
              <el-upload ref="uploadRef" :show-file-list="false" :before-upload="handleBeforeUpload" :action="upload.url"
                :headers="upload.headers" :on-success="handleFileSuccess" :data="{waterFlag: false}">
                <div class="el-upload__text">
                  <img class="op-img" src="@/assets/images/online-service/upload-image.png" alt="" />
                </div>
              </el-upload>
            </div>
            <div class="input-wrap">
              <el-input ref="replyRef" v-model="replyStr" placeholder="请输入..." :rows="4" resize="none" type="textarea"
                maxlength="500" @keyup.enter.native="send(null,$event)" />
            </div>
            <div class="send-button-wrap">
              <span class="c-88 mr-10">shift+回车换行</span>
              <el-button @click="send(null,$event)" type="primary">发送</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/userStore'
import { type IResponseData, type IUserInfo, type ICustomer } from "@/types"
import Command from '@/utils/command'
import {
  initWebSocket,
  sendWebsocket,
  closeWebsocket,
  createPacket,
  socketState
} from '@/utils/webSocket'
import ConsultUserIcon from '@/assets/images/online-service/consult-user.png'
import ConsultServiceIcon from '@/assets/images/online-service/consult-service.png'
import { getSnowflakeID, getMessageList } from '@/api/online-service'

const props = defineProps({
  open: Boolean,
  customer: {
    type: Object as PropType<ICustomer>,
    default: () => {
      return {
        userId: '',
        userName: '',
      }
    } 
  }
});
const { open, customer } = toRefs(props)

const emit = defineEmits(['update:open'])

interface IMessage {
  id: string;
  type: number | string;
  content: string;
  createAt: string;
  fromUserId: string;
  toUserId: string;
}

const rootRef = ref()
const { public: { baseURL } } = useRuntimeConfig()
const userStore = useUserStore()
const { $modal } = useNuxtApp()
const route = useRoute()
const kOnlineGuest = 'kOnlineGuest'
const messageList = ref(<IMessage[]>[])
const questionList = ref([])
const onlineStatus = ref(undefined)
const replyStr = ref('')
const topLoading = ref(false)
const total = ref(0)
const title = ref('')
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  toUserId: '',
  firstId: '',
  userId: '',
})
const toUserId = ref('')
const toUserName = ref('')
const fromUserId = ref('')
const source = ref('')
const upload = ref({
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: {},
  // 是否更新已经存在的数据
  // 上传的地址
  url: `${baseURL}/resource/oss/upload`
})
const fileSize = 5;
const fileType = ['png', 'gif', 'jpg', 'jpeg']

const isMine = (message: any) => {
  return message.fromUserId == fromUserId.value
}
const isGuest = () => {
  if(userStore.userId){
    return false
  }else{
    return true
  }
}
const scrollToBottom = () => {
  nextTick(() => {
    const scrollContainer = rootRef.value.querySelector('#scrollLoader-container')
    scrollContainer.scrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight
  })
}
const scrollTo10 = () => {
  nextTick(() => {
    const scrollContainer = rootRef.value.querySelector('#scrollLoader-container')
    if (scrollContainer) {
      scrollContainer.scrollTop = 10
    }
  })
}
const getList = () => {
  getMessageList(queryParams.value).then((res: any) => {
    topLoading.value = false
    if (messageList.value && messageList.value.length == 0) {
      messageList.value = res.rows;
      scrollToBottom()
    } else {
      messageList.value = res.rows.concat(messageList.value) // 倒序合并
      scrollTo10()
    }
    // 监听滚动
    let length = res.rows ? res.rows.length : 0
    listenMessageScroll(length)

    total.value = res.total;
  }).catch(() => {});

}
const send = (uploadUrl = null,e?: any) => {
  let sendStr = ''
  if(e && e.shiftKey && e.keyCode==13) {   //用户点击了ctrl+enter触发
    sendStr += "\n";
    return 
  }
  let type = 1
  if (uploadUrl) {
    sendStr = uploadUrl
    type = 2
  } else {
    sendStr = replyStr.value.trim()
    if (!sendStr && sendStr.length == 0) return
  }

  sendToServer(sendStr,type,undefined)
  replyStr.value = ''
}
const changeOnlineState = (info: any) => {
  onlineStatus.value = info.onlineStatus
}
const callSocket = (retObj: any) => {
  // console.log(retObj);
  if(retObj.onlineStatus != undefined){
    onlineStatus.value = retObj.onlineStatus
  }
  if (retObj.command == Command.MESSAGE_RESPONSE) { // 消息返回
    messageList.value.push(retObj.data)
    if (isGuest()) {
      localStorage.setItem('kGuest-' + fromUserId.value + '-' + toUserId.value, JSON.stringify(messageList.value))
    }
    scrollToBottom()
  } else if (retObj.command == Command.LOGOUT_RESPONSE) { // 退出登录
    closeWebsocket()
  } else if(retObj.command == Command.MESSAGE_ON_OFF_RESPONSE){ // 用户上下线响应
    changeOnlineState(retObj.data)
  }
}
const sendToServer = (sendStr: string,type: number,otherId: string | undefined) => {
  let messageInfo = createPacket({
    content: sendStr,
    type: type, // 1文字2图片//3常见问题
    otherId: otherId,
    toUserId: toUserId.value,
  }, Command.MESSAGE_REQUEST)
  sendWebsocket(messageInfo, callSocket)
}
const getUserType = () => {//咨询者类型(0访客，1用户，2机构) 
  if(userStore.userId){
    if((<IUserInfo>userStore.userInfo).userType == 'cloud_user'){
      return 1
    }
  }
  return 0
}
const closeSocket = () => {
  $modal.confirm('结束沟通将离开当前页，确定离开当前页?', "提示").then(() => {
    close()
  }).catch(() => {})
}
// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {
  let isValid = false;
  if (fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isValid = fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isValid = file.type.indexOf("image") > -1;
  }
  if (!isValid) {
    $modal.msgError(
      `文件格式不正确, 请上传${fileType.join("/")}格式文件!`
    );
    return false;
  }
  if (fileSize) {
    const isLt = file.size / 1024 / 1024 < fileSize;
    if (!isLt) {
      $modal.msgError(`上传文件大小不能超过 ${fileSize} MB!`);
      return false;
    }
  }
  return true;
}
// 文件上传成功处理
const handleFileSuccess = (response: any, file: any, fileList: any) => {
  upload.value.isUploading = false;
  if (response.code == 200) {
    send(response.data.url)
  }
}
// 监听聊天窗口滚动，并触发上下拉刷新
const listenMessageScroll = (msgLength: number) => {
  setTimeout(function() {
    nextTick(() => {
      const scrollContainer = rootRef.value.querySelector('#scrollLoader-container')

      scrollContainer.onscroll = function() {
        // console.log(`滚动中 scrollTop=${scrollContainer.scrollTop}, scrollHeight=${scrollContainer.scrollHeight}, clientHeight=${scrollContainer.clientHeight}`)
        if (scrollContainer.scrollTop <= 0 && msgLength == queryParams.value.pageSize) {
          if (topLoading.value) {
            return
          }
          topLoading.value = true
          if(messageList.value && messageList.value.length > 0){
            queryParams.value.firstId = messageList.value[0].id
          } 

          getList()
        }
      }
    })
    
  }, 50)
}

const close = () => {
  emit('update:open',false)
  closeWebsocket()
}

onMounted(async () => {
  toUserId.value = customer.value.userId
  queryParams.value.toUserId = customer.value.userId
  toUserName.value = customer.value.userName
  title.value = customer.value.userName

  if (!isGuest()) {
    fromUserId.value = userStore.userId
    queryParams.value.userId = userStore.userId
  } else { // 游客
    if (localStorage.getItem(kOnlineGuest)) {
      fromUserId.value = <string>localStorage.getItem(kOnlineGuest)
      queryParams.value.userId = fromUserId.value  
    } else {
      //网络请求获取
      try {
        let res = await getSnowflakeID() as IResponseData
        if (res.data) {
          localStorage.setItem(kOnlineGuest, res.data)
          fromUserId.value = res.data
        }
      } catch (error) {
        $modal.msgError('获取信息错误，请重新进入')
        close()
      }
    }
  }
  if (!fromUserId.value || !toUserId.value) {
    $modal.msgError('获取信息错误，请重新进入')
    close()
    return
  }

  let beatInfo = createPacket({}, Command.HEART_BEAT_REQUEST)
  let loginInfo = createPacket({
    fromUserId: fromUserId.value,
    toUserId: toUserId.value,
    opeSystem: getOS(),
    browserType: getBrowserInfo(),
    source: source.value,
    customerType: 1,// 1.客服 2.智能客服 3.专家
    userType: getUserType(),//咨询者类型(0访客，1用户，2机构)
    loginUserType: 0 // 登录连接用户类型 [0.咨询者 1.解答者 ]
  }, Command.LOGIN_REQUEST)

  initWebSocket(beatInfo, loginInfo)

  sendWebsocket(null, callSocket)
  getList()
})
onUnmounted(() => {
  closeWebsocket()
})

</script>
<style lang="scss" scoped>

.online-service-wrap {
    // height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .talk-send-wrap {
    // width: 1200px;
    height: 788px;
    display: flex;
    flex-direction: column;
    background: #F7F7F7;
    box-shadow: 0px 0px 32px 0px rgba(0, 0, 0, 0.24);
    border-radius: 5px;

    .talk-title-wrap {
      // width: 1200px;
      height: 50px;
      padding: 0 20px;
      box-sizing: border-box;
      background-color: $primary-color;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        flex: 1;
        font-size: 16px;
        color: #FFFFFF;
      }

      .buttons {
        flex: 1;
        font-size: 16px;
        color: #FFFFFF;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .over-btn {
          height: 32px;
          padding: 0 10px;
          border: 1px solid #FFFFFF;
          border-radius: 5px;
          line-height: 32px;
          cursor: pointer;
        }
      }
    }
    .container-wrap{
      flex: 1;
      display: flex;

      .container-left{
        width: 860px;
      }
    }
    
    .talk-content-wrap {
      height: calc(788px - 50px - 192px);
      overflow-y: auto;

      ul {
        display: block;
        list-style-type: disc;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 0;
      }

      .message-container {
        overflow-x: hidden;
        flex: 1;
        width: 100%;
      }

      .message {
        padding: 10px 15px;
      }

      .message li {
        margin-bottom: 15px;
        left: 0;
        position: relative;
        display: block;
      }

      .message .time {
        margin: 10px 0;
        text-align: center;
      }

      .message .text {
        display: inline-block;
        position: relative;
        max-width: calc(100% - 75px);
        min-height: 35px;
        line-height: 2.1;
        font-size: 15px;
        padding: 6px 10px;
        text-align: left;
        word-break: break-all;
        background-color: #fff;
        color: #000;
        border-radius: 4px;
        //box-shadow: 0 1px 7px -5px #000;
        overflow: hidden;
      }

      .message .avatar {
        float: left;
        margin: 0 10px 0 0;
        border-radius: 3px;
        // background: #fff;
        width: 45px;
        height: 45px;
      }

      .message .time>span {
        display: inline-block;
        padding: 3px 8px;
        font-size: 12px;
        color: #fff;
        border-radius: 2px;
        background-color: #dadada;
      }

      .message .system>span {
        padding: 4px 9px;
        text-align: left;
      }

      .message .text:before {
        content: " ";
        position: absolute;
        top: 9px;
        right: 100%;
        border: 6px solid transparent;
        border-right-color: #fff;
      }

      .message .main {
        text-align: left;
      }

      .message .self {
        text-align: right;
      }

      .message .self .avatar {
        float: right;
        margin: 0 0 0 10px;
      }

      .message .self .text {
        background-color: #9eea6a;
      }

      .message .self .text:before {
        right: inherit;
        left: 100%;
        border-right-color: transparent;
        border-left-color: #9eea6a;
      }

      .message .image {
        max-width: 200px;
      }

      img.static-emotion-gif,
      img.static-emotion {
        vertical-align: middle !important;
      }

      .an-move-left {
        left: 0;
        animation: moveLeft 0.7s ease;
        -webkit-animation: moveLeft 0.7s ease;
      }

      .an-move-right {
        left: 0;
        animation: moveRight 0.7s ease;
        -webkit-animation: moveRight 0.7s ease;
      }

      @keyframes moveRight {
        0% {
          left: -20px;
          opacity: 0;
        }

        100% {
          left: 0;
          opacity: 1;
        }
      }

      @-webkit-keyframes moveRight {
        0% {
          left: -20px;
          opacity: 0;
        }

        100% {
          left: 0;
          opacity: 1;
        }
      }

      @keyframes moveLeft {
        0% {
          left: 20px;
          opacity: 0;
        }

        100% {
          left: 0;
          opacity: 1;
        }
      }

      @-webkit-keyframes moveLeft {
        0% {
          left: 20px;
          opacity: 0;
        }

        100% {
          left: 0;
          opacity: 1;
        }
      }

      @media (max-width: 367px) {
        .fzDInfo {
          width: 82%;
        }
      }

      .loading {
        width: 100%;
        height: 40px;
        position: relative;
        overflow: hidden;
        text-align: center;
        margin: 5px 0;
        font-size: 13px;
        color: #b0b0b0;
        line-height: 100px;
      }

      .loader {
        font-size: 10px;
        margin: 8px auto;
        text-indent: -9999em;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #999;
        background: -moz-linear-gradient(left, #999 10%, rgba(255, 255, 255, 0) 42%);
        background: -webkit-linear-gradient(left, #999 10%, rgba(255, 255, 255, 0) 42%);
        background: -o-linear-gradient(left, #999 10%, rgba(255, 255, 255, 0) 42%);
        background: -ms-linear-gradient(left, #999 10%, rgba(255, 255, 255, 0) 42%);
        background: linear-gradient(to right, #999 10%, rgba(255, 255, 255, 0) 42%);
        position: relative;
        -webkit-animation: load3 1s infinite linear;
        animation: load3 1s infinite linear;
      }

      .loader:before {
        width: 50%;
        height: 50%;
        background: #999;
        border-radius: 100% 0 0 0;
        position: absolute;
        top: 0;
        left: 0;
        content: "";
      }

      .loader:after {
        background: #f5f5f5;
        width: 72%;
        height: 75%;
        border-radius: 68%;
        content: "";
        margin: auto;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
      }

      @-webkit-keyframes load3 {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      @keyframes load3 {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
    }

    .send-wrap {
      border-top: 1px solid #E8E8E8;
      height: 192px;
      display: flex;
      flex-direction: column;
      background-color: #FFFFFF;

      .op-wrap {
        height: 50px;
        background-color: #eee;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .op-img {
          margin-left: 20px;
          height: 26px;
          cursor: pointer;
        }
      }

      .input-wrap {
        flex: 1;

        .el-input {
          border: none !important;
          box-shadow: none !important;
        }

        :deep(.el-textarea__inner)  {
          border: none;
          box-shadow: none;
        }
      }

      .send-button-wrap {
        height: 40px;
        margin: 10px;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .el-button {
          font-size: 14px;
          color: #FFFFFF;
          border-radius: 3px;
          padding: 10px 20px;
        }
      }
    }

  }

</style>