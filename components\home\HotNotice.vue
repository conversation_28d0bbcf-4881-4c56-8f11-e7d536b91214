<template>
  <div class="notice-wrap">
    <div class="notice-container main-wrap">
      <div class="notice-list">
        <div v-for="item in standardList" :key="item.type" class="notice-item">
          <h3 class="title">{{item.title}}</h3>
          <ul v-if="item.list && item.list.length > 0" class="n-list">
            <li v-for="row in item.list" :key="row.id" @click="handleDetail(row,item.type)" class="n-item">
              <div class="icon"><img src="@/assets/images/home/<USER>" alt=""></div>
              <div class="title overflow-ellipsis">
                {{item.type == '0' ? row.title: row.standardCode +" | "+row.standardName}}
              </div>
              <div v-if="item.type == '0'" class="date">{{row.publishDate}}</div>
            </li>
          </ul>
          <div v-else class="n-list">
            <BxcEmpty class="mt-30" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getStandardNoticeList, getStandardForeignList } from '@/api/home'
import type { IStandardInfo } from '@/types'

const standardList = ref([{
  type: '0',
  title: '标准公告',
  list: <IStandardInfo[]>[]
},{
  type: '1',
  title: '热点国际标准',
  list: <IStandardInfo[]>[]
}])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  pvSort: '1'
})
const getList = async (type: string) => {
  try {
    let res: any
    if(type == '0'){
      res = await getStandardNoticeList(queryParams.value)
    }else if(type == '1'){
      res = await getStandardForeignList(queryParams.value)
    }
    
    standardList.value.forEach((item: any) => {
      if(item.type == type) {
        item.list = res.rows || []
      }
    })
  } catch (error) {}
}
const handleDetail = (row: any, type: string) => {
  if(type == '0'){
    navigateTo(`/retrieval/announcementDetail?id=${row.id}`)
  }else{
    navigateTo(`/retrieval/internationDetail?id=${row.id}`)
  }
}

await getList('0')
await getList('1')
</script>
<style lang="scss" scoped>
.notice-wrap{
  margin-top: 70px;
  background: #F8F9FA;
  .notice-list{
    display: flex;
    justify-content: space-between;
    
    
    .notice-item{
      margin-top: 40px;
      margin-bottom: 70px;
      width: 550px;
      
      .n-list{
        height: 380px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        // background: #000;
        .n-item{
          height: 38px;
          font-size: 14px;
          display: flex;
          align-items: center;
          cursor: pointer;
          &:hover{
            .title,.date{
              color: $primary-color;
            }
          }
          .icon{
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .title{
            margin-left: 15px;
            margin-right: auto;
            color: #333333;
          }
          .date{
            margin-left: 50px;
            color: #888888;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
</style>