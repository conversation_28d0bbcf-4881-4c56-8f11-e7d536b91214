<template>
  <div>
    <div class="banner">
      <div class="main-wrap">
        <div class="f-32 c-ff f-bold overflow-ellipsis">{{ form.systemName || '-' }}</div>
        <div class="banner-title mt-20 lh-26 overflow-three-ellipsis">
          {{ form.systemDescription || '-' }}
        </div>
        <div @click="handleSubscription" class="banner-btn">
          <span class="iconfont f-18 mr-5" :class="form.subscription ? 'icon-yidingyue' : 'icon-dingyue'"></span>
          {{ form.subscription ? '已订阅' : '订阅' }}
        </div>
      </div>
    </div>
    <div class="content main-wrap">
      <div class="content-left">
        <div class="flex flex-ai-center content-left-title">
          <span class="iconfont icon-biaozhuntixi f-18 c-ff"></span>
          <span class="f-16 c-ff f-bold ml-10">{{ form.systemName }}</span>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          empty-text="暂无数据"
          :highlight-current="true"
          :expand-on-click-node="false"
          :props="{ children: 'children', label: 'name' }"
          :default-checked-keys="defaultCheckedKeys"
          :default-expand-all="true"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <div class="flex flex-ai-center" style="width: 100%">
              <span v-if="node.level == 1" class="iconfont icon-biaozhunguifan f-18 c-primary"></span>
              <RetrievalToolTip :text="node.label" />
            </div>
          </template>
        </el-tree>
      </div>
      <div class="content-right">
        <ClientOnly>
          <div class="content-right-top">
            <el-form ref="ruleFormRef" :model="query">
              <el-form-item label="" prop="standardCode">
                <el-input v-model="query.standardCode" clearable placeholder="输入标准号" />
              </el-form-item>
              <el-form-item label="" prop="standardName">
                <el-input v-model="query.standardName" clearable placeholder="输入标准名称" />
              </el-form-item>
              <el-form-item label="" prop="standardType">
                <el-select v-model="query.standardType" clearable placeholder="请选择标准类型">
                  <el-option
                    v-show="item.dictValue != 5 && item.dictValue != 6"
                    v-for="item in standardTypeOptions"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div>
              <el-button @click="getData('pageNum')" type="primary" icon="Search" class="ml-20" />
              <el-button @click="resetForm(ruleFormRef)" type="primary" plain icon="Refresh" class="ml-10" />
            </div>
          </div>
        </ClientOnly>
        <template v-if="tableData && tableData.length > 0">
          <div
            v-loading="loading"
            v-for="(item, index) in tableData"
            :key="item.id"
            @click="handleJump('/retrieval/domesticDetail', { id: item.id })"
            class="content-right-card"
          >
            <div class="content-right-card-info">
              <div class="content-right-card-info-number">
                {{ (query.pageNum - 1) * query.pageSize + index + 1 }}
              </div>
              <div class="content-right-card-info-title">
                <div class="flex flex-ai-center overflow-ellipsis">
                  <div class="f-16 c-33 f-bold overflow-ellipsis">
                    {{ item.standardCode }}&nbsp;|&nbsp;{{ item.standardName }}
                  </div>
                  <div :class="getStatusColor('blue')" class="ml-15">{{ item.standardTypeName }}</div>
                  <div :class="getStatusColor(statusToString(item.standardStatus))" class="ml-10">
                    {{ item.standardStatusName }}
                  </div>
                </div>
                <div class="f-14 c-33 flex flex-sb flex-ai-center mt-15">
                  <div class="flex-1 overflow-ellipsis">
                    节点名称：
                    <span class="c-88">{{ item.nodeName }}</span>
                  </div>
                  <div class="ml-10 flex-1 overflow-ellipsis">
                    发布日期：
                    <span class="c-88">{{ item.publishDate }}</span>
                  </div>
                  <div class="ml-10 flex-1 overflow-ellipsis">
                    实施日期：
                    <span class="c-88">{{ item.executeDate }}</span>
                  </div>
                </div>
              </div>
            </div>
            <span
              @click.stop="handleTrusteeship(item)"
              class="iconfont content-right-card-icon c-88"
              :class="item.isTrusteeship == 1 ? 'icon-kuangjituoguan c-primary' : 'icon-tuoguan c-88'"
            ></span>
          </div>
          <BxcPagination
            v-model:page="query.pageNum"
            v-model:limit="query.pageSize"
            :total="tableTotal"
            @pagination="getData"
            class="mt-35"
          />
        </template>
        <BxcEmpty v-else />
      </div>
    </div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleLoginSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ElMessageBox } from 'element-plus';
  import { getDicts } from '@/api/common';
  import { getSystemDetailTree, getSystemDetailTable } from '@/api/data/system';
  import { getStatusColor, handleJump } from '@/utils/common';
  import { type IStandardInfo, type IDicts, type ISystemInfo, type IResponseData } from '@/types';
  import { ElTree } from 'element-plus';
  import type { FormInstance } from 'element-plus';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();
  const { $modal } = useNuxtApp();
  const route = useRoute();

  interface ITree {
    id: string;
    name: string;
    [key: string]: any;
  }

  const ruleFormRef = ref<FormInstance>();
  const loading = ref(false);
  const treeRef = ref<InstanceType<typeof ElTree>>();
  const defaultCheckedKeys = ref([]);
  const standardTypeOptions = ref<IDicts[]>([]);
  const form = ref<ISystemInfo>({
    subscription: '',
    systemName: '',
    hotTag: '',
    systemDescription: '',
    standardType: '',
  });
  const treeData = ref<ITree[]>([]);
  const query = ref({
    nodeId: '',
    pageNum: 1,
    pageSize: 10,
    standardCode: '',
    standardName: '',
    standardType: '',
  });
  const tableData = ref<IStandardInfo[]>([]);
  const tableTotal = ref(0);
  const openLogin = ref(false);

  let dictRes = <IResponseData>await getDicts('bxc_standard_type');
  standardTypeOptions.value = dictRes.data;

  let treeRes = <IResponseData>await getSystemDetailTree(route.query.id as string | number);
  treeData.value = treeRes.data || [];
  form.value = treeRes.otherInfo || {};

  if (treeData.value && treeData.value.length > 0) {
    query.value.nodeId = treeData.value[0].id;
  }

  let tableRes = <IResponseData>await getSystemDetailTable(query.value);
  tableData.value = tableRes.rows || [];
  tableTotal.value = tableRes.total || 0;

  onMounted(() => {
    if (treeRef.value) treeRef.value!.setCurrentKey(query.value.nodeId);
  });

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const getData = async (pageNum?: string) => {
    if (pageNum) query.value.pageNum = 1;
    let { rows, total } = <IResponseData>await useHttp.get('/business/standardSystemPfNode/getStandardWithNodePage', query.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleNodeClick = (item: any) => {
    query.value.nodeId = item.id;
    if (treeRef.value) treeRef.value!.setCurrentKey(query.value.nodeId);
    getData('pageNum');
  };

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    getData('pageNum');
  };

  const handleTrusteeship = async (row: any) => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      if (row.isTrusteeship == 1) {
        ElMessageBox.confirm('确认取消托管标准【' + row.standardCode + '】？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          lockScroll: false,
        })
          .then(() => {
            useHttp.delete('/search/trusteeshipManage/remove', { ids: [row.id] }).then(res => {
              let data = res as IResponseData;
              if (data.code == 200) {
                $modal.msgSuccess('取消标准托管成功！');
                getData('pageNum');
              }
            });
          })
          .catch(() => {});
      } else {
        let data = <IResponseData>await useHttp.post('/search/trusteeshipManage', {
          standardId: row.id,
          standardCode: row.standardCode,
          standardType: row.standardType,
        });
        if (data.code == 200) {
          $modal.msgSuccess('标准托管成功！');
          getData('pageNum');
        }
      }
    }
  };

  const handleSubscription = async () => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      if (form.value.subscription) {
        ElMessageBox.confirm('确认取消订阅专题【' + form.value.systemName + '】？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          lockScroll: false,
        })
          .then(() => {
            useHttp.delete('/business/standardSystemSubscription/delete/' + form.value.id).then(res => {
              let data = res as IResponseData;
              if (data.code == 200) {
                $modal.msgSuccess('取消订阅！');
                getDetail();
              }
            });
          })
          .catch(() => {});
      } else {
        let data = <IResponseData>await useHttp.post('/business/standardSystemSubscription', { systemId: form.value.id });
        if (data.code == 200) {
          $modal.msgSuccess('订阅成功！');
          getDetail();
        }
      }
    }
  };

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/business/standardSystem/' + route.query.id);
    form.value = data;
  };

  const handleLoginSuccess = () => {
    getDetail()
    getData();
  }
</script>

<style lang="scss" scoped>
  .banner {
    width: 100%;
    min-height: 380px;
    background: url('@/assets/images/system/detail-cover.png') no-repeat center;
    background-size: 100% 100%;

    .main-wrap {
      padding-top: 120px;
    }

    &-title {
      font-size: 16px;
      color: #fff;
      width: 1050px;
      line-height: 24px;
    }

    &-btn {
      color: #fff;
      width: 240px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: $primary-color;
      border-radius: 20px;
      margin-top: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }

  .content {
    padding: 45px 0 35px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    &-left {
      width: 300px;
      background-color: #f8f9fb;

      &-title {
        padding: 10px 18px;
        box-sizing: border-box;
        background-color: $primary-color;
        border-radius: 3px 3px 0px 0px;
        margin-bottom: 15px;
      }
    }

    &-right {
      width: 870px;

      &-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }

      &-card {
        padding: 15px 0;
        box-sizing: border-box;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-info {
          display: flex;
          align-items: center;
          width: 90%;
          overflow: hidden;
          cursor: pointer;

          &-number {
            width: 55px;
            text-align: center;
            font-size: 16px;
          }

          &-title {
            width: calc(100% - 55px);
            flex: 1;
            overflow: hidden;
          }
        }

        &-icon {
          font-size: 20px;
          display: block;
          cursor: pointer;
        }
      }
    }
  }

  :deep(.el-tree) {
    background-color: #f8f9fb;
    padding: 0 18px 30px;
    box-sizing: border-box;
  }

  :deep(.el-tree__empty-block) {
    background-color: #f8f9fb;
  }

  :deep(.el-form) {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    flex: 1;
  }

  :deep(.el-form-item) {
    width: 100%;
    margin: 0;
  }

  :deep(.el-input) {
    width: 100% !important;
  }

  :deep(.el-select__wrapper) {
    width: 100% !important;
  }

  :deep(.el-button--primary) {
    border: none;
  }
</style>
