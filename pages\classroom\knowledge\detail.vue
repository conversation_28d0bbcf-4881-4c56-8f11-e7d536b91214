<template>
  <div class="container mt-60">
    <div class="main-wrap">
      <div class="f-18 c-33 f-bold">{{ form.knowledgeName }}</div>
      <div v-if="form.theme || (form.economicTypeNameList && form.economicTypeNameList.length > 0)" class="info-label">
        <div v-if="form.theme" class="info-label-item" :class="getStatusColor('green')">{{ form.themeName }}</div>
        <template v-if="form.economicTypeNameList && form.economicTypeNameList.length > 0">
          <div
            v-for="(item, index) in form.economicTypeNameList"
            :key="index"
            class="info-label-item"
            :class="getStatusColor('purple')"
          >
            {{ item }}
          </div>
        </template>
      </div>
      <div class="info-content">
        <div class="flex f-14 c-33">
          <div class="info-content-label">发布日期：</div>
          <div class="info-content-value">{{ form.publishDate.split(' ')[0] }}</div>
        </div>
        <div class="flex f-14 c-33 mt-5">
          <div class="info-content-label">关联标准：</div>
          <div class="info-content-value">{{ form.standardCodeName }}</div>
        </div>
        <div class="flex f-14 c-33 mt-5">
          <div class="info-content-label">课程简介：</div>
          <div class="info-content-value">{{ form.introduction }}</div>
        </div>
      </div>
      <el-divider />
      <template v-if="form.contentFormat == 0">
        <div
          @click="previewUrl = item.url"
          v-for="(item, index) in form.contentFileList"
          :key="index"
          class="f-14 mt-20 mr-10 c-33 flex flex-ai-center"
        >
          <i v-if="getFileExtension(item.url)" class="iconfont icon-folder-video-fill f-18 c-primary"></i>
          <i v-else class="iconfont icon-wenben f-18 c-primary"></i>
          <span class="text-underline ml-5">{{ form.knowledgeName }}{{ index + 1 }}</span>
        </div>
        <iframe
          v-if="previewUrl"
          :src="runtimeConfig.public.kkFileURL + '?url=' + encodeURIComponent(base64Encode(previewUrl))"
          class="_preview"
        ></iframe>
      </template>
      <template v-else>
        <div v-html="form.contentText" class="app-richtext"></div>
        <template v-if="form.contentFileList && form.contentFileList.length > 0">
          <div class="f-20 f-bold mt-40">相关文件</div>
          <div
            @click="handlePreview(item.url)"
            v-for="(item, index) in form.contentFileList"
            :key="index"
            class="f-14 mt-20 mr-10 c-33 flex flex-ai-center"
          >
            <i class="iconfont icon-wenben f-18 c-primary"></i>
            <span class="text-underline ml-5">{{ item.name }}</span>
          </div>
        </template>
      </template>
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :isShowFeedback="false" :type="8" @updateData="getDetail" />
    <BxcPreviewFile v-if="open" v-model:open="open" :url="url" />
  </div>
</template>

<script setup lang="ts">
  import { getKnowledgeDetail } from '@/api/classroom/knowledge';
  import { type IKnowledgeInfo, type IResponseData } from '@/types';

  const route = useRoute();
  const runtimeConfig = useRuntimeConfig();

  const previewUrl = ref('');
  const open = ref(false);
  const url = ref('');
  const form = ref<IKnowledgeInfo>({
    knowledgeName: '',
    introduction: '',
    publishDate: '',
    standardCodes: '',
  });

  let { data } = <IResponseData>await getKnowledgeDetail(route.query.id as string | number);
  data.economicTypeNameList = data.economicTypeName ? splitStrToArray(data.economicTypeName, ' | ') : [];
  form.value = data || {};
  if (form.value.contentFileList && form.value.contentFileList.length > 0) previewUrl.value = form.value.contentFileList[0].url;

  useHead({
    title: form.value.knowledgeName,
    meta: [
      { name: 'keywords', content: '标准学院，标准解读，标准政策，标准知识' },
      {
        name: 'description',
        content:
          '标信查平台标准学院频道，名师在线讲解标准知识，深入解读标准内容，及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务。',
      },
    ],
  });

  const getDetail = async () => {
    let { data } = <IResponseData>await getKnowledgeDetail(route.query.id as string | number);
    data.economicTypeNameList = data.economicTypeName ? splitStrToArray(data.economicTypeName, ' | ') : [];
    form.value = data || {};
    if (form.value.contentFileList && form.value.contentFileList.length > 0) previewUrl.value = form.value.contentFileList[0].url;
  };

  const getFileExtension = (str: string) => {
    const lastDotIndex = str.lastIndexOf('.');
    if (lastDotIndex === -1) return '';
    const extension = str.slice(lastDotIndex + 1).toLowerCase();
    if (extension == 'mp4') {
      return true;
    } else {
      return false;
    }
  };

  const handlePreview = (row: string) => {
    url.value = row;
    open.value = true;
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 25px 0 70px;
    box-sizing: border-box;
    background-color: #f8f9fb;
  }

  .main-wrap {
    background-color: #fff;
    padding: 30px 25px 57px;
    box-sizing: border-box;
  }

  .info {
    &-label {
      display: flex;
      flex-wrap: wrap;
      margin-top: 15px;

      &-item {
        margin: 0 10px 10px 0;
      }
    }

    &-content {
      margin-top: 5px;
      line-height: 25px;

      &-label {
        width: 70px;
      }

      &-value {
        flex: 1;
      }
    }
  }

  ._preview {
    width: 100%;
    height: 75vh;
    border: none;
    margin-top: 20px;

    &::-webkit-scrollbar-track-piece {
      background: #e8e8e8 !important;
    }

    &::-webkit-scrollbar {
      width: 6px !important;
    }

    &::-webkit-scrollbar-thumb {
      background: #d1d1d1 !important;
      border-radius: 20px !important;
    }
  }

  .text-underline {
    cursor: pointer;

    &:hover {
      color: $primary-color !important;
    }
  }

  :deep(.el-divider--horizontal) {
    margin: 12px 0 30px !important;
  }
</style>
