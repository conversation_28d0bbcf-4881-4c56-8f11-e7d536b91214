<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="form.keyword"
                maxlength="50"
                placeholder="输入专家姓名、工作单位、所属委员会关键词检索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <div class="flex flex-ai-center mb-15 f-14 c-33">
          为您找到
          <span class="c-primary">{{ tableTotal }}</span>
          条相关标准专家记录
        </div>
        <el-table :data="tableData">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column type="index" label="序号" width="80">
            <template #default="{ $index }">
              {{ (form.pageNum - 1) * form.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="专家姓名" show-overflow-tooltip>
            <template #default="{ row }">
              <span @click="handleJump('/retrieval/expertDetail', { id: row.id })" class="c-primary pointer">
                {{ row.commissionerName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="workUnit" label="工作单位" show-overflow-tooltip />
          <el-table-column prop="committeeNumberList" label="所属委员会" show-overflow-tooltip />
        </el-table>
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
          class="mt-30"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准专家检索_标准专家查询_行业标准专家检索_标信查平台',
    meta: [
      { name: 'keywords', content: '标准专家，标准专家检索，标准专家检查询，标信查' },
      {
        name: 'description',
        content:
          '标信查平台及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务，并为用户提供一些标准的增值服务。',
      },
    ],
  });

  import { getExpertList } from '@/api/retrieval/expert';
  import { handleJump } from '@/utils/common';
  import { type IExpertInfo, type IResponseData } from '@/types';

  interface IForm {
    keyword?: string;
    pageNum: number;
    pageSize: number;
  }

  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref<IExpertInfo[]>([]);
  const tableTotal = ref(0);

  let { rows, total } = <IResponseData>await getExpertList(form.value);
  tableData.value = rows || [];
  tableTotal.value = total || 0;

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    let { rows, total } = <IResponseData>await useHttp.get('/search/sdc/tcCommissioner/list', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleReset = () => {
    form.value.keyword = '';
    getData('pageNum');
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(104);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    form.value.keyword = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    form.value.keyword = form.value.keyword?.replace(/^\s+|\s+$/g, '');
    if (form.value.keyword) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: form.value.keyword,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    :deep(.el-input) {
      width: 95% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 20px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-reset {
      position: absolute;
      right: -40px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }
</style>
