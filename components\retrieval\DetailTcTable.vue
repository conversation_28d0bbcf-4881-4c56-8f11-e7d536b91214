<template>
  <div v-if="props.tableData && props.tableData.length > 0">
    <div class="f-20 f-bold">相关标准计划</div>
    <el-table :data="props.tableData" class="mt-20">
      <template #empty>
        <BxcEmpty />
      </template>
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column label="计划号" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleJump('/retrieval/planDetail', { id: row.id })" class="c-primary pointer">
            {{ row.planNumber }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="entryName" label="项目名称" show-overflow-tooltip />
      <el-table-column prop="amendName" label="制修订" show-overflow-tooltip width="100" />
      <el-table-column prop="projectStatusName" label="状态" show-overflow-tooltip width="100" />
      <el-table-column prop="planReleaseDate" label="下达日期" show-overflow-tooltip width="120" />
    </el-table>
  </div>
</template>

<script setup lang="ts">
  import { handleJump } from '@/utils/common';

  const props = defineProps({
    tableData: { type: Array },
  });
</script>

<style lang="scss" scoped></style>
