import { encode, decode } from '@/utils/codec'

// 定义一个用于存储WebSocket回调函数的类型，具体类型可根据实际回调参数和返回值调整
type WebSocketCallback = (data: any) => void;

// 存储WebSocket连接相关的全局变量声明及初始类型定义
let global_callback: WebSocketCallback | null = null;
let socket: WebSocket | null = null; // 存储 WebSocket 连接，初始化为空字符串，后续会赋值为WebSocket实例
let timeoutObj: NodeJS.Timeout | number = 0;  // 心跳定时器
let serverTimoutObj: NodeJS.Timeout | number = 0; // 服务超时定时关闭
let lockReconnect: boolean = false; // 是否真正建立了连接
let timeoutnum: NodeJS.Timeout | number = 0; // 重新连接的定时器，  没连接上会一直重连，设置延迟避免请求过多
let beatObj: any; // 心跳相关信息对象，具体类型根据实际传入内容确定
let loginObj: any; // 登录相关信息对象，具体类型根据实际传入内容确定

const { public: { webSocketURL } } = useRuntimeConfig()
// 定义WebSocket配置对象的类型
type SocketConfigType = {
  url: string;
  retryTimeout: number; // 心跳时间（单位：毫秒），这里暂定20s，即20000毫秒
};
// 定义WebSocket配置对象
const socketConfig: SocketConfigType = {
  url: webSocketURL,
  retryTimeout: 20000 // 心跳时间 暂定20s
}

export const sendWebsocket = function (agentData: any, callback: WebSocketCallback) {
  global_callback = callback
  if(agentData){
    socketOnSend(agentData)
  }
}

export const initWebSocket = function (beatInfo: any, loginInfo: any) {
  let weburl = socketConfig.url
  beatObj = beatInfo
  loginObj = loginInfo
  if (!window.WebSocket) { 
    console.log('当前浏览器不支持WebSocket')
    return 
  }
  if (!socket || socket.readyState != 1) {
    socket = new WebSocket(weburl)
    socket.binaryType = 'arraybuffer'
    socketOnOpen()
    socketOnClose()
    socketOnError()
    socketOnMessage()
  }
}

/**
 * 关闭websocket函数
 */
export const closeWebsocket = function () {
  if (socket) {
    socket.close()
  }
  clearTimeout(timeoutObj)
  clearTimeout(serverTimoutObj)
}
export const loginWebsocket = (loginInfo: any) => {
  loginObj = loginInfo
  socket!.send(encode(loginObj)!)
}

function socketOnSend(data: any) {
  socket!.send(encode(data)!)
}
// 0        CONNECTING        连接尚未建立
// 1        OPEN            WebSocket的链接已经建立
// 2        CLOSING            连接正在关闭
// 3        CLOSED            连接已经关闭或不可用
export const socketState = () => {
  return socket && socket.readyState ? socket.readyState : 0
}
function socketOnOpen() {
  socket!.onopen = () => {
    console.log('socket连接成功')
    start()
  }
}

// 开启
function start() {
  if (socket!.readyState === 1) {
    // 如果连接正常  给后端发送指定消息
    socket!.send(encode(beatObj)!)
    // 登录
    if(loginObj){
      socket!.send(encode(loginObj)!)
    }
  }
  heartBeat()
}
// 心跳包
function heartBeat() {
  timeoutObj && clearTimeout(timeoutObj)
  serverTimoutObj && clearTimeout(serverTimoutObj)
  timeoutObj = setTimeout(() => {
    // 这里发送一个心跳，后端收到后返回一个心跳消息
    if (socket!.readyState === 1) {
      // 如果连接正常  给后端发送指定消息
      socket!.send(encode(beatObj)!)
      return
    } else {
      // 重连
      reconnect()
    }
    // serverTimoutObj = setTimeout(() => {
    //   // 超时关闭连接
    //   socket.close()
    // }, socketConfig.retryTimeout);
  }, socketConfig.retryTimeout);
}

// 重连
function reconnect() {
  if (lockReconnect) {
    return
  }
  lockReconnect = true
  timeoutnum && clearTimeout(timeoutnum)
  timeoutnum = setTimeout(() => {
    initWebSocket(beatObj, loginObj);
    lockReconnect = false
  }, 5000);
}

function socketOnClose() {
  socket!.onclose = () => {
    console.log('socket已经关闭')
  }
}

function socketOnError() {
  socket!.onerror = () => {
    lockReconnect = false
    reconnect()
    console.log('socket 连接失败')
  }
}

function socketOnMessage() {
  socket!.onmessage = (e: any) => {

    if(global_callback){
      global_callback(decode(e.data))
    }
    reset()
  }
}

// 重置心跳
function reset() {
  // 清除时间
  clearTimeout(timeoutObj)
  clearTimeout(serverTimoutObj)
  // 重启心跳
  heartBeat()
}
export function createPacket(data:any, command: number) {
  return Object.assign({
    version: 1,
    command: command
  }, data)
}