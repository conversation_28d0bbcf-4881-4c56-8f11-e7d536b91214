<template>
  <div>
    <div class="h-title">调查单位基本情况</div>
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="mt-25 form-inline">
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" maxlength="100" />
      </el-form-item>
      <!-- 唯一校验 -->
      <el-form-item label="统一社会信用代码" prop="socialCode">
        <el-input v-model="form.socialCode" placeholder="请输入统一社会信用代码" />
      </el-form-item>
      <el-form-item prop="legalRepresent">
        <template #label>
          法定代表人
          <span class="c-99">（单位负责人）</span>
        </template>
        <el-input v-model="form.legalRepresent" placeholder="请输入姓名" maxlength="50" />
      </el-form-item>
      <el-form-item label="联系电话" prop="legalRepresentContact">
        <el-input v-model="form.legalRepresentContact" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item prop="standardizationManager">
        <template #label>
          标准化负责人
          <span class="c-99">（标准总监）</span>
        </template>
        <el-input v-model="form.standardizationManager" placeholder="请输入姓名" maxlength="50" />
      </el-form-item>
      <el-form-item label="联系电话" prop="standardizationManagerContact">
        <el-input v-model="form.standardizationManagerContact" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="负责人电子邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入负责人电子邮箱" />
      </el-form-item>
      <el-form-item label="单位网址" prop="unitWebsite">
        <el-input v-model="form.unitWebsite" placeholder="请输入单位网址" maxlength="100" />
      </el-form-item>
      <el-form-item label="行业类别" prop="nationalEconomicTypeId">
        <el-cascader
          ref="cascaderRef"
          @change="handleChange"
          v-model="form.nationalEconomicTypeId"
          :options="nationalEconomicTypeOptions"
          :props="{
            emitPath: false,
            label: 'name',
            value: 'id',
          }"
          clearable
          filterable
          placeholder="请选择行业类别"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="行业代码" prop="nationalEconomicTypeCode">
        <el-input v-model="form.nationalEconomicTypeCode" disabled />
      </el-form-item>
      <el-form-item label="产业类别" prop="industryCategory" class="half-column">
        <el-select v-model="form.industryCategory" placeholder="请选择产业类别" clearable>
          <el-option
            v-for="item in industryCategoryOptions"
            :key="item['dictValue']"
            :label="item['dictLabel']"
            :value="item['dictValue']"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="unitAddressRegion" label="单位地址" class="one-column">
        <div class="flex flex-sb flex-1">
          <el-cascader
            v-model="form.unitAddressRegion"
            clearable
            filterable
            collapse-tags
            :options="regionOptions"
            :props="{
              emitPath: false,
              label: 'name',
              value: 'code',
              children: 'childRegion',
            }"
            placeholder="请选择单位地址"
            class="cascader-fold"
          ></el-cascader>
          <el-input v-model="form.unitAddressDetail" placeholder="请输入" maxlength="200" />
        </div>
      </el-form-item>
      <el-form-item prop="mainProduct" class="one-column">
        <template #label>
          主要产品
          <span class="c-99">（或主要业务活动）</span>
        </template>
        <el-input v-model="form.mainProduct" type="textarea" :rows="5" placeholder="请输入" maxlength="300" show-word-limit />
      </el-form-item>
    </el-form>
    <el-button @click="nextPage" :loading="loading" type="primary">下一页</el-button>
  </div>
</template>

<script setup lang="ts">
  import { checkValidity } from '@/api/questionnaire';
  import { getDicts, getDistrictList } from '@/api/common';
  import { getNationalEconomicTypeTree, checkFillingSocialCode } from '@/api/questionnaire';
  import { type IResponseData, type IDicts } from '@/types';

  const { $modal } = useNuxtApp();
  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    info: { type: Object },
  });

  const CheckUniqueness = (rule: any, value: any, callback: any) => {
    if (form.value.enterpriseName) {
      checkFillingSocialCode({
        fillingTaskId: props.info?.id,
        enterpriseName: form.value.enterpriseName,
        socialCode: value,
      }).then((res: any) => {
        if (res.data) {
          callback(new Error('输入的企业信息已填报，不能重复填报'));
        } else {
          callback();
        }
      });
    } else {
      callback();
    }
  };

  const CheckUnitAddressRegion = (rule: any, value: any, callback: any) => {
    if (value == '') {
      callback(new Error('请选择省市区'));
    } else if (form.value.unitAddressDetail == '') {
      callback(new Error('请完善地址信息'));
    } else {
      callback();
    }
  };

  const cascaderRef = ref();
  const formRef = ref();
  const loading = ref(false);
  const nationalEconomicTypeOptions = ref([]);
  const industryCategoryOptions = ref([]);
  const regionOptions = ref([]);
  const form = ref({
    enterpriseName: '',
    socialCode: '',
    legalRepresent: '',
    legalRepresentContact: '',
    standardizationManager: '',
    standardizationManagerContact: '',
    email: '',
    unitWebsite: '',
    nationalEconomicTypeId: '',
    nationalEconomicTypeCode: '',
    industryCategory: '',
    unitAddressRegion: '',
    unitAddressDetail: '',
    mainProduct: '',
  });
  const rules = reactive({
    enterpriseName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
    socialCode: [
      { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
      { pattern: /^[0-9A-HJ-NPQRTUWXY]{18}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' },
      { validator: CheckUniqueness, trigger: 'blur' },
    ],
    legalRepresent: [
      { required: true, message: '请输入法人姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '姓名长度必须介于 2 和 20 之间', trigger: 'blur' },
      { pattern: realNamePattern, message: '法人姓名2-20个字母、汉字', trigger: 'blur' },
    ],
    legalRepresentContact: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: mobileValidPattern, message: '请输入正确的联系电话', trigger: 'blur' },
    ],
    standardizationManager: [
      { required: true, message: '请输入标准化负责人姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '姓名长度必须介于 2 和 20 之间', trigger: 'blur' },
      { pattern: realNamePattern, message: '标准化负责人姓名2-20个字母、汉字', trigger: 'blur' },
    ],
    standardizationManagerContact: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: mobileValidPattern, message: '请输入正确的联系电话', trigger: 'blur' },
    ],
    unitWebsite: [{ pattern: /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/, message: '请输入正确的单位网址', trigger: 'blur' }],
    email: [
      { pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确的负责人电子邮箱', trigger: 'blur' },
    ],
    nationalEconomicTypeId: [{ required: true, message: '请选择行业类别', trigger: 'change' }],
    industryCategory: [{ required: true, message: '请选择产业类别', trigger: 'change' }],
    unitAddressRegion: [{ required: true, validator: CheckUnitAddressRegion, trigger: 'blur' }],
    mainProduct: [{ required: true, message: '请完善主要产品信息', trigger: 'blur' }],
  });

  let nationalEconomicTypeData = <IResponseData>await getNationalEconomicTypeTree();
  nationalEconomicTypeOptions.value = nationalEconomicTypeData?.data || [];

  let industryCategoryDict = <IDicts>await getDicts('bxc_filling_industry_category');
  industryCategoryOptions.value = industryCategoryDict?.data || [];

  let regionOptionsData = <IResponseData>await getDistrictList();
  regionOptions.value = regionOptionsData?.data || [];

  const handleChange = (e: any) => {
    form.value.nationalEconomicTypeCode = cascaderRef?.value.getCheckedNodes(true)[0]?.data?.code;
  };

  const nextPage = () => {
    loading.value = true;
    checkValidity(route.query.id as string | number).then((res: any) => {
      if (res.data) {
        formRef?.value.validate((valid: boolean) => {
          if (valid) {
            emit('handleNext', form.value, 1);
          }
          loading.value = false;
        });
      } else {
        $modal.msgError(res.message);
        router.replace('/questionnaire');
      }
    });
  };

  const emit = defineEmits(['handleNext']);
</script>

<style lang="scss" scoped>
  .form-inline {
    :deep(.el-form-item) {
      width: 48% !important;
    }

    .half-column {
      width: 48% !important;
    }

    .one-column {
      width: 100% !important;

      :deep(.el-input) {
        width: 48% !important;
      }

      :deep(.el-cascader) {
        width: 48% !important;
      }

      :deep(.el-cascader .el-input) {
        width: 100% !important;
      }
    }
  }

  :deep(.el-input) {
    height: 36px !important;
  }

  :deep(.el-button) {
    display: block;
    width: 180px !important;
    height: 42px !important;
    border-radius: 3px !important;
    margin: 40px auto 60px !important;
    border-color: $primary-color !important;
  }

  :deep(.el-button:hover) {
    background-color: $primary-color !important;
  }

  :deep(.el-button.is-plain) {
    color: $primary-color !important;
  }
</style>
