.comparison-full-text-wrap{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 800px;
  // gap: 0px 20px;
  min-width: 1200px;
  overflow-y: hidden;
  padding-bottom: 50px;
  position: relative;
  .left{
    height: 100%;
    background: #ECF5FC;
    width: calc(50vw - 20px);
    flex-shrink: 0;
    display: flex;
    .left-tree-wrap{
      width: 348px;
      border-right: 1px solid #FFFFFF;
      overflow-y: auto;
    }
    .left-content{
      flex: calc(100% - 368px);
      flex-shrink: 0;
      background: #FFFFFF;
      margin: 10px;
      overflow-y: auto;
      padding: 20px 20px;
      font-size: 14px;
      color: #333333;
      line-height: 26px;
      box-sizing: border-box;
      word-wrap: break-word;
      table {
        border-collapse: collapse
      }
    
      table,th,td {
        border: 1px solid #999;
        padding: 8px;
      }
    }
  }
  .right{
    height: 100%;
    background: #EBF1FC;
    width: calc(50vw - 20px);
    flex-shrink: 0;
    display: flex;
    .right-tree-wrap{
      width: 348px;
      border-left: 1px solid #FFFFFF;
      overflow-y: auto;
    }
    .right-content{
      flex: calc(100% - 368px);
      flex-shrink: 0;
      background: #FFFFFF;
      margin: 10px;
      overflow-y: auto;
      padding: 20px 20px;
      font-size: 14px;
      color: #333333;
      line-height: 26px;
      box-sizing: border-box;
      word-wrap: break-word;
      table {
        border-collapse: collapse;
      }
    
      table,th,td {
        border: 1px solid #999;
        padding: 8px;
      }
    }
    
  }
  .custom-origin-tree{
    padding: 20px 10px !important;
    overflow: auto;

    .el-tree-node__content:hover {
      background-color: #D8EEFF !important;
      border-radius: 3px;
      color: #0096FF !important;
      .el-tree-node__expand-icon {
        color: #0096FF !important;
        font-size: 15px !important;
      }
    }

    .el-tree-node:focus > .el-tree-node__content {
      background-color: #D8EEFF !important;
    }

    .el-tree-node__expand-icon {
      color: #c8c8c8 !important;
      font-size: 15px !important;
    }

    .el-tree-node.is-current > .el-tree-node__content {
      color: #0096FF !important;
      background-color: #D8EEFF !important;
      .el-tree-node__expand-icon {
        color: #0096FF !important;
        font-size: 15px !important;
      }
    }
    .el-tree-node__content > .el-tree-node__expand-icon {
      padding-left: 6px !important;
    }
    
  }
  .custom-compare-tree{
    padding: 20px 10px !important;
    overflow: auto;

    .el-tree-node__content:hover {
      background-color: #D5E3FD !important;
      border-radius: 3px;
      color: $primary-color !important;
      .el-tree-node__expand-icon {
        color: $primary-color !important;
        font-size: 15px !important;
      }
    }

    .el-tree-node:focus > .el-tree-node__content {
      background-color: #D5E3FD !important;
    }

    .el-tree-node__expand-icon {
      color: #c8c8c8 !important;
      font-size: 15px !important;
    }

    .el-tree-node.is-current > .el-tree-node__content {
      color: $primary-color !important;
      background-color: #D5E3FD !important;
      .el-tree-node__expand-icon {
        color: $primary-color !important;
        font-size: 15px !important;
      }
    }
    .el-tree-node__content > .el-tree-node__expand-icon {
      padding-left: 6px !important;
    }
  }
  .custom-tree-node{
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    height: 40px;
    overflow: hidden;
    .custom-label{
      flex: 1;
      white-space: nowrap;
      overflow: hidden;//文本超出隐藏
      text-overflow: ellipsis;
      .iconfont{
        font-size: 16px;
       }
    }
  }
  
  .vs-btn{
    position: absolute;
    width: 86px;
    height: 86px;
    top: 320px;
    left: calc(50% - 43px);
    cursor: pointer;
  }
}