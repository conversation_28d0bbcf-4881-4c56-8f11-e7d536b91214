<template>
  <el-popover
    v-model="visible"
    popper-class="application-wrap"
    placement="bottom-start"
    trigger="hover"
    :teleported="false"
    :offset="21"
    :show-arrow="false"
    ref="applicationRef"
  >
    <div class="application-container scroller-bar-style">
      <div class="application-list">
        <div class="title-list">
          <div v-for="(item, index) in dataList" :key="index" @mouseenter="changeIndex(index)" :class="{actived: currentIndex == index}">{{ item.name }}</div>
        </div>
        <div class="line"></div>
        <div class="item-wrap">
          <div class="item-list">
            <div v-for="(row,i) in dataList[currentIndex].children" :key="i" class="item-row">
              <NuxtLink class="row-title" :to="row.path" @click.native.prevent="handleLink(row)">{{row.name}}</NuxtLink>
              <div class="row-desc">{{row.desc}}</div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <template #reference>
      <div class="flex">
        <div class="pointer ml-80">应用</div>
        <el-icon class="ml-5"><ArrowDown /></el-icon>
      </div>
    </template>
  </el-popover>
  
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/userStore'

const userStore = useUserStore()
const applicationRef = ref()
const visible = ref(false)
const currentIndex = ref(0)
const { $modal } = useNuxtApp()
const router = useRouter()

const dataList = [
  {
    name: '数据检索',
    children: [{
      name: '标准检索',
      desc: '涵盖国行地团企全量标准数据',
      path: '/retrieval/domestic',
    },{
      name: '国际国外标准检索',
      desc: '涵盖国际标准、国外标准数据',
      path: '/retrieval/internation',
    },{
      name: '全国TC目录检索',
      desc: '全国TC技术委员会目录及相关标准查询',
      path: '/retrieval/tc',
    },{
      name: '标准样品检索',
      desc: '国家标准样品目录清单检索查询',
      path: '/retrieval/sample',
    },{
      name: '标准专家检索',
      desc: '各个行业领域及TC专家在线检索',
      path: '/retrieval/expert',
    },{
      name: '标准公告检索',
      desc: '标准发布/废止公告及关联标准信息查询',
      path: '/retrieval/announcement',
    },{
      name: '法律法规检索',
      desc: '相关法律法规信息在线检索与数据查看',
      path: '/retrieval/law',
    },{
      name: '计划标准检索',
      desc: '国行地团计划标准项目信息公示查询',
      path: '/retrieval/plan',
    }]
  },
  {
    name: '应用工具',
    children: [{
      name: '标准比对',
      desc: '基于标准数字化内容的多维度差异化比对与标注',
      path: '/tools/comparison',
    }]
  },
  {
    name: '数据服务',
    children: [{
      name: '标准托管',
      desc: '标准在线托管与动态监测预警服务',
      path: '/data/trusteeship',
    },{
      name: '标准查新',
      desc: '快速批量检索标准状态与替代关系数据',
      path: '/data/search',
    },{
      name: '行业标准体系',
      desc: '针对各行业建立科学、规范、有效的标准体系',
      path: '/data/system',
    },{
      name: '标准专题',
      desc: '针对热门与热点，构建专业标准体系',
      path: '/data/special',
    },{
      name: '标准动态',
      desc: '实时跟踪标准发布、废止、替代动态',
      path: '/data/dynamic',
    }]
  },
  {
    name: '标准学院',
    children: [{
      name: '标准知识',
      desc: '名师在线深入解读标准知识',
      path: '/classroom/knowledge/introduce',
    },{
      name: '标准政策',
      desc: '涵盖全国各地方标准化政策与扶持信息',
      path: '/classroom/policy/search',
    }]
  },
  {
    name: '企业服务',
    children: [{
      name: '标准制修订服务',
      desc: '解决标准申报、制定过程中的问题和困难',
      path: '',
      type: 'amend',
    },{
      name: '标准战略分析',
      desc: '制定符合国家、行业最佳的标准化战略服务',
      path: '',
      type: 'strategy',
    },{
      name: '标准企业培训服务',
      desc: '提供符合国家、行业或企业的标准培训服务',
      path: '',
      type: 'train',
    },{
      name: '企业参与标准制修订服务',
      desc: '促进企业在标准制修订过程中的参与和贡献',
      path: '',
      type: 'involved',
    },{
      name: '企业标准监督抽查',
      desc: '建立健全标准化管理体系及产品合规性服务',
      path: '',
      type: 'check',
    },{
      name: '企业标准信息化建设服务',
      desc: '通过规范化、集成化手段提高企业管理水平',
      path: '',
      type: 'informatization',
    },{
      name: '企业标准”领跑者“创建服务',
      desc: '提高企业的标准化水平和竞争力',
      path: '',
      type: 'leader',
    },{
      name: '标准化示范试点建设服务',
      desc: '辅导企业申报试点，提升企业标准化能力',
      path: '',
      type: 'pilot',
    },{
      name: '企业标准体系建设服务',
      desc: '管理体系设计、实施、审核的全程专业服务',
      path: '',
      type: 'system',
    },{
      name: '企业标准良好行为建设',
      desc: '提供产品标准评价和标准质量水平提升服务',
      path: '',
      type: 'build',
    },{
      name: '企业标准自我声明指导服务',
      desc: '企业制定的标准进行评估和确认的服务',
      path: '',
      type: 'declaration',
    },{
      name: '标准有效性确认',
      desc: '标准状态判定和现行标准信息等相关信息',
      path: '',
      type: 'validity',
    }]
  }
]
const changeIndex = (index: number) => {
  currentIndex.value = index
}

const handleLink = (row: any) => {
  if(row.path == '') {
    router.push({
      path: `/enterprise/${row.type}-service`
    })
  }else{
    if(!row.path) {
      $modal.msgWarning('正在建设中，敬请期待')
    }
  }
  applicationRef.value.hide()
}

</script>
<style lang="scss">
.el-popover.el-popper.application-wrap {
  padding: 0px !important;
  margin: 0px !important;
  // width: 100vw !important;
  width: 100% !important;
  height: 360px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  // box-shadow: 0px 9px 9px 0px rgba(0, 0, 0, 0.08) !important;
  inset: 59px 0px auto 0px!important; 
  z-index: 9999 !important;

  .application-container {
    height: 360px;
    margin: 0 auto;
    width: 1200px;
    overflow-y: auto;
    .application-list {
      display: flex;
      .title-list{
        padding: 25px 0px;
        width: 160px;
        display: flex;
        flex-direction: column;
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        gap: 30px;
        div{
          cursor: pointer;
        }
      }
      .actived{
        color: $primary-color;
      }
      .line{
        width: 1px;
        height: 360px;
        background: #E8E8E8;
        margin: 0px 40px 0px 0px;
      }
      .item-wrap{
        flex: 1;
        display: flex;
        align-items: flex-start;
        .item-list{
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          .item-row{
            margin-top: 25px;
            flex: 0 0 33.33%;
            font-size: 14px;
            .row-title{
              color: #333333;
              font-weight: bold;
              &:hover{
                color: $primary-color;
                cursor: pointer;
              }
            }
            .row-desc{
              margin-top: 6px;
              color: #888888;
            }
          }
        }
      }
    }
  }
}
</style>