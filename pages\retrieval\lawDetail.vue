<template>
  <div class="container mt-60">
    <div class="main-wrap">
      <div class="f-18 c-33 f-bold text-center">{{ form.title }}</div>
      <div class="f-14 c-88 mt-10 text-center">发布日期：{{ form.publishDate }}</div>
      <el-divider />
      <ClientOnly>
        <div v-html="form.content" class="app-richtext"></div>
      </ClientOnly>
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :type="6" @updateData="getDetail" />
  </div>
</template>

<script setup lang="ts">
  import { getLawDetail } from '@/api/retrieval/law';
  import { type ILawInfo, type IResponseData } from '@/types';

  const route = useRoute();

  const form = ref<ILawInfo>({
    title: '',
    publishDate: '',
  });

  let { data } = <IResponseData>await getLawDetail(route.query.id as string | number);
  form.value = data || {};

  useHead({
    title: form.value.title,
  });

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/search/process/lawsRegulations/' + route.query.id);
    form.value = data || {};
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;
  }

  :deep(.el-divider--horizontal) {
    margin: 12px 0 30px !important;
  }
</style>
