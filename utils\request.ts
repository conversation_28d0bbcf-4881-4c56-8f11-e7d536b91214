import { type IResponseData } from "@/types";
import { useUserStore } from '@/store/userStore';

const httpService = {
  get: (url: string, params?: any, options?: object) => {
    // const queryParams = new URLSearchParams(params).toString();
    // let path = `${url}${queryParams? `?${queryParams}` : ''}`;

    return handleRequest(url, { method: 'GET', params, ...options, watch: false });
  },
  post: (url: string, body?: any, options?: object) => {
    return handleRequest(url, { method: 'POST', body, ...options, watch: false });
  },
  put: (url: string, body?: any, options?: object) => {
    return handleRequest(url, { method: 'PUT', body, ...options, watch: false });
  },
  delete: (url: string, params?: any, options?: object) => {
    return handleRequest(url, { method: 'DELETE', params, ...options, watch: false });
  },
};
const isPop401 = ref(false);
const handleRequest = async (url: string, options: object) => {
  const { public: { baseURL } } = useRuntimeConfig()
  const token = useUserStore().token
  let defaultOptions = {
    baseURL,
    // server: true, // 默认为true
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
      ...(token ? { Authorization: `Bearer ${token}` } : {})
    },
    timeout: 6000,
  }
  let optionMerge: object = {...defaultOptions,...options }

  return new Promise(async (resolve, reject) => {
    const { data, error, pending, status, refresh } = await useFetch(url, {
      ...optionMerge,
      onRequestError({ error }) {
        return reject(error || '服务器请求异常')
      },
      onResponseError({ response }) {
        return reject(response || '服务器返回异常')
      }
    });
    if(status.value != 'success') {
      if(import.meta.client){
        const { $modal } = useNuxtApp();
        $modal.msgError(`${error.value}` || '服务器请求异常')
      }
      
      return reject(error.value || '服务器请求异常');
    }

    if (error.value) {
      return reject(error.value);
    }
    if(typeof data.value == 'string'){
      data.value = JSON.parse(data.value)
    }
    const res  = data.value as IResponseData;
    const code = res.code || 500;
    const msg = res.message || '服务器错误';
    
    if(import.meta.server){
      return resolve(res);
    }else{ // 客户端处理
      const router = useRouter()
      const route = useRoute()
      const { $modal } = useNuxtApp();
      if(code == 401){
        if(isPop401.value) return;// 防止重复弹出
        isPop401.value = true;
        $modal.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示','重新登录').then(() => {
          useUserStore().logout().then(() => {
            location.href = `/login?redirect=${route.fullPath}`;
          });
        }).catch(() => {

        }).finally(() => {
          useUserStore().resetData()
          isPop401.value = false;
        });
        return reject(new Error(msg));
      }else if(code == 302){
        $modal.alertWarning(msg,'确定',false).then(() => {
          useUserStore().logout().then(() => {
            location.href = `/login?redirect=${route.fullPath}`;
          });
        }).catch(() => {

        });
        return reject(new Error(msg));
      }else if (code == 500){
        $modal.msgError(msg)
        return reject(new Error(msg));
      }else if (code == 303){
        router.push("/");
      }else if (code == 601) {
        $modal.msgWarning(msg)
        return reject(new Error(msg));
      } else if (code != 200) {
        $modal.notifyError(msg)
        return reject(new Error(msg));
      } else {
        return resolve(res);
      }
    }
  });
}

export default httpService;