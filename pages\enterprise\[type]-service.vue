<template>
  <div class="service-wrap mt-60">
    <div class="main-wrap">
      <div class="service-container">
        <div class="service-content">
          <div class="title">{{currentItem.name}}</div>
          <img  :src="imageUrl" alt="">
        </div>
        <div class="service-sidebar">
          <div class="side-apply" @click="handleApply">
            <div class="apply-btn">立即申请</div>
          </div>
          <div class="side-recommend">
            <div class="title">推荐服务</div>
            <ul v-if="dataList && dataList.length > 0" class="recommend-list">
              <li v-for="item in dataList" :key="item.type" @click="handleDetail(item)" class="recommend-item">
                <div class="item-title">{{item.name}}</div>
                <div class="item-desc">{{item.desc}}</div>
              </li>
            </ul>
            <BxcEmpty v-else class="mt-10" />
          </div>
        </div>
      </div>
    </div>
    <!-- 申请服务 -->
    <EnterprisePopApply v-if="open" v-model:open="open" :item="currentItem" />
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleApply"/>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from '@/store/userStore'

definePageMeta({
  middleware: [
    function (to, from) {
      if(!useEnterpriseService().isValidType((to.params.type || '') as string)){
        return navigateTo('/404')
      }
    }
  ],
});
const route = useRoute()
const { type } = route.params
const router = useRouter()
const userStore = useUserStore()
const open = ref(false)
const openLogin = ref(false)
const { setUseHead } = useSeo()
setUseHead(route.path)

const { serviceList,getServiceByType,isValidType } = useEnterpriseService()

const dataList = computed(() => {
  return serviceList.filter((item:any) => item.type!= type)
})
const currentItem = computed(() => {
  return getServiceByType(type as string)
})
const imageUrl = computed(() => {
  if(!type || !isValidType(type as string)){
    return ''
  }else{
    return `/enterprise/${type}.jpg`
  }
})
const handleApply = () => {
  if(!userStore.token){
    openLogin.value = true
    return
  }else{
    openLogin.value = false
    open.value = true
  }
}
const handleDetail = (item: any) => {
  router.push({
    path: `/enterprise/${item.type}-service`
  })
}
</script>
<style lang="scss" scoped>
.service-wrap{
  background: #F8F9FB;
  .service-container{
    padding: 20px 0px 40px 0px;
    display: flex;
    justify-content: space-between;
    gap: 15px;
    .service-content{
      background: #FFFFFF !important;
      flex: 1;
      padding: 30px;
      .title{
        font-weight: bold;
        font-size: 20px;
        color: #333333;
        text-align: center;
        margin-bottom: 20px;
      }
      img{
        width: 760px;
      }
    }
    .service-sidebar{
      width: 360px;
      flex-shrink: 0;
      .side-apply{
        background: url("@/assets/images/enterprise/side-bg.png") no-repeat center;
        background-size: 100% 100%;
        height: 80px;
        width: 100%;
        display: flex;
        align-items: center;
        padding-left: 30px;
        box-sizing: border-box;
        cursor: pointer;
        .apply-btn{
          width: 90px;
          height: 36px;
          background: #E9F1FF;
          border-radius: 3px;
          font-size: 16px;
          color: $primary-color;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .side-recommend{
        margin-top: 20px;
        background: #FFFFFF;
        .title{
          font-weight: bold;
          font-size: 18px;
          color: #FFFFFF;
          width: 100%;
          height: 55px;
          line-height: 55px;
          background: #F2A511;
          padding-left: 25px;
          box-sizing: border-box;
        }
        .recommend-list{
          margin-top: 5px;
          display: flex;
          flex-direction: column;
          padding: 0 25px;
          .recommend-item{
            height: 72px;
            border-bottom: 1px solid #E5E8EF;
            display: flex;
            flex-direction: column;
            font-size: 14px;
            justify-content: center;
            cursor: pointer;
            &:last-child{
              border-bottom: none;
            }
            &:hover{
              .item-title{
              color: $primary-color;
              }
            }
            .item-title{
              font-weight: bold;
              color: #333333;
            }
            .item-desc{
              margin-top: 10px;
              color: #888888;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}

</style>