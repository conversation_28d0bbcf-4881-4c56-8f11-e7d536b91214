<template>
  <div>
    <div class="f-20 f-bold">基础信息</div>
    <RetrievalDetailDescriptions :form="form" :data="basicInfo" class="mt-10" />
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);

  const basicInfo = computed(() => {
    let arr = [
      { label: '标准类型：', fieldName: 'standardTypeName' },
      { label: '标准状态：', fieldName: 'standardStatusName' },
      { label: '发布日期：', fieldName: 'publishDate' },
      { label: '实施日期：', fieldName: 'executeDate' },
      { label: 'CCS号：', fieldName: 'standardTypeCodeGbName' },
      { label: 'ICS号：', fieldName: 'standardTypeCodeIsoName' },
    ];
    // 废止 被替代
    if (form.value.repealDate || form.value.standardStatus == 3 || form.value.standardStatus == 4) {
      arr = arr.concat([{ label: '废止日期：', fieldName: 'repealDate' }]);
    }
    switch (Number(form.value.standardType)) {
      case 0:
        return (arr = arr.concat([
          { label: '制修订：', fieldName: 'amendName' },
          { label: '标准性质：', fieldName: 'standardAttrName' },
          { label: '标准类别：', fieldName: 'standardCategoryName' },
          { label: '归口单位：', fieldName: 'registryUnit' },
          { label: '执行单位：', fieldName: 'applyUnit' },
          { label: '主管部门：', fieldName: 'manageDept' },
        ]));
        break;
      case 1:
        return (arr = arr.concat([
          { label: '制修订：', fieldName: 'amendName' },
          { label: '标准性质：', fieldName: 'standardAttrName' },
          { label: '标准类别：', fieldName: 'standardCategoryName' },
          { label: '归口单位：', fieldName: 'registryUnit' },
          { label: '批准发布部门：', fieldName: 'confirmPublishDept' },
          { label: '行业分类：', fieldName: 'industryClassification' },
          { label: '备案日期：', fieldName: 'filingsDate' },
          { label: '备案号：', fieldName: 'filingsNumber' },
        ]));
        break;
      case 2:
        return (arr = arr.concat([
          { label: '制修订：', fieldName: 'amendName' },
          { label: '标准性质：', fieldName: 'standardAttrName' },
          { label: '标准类别：', fieldName: 'standardCategoryName' },
          { label: '归口单位：', fieldName: 'registryUnit' },
          { label: '批准发布部门：', fieldName: 'confirmPublishDept' },
          { label: '发布地区：', fieldName: 'address' },
          { label: '备案日期：', fieldName: 'filingsDate' },
          { label: '备案号：', fieldName: 'filingsNumber' },
        ]));
        break;
      case 3:
        return (arr = arr.concat([
          { label: '制修订：', fieldName: 'amendName' },
          { label: '标准性质：', fieldName: 'standardAttrName' },
          { label: '标准类别：', fieldName: 'standardCategoryName' },
          { label: '归口单位：', fieldName: 'registryUnit' },
          { label: '团体名称：', fieldName: 'associationName' },
          { label: '是否包含专利：', fieldName: 'isPatentInfoName' },
        ]));
        break;
      case 4:
        return (arr = arr.concat([
          { label: '制修订：', fieldName: 'amendName' },
          { label: '标准性质：', fieldName: 'standardAttrName' },
          { label: '标准类别：', fieldName: 'standardCategoryName' },
          { label: '归口单位：', fieldName: 'registryUnit' },
          { label: '发布单位：', fieldName: 'applyUnit' },
        ]));
        break;
      case 5:
        return (arr = arr.concat([
          { label: '发布组织：', fieldName: 'ctName' },
          { label: '标准语言：', fieldName: 'languageType' },
        ]));
        break;
      case 6:
        return (arr = arr.concat([
          { label: '发布组织：', fieldName: 'ctName' },
          { label: '标准语言：', fieldName: 'languageType' },
        ]));
        break;
      case 8:
        return (arr = arr.concat([
          { label: '归口单位：', fieldName: 'registryUnit' },
          { label: '发布单位：', fieldName: 'applyUnit' },
        ]));
        break;
      case 9:
        return (arr = arr.concat([
          { label: '归口单位：', fieldName: 'registryUnit' },
          { label: '发布单位：', fieldName: 'applyUnit' },
        ]));
        break;
      default:
        return arr;
        break;
    }
  });
</script>

<style lang="scss" scoped></style>
