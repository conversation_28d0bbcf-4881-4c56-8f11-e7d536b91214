<template>
  <div class="not-found-container">
    <img class="error-img" src="@/assets/images/layout/404.png" alt="">
    <div class="content">
      <div class="num">404</div>
      <div class="msg">很抱歉，您访问的页面走丢了</div>
      <div class="back-home" @click="handleBack">返回首页</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const handleBack = () => {
  navigateTo('/')
}
</script>
<style lang="scss" scoped>
.not-found-container {
  margin-top: 60px;
  padding: 100px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .error-img{
    width: 502px;
    img{
      width: 100%;
    }
  }
  .content{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    margin-left: 100px;
    .num{
      font-weight: bold;
      font-size: 68px;
      color: #333333;
    }
    .msg{
      margin-top: 20px;
      font-size: 22px;
      color: #666666;
    }
    .back-home{
      margin-top: 60px;
      width: 150px;
      height: 50px;
      background: $primary-color;
      border-radius: 25px;
      color: #FFFFFF;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

  }
}
</style>