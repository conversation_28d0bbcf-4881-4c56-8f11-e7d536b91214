<template>
  <div class="pop-container">
    <el-dialog :append-to-body="true" :lock-scroll="false" v-model="open" width="600px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
        <el-form-item label="需求说明" prop="demandDes">
          <el-input
            v-model="form.demandDes"
            type="textarea"
            :rows="5"
            placeholder="请输入需求说明信息"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="单位名称" prop="unitName">
          <el-input
            v-model="form.unitName"
            placeholder="请输入单位名称"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="联系人" prop="contacts">
          <el-input
            v-model="form.contacts"
            placeholder="请输入联系人名称"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactsNumber">
          <el-input
            v-model="form.contactsNumber"
            placeholder="请输入联系方式"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="form.address"
            placeholder="请输入联系地址"
            maxlength="50"
          />
        </el-form-item>

        <div class="tip-box mt-30">
          您也可以通过直接拨打我们的官方客服热线：<span class="mobile">************</span>，人工预约标准数字化管理系统演示。我们将安排专人为您服务。
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close" plain>关闭</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { addOrder } from '@/api/solution'
import { useUserStore } from '@/store/userStore'
import type { IUserInfo } from '@/types'

const userStore = useUserStore()
const { $modal } = useNuxtApp()
const title = ref('演示预约')
const formRef = ref()
const loading = ref(false)

const props = defineProps({
  open: Boolean,
});
const { open } = toRefs(props)

const userInfo = <IUserInfo>userStore.userInfo
const form = ref({
  appointmentProject: '0',
  demandDes: '',
  unitName: userInfo.unitName || '',
  contacts: userInfo.realName || '',
  contactsNumber: userStore.phonenumber || '',
  address: ''
});
const rules = ref({
  unitName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
  contacts: [
    { required: true, message: '请输入联系人名称', trigger: 'blur' },
    { min: 2, max: 20, message: "联系人名称长度必须介于 2 和 20 之间", trigger: "blur" },
    { pattern: realNamePattern, message: "联系人名称2-20个字母、汉字", trigger: "blur" },
  ],
  contactsNumber: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: telMobileValidPattern, message: '请输入正确的联系方式（手机号/座机号）', trigger: 'blur' },
  ],
});

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}

const save = () => {
  formRef?.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      addOrder(form.value).then(response => {
        emit('update:open',false)
        emit('success')
        $modal.msgSuccess("您的演示预约申请提交成功，我们将有专员和您联系！")
      }).finally(() => {
        loading.value = false
      })
    }
  })
}

</script>