<template>
  <el-dialog
    class="preview-wrap"
    append-to-body
    v-model="props.open"
    width="80%"
    title="查看"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <iframe :src="previewUrl" style="width: 100%; height: 75vh; border: none"></iframe>
  </el-dialog>
</template>

<script lang="ts" setup>
  const props = defineProps({
    open: Boolean,
    url: {
      type: String,
      default: '',
    },
  });

  const runtimeConfig = useRuntimeConfig();

  const previewUrl = computed(() => {
    return `${runtimeConfig.public.kkFileURL}?url=${encodeURIComponent(base64Encode(props.url))}`;
  });

  const close = () => {
    emit('update:open', false);
  };

  const emit = defineEmits(['update:open']);
</script>

<style lang="scss">
  .preview-wrap .el-dialog__body {
    padding: 0 !important;
  }
</style>
