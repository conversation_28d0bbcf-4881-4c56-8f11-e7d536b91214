<template>
  <div class="service-wrap main-wrap">
    <div class="h-title-wrap">
      <h3 class="title">企业服务</h3>
      <div class="more-btn" @click="handleMore()">
        <span>查看更多</span><el-icon><ArrowRight /></el-icon>
      </div>
    </div>
    <div class="service-container">
      <ul class="service-list">
        <li
          v-for="(item, index) in dataList"
          :key="index"
          class="service-item"
          :class="{ actived: index == currentIndex }"
          @click="handleChange(item, index)"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="service-content">
        <img :src="currentItem.img" alt="" />
        <div class="name">{{ currentItem.name }}</div>
        <div class="desc">{{ currentItem.desc }}</div>
        <div class="more" @click="handleMore(currentItem.type)">
          <span>了解更多</span><el-icon><Right /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import SERVICE1 from "@/assets/images/home/<USER>";
import SERVICE2 from "@/assets/images/home/<USER>";
import SERVICE3 from "@/assets/images/home/<USER>";
import SERVICE4 from "@/assets/images/home/<USER>";
import SERVICE5 from "@/assets/images/home/<USER>";
import SERVICE6 from "@/assets/images/home/<USER>";
import SERVICE7 from "@/assets/images/home/<USER>";
import SERVICE8 from "@/assets/images/home/<USER>";

interface Item {
  name: string;
  img: string;
  desc: string;
  type: string;
}

const currentItem = ref({} as Item);
const currentIndex = ref(0);

const dataList: Item[] = [
  {
    name: "标准制修订服务",
    img: SERVICE1,
    desc: "为帮助企业解决国家、行业、地方、团体、企业标准申报、制定过程中的问题和困难，对已有的标准进行修订和更新，以适应新的技术、市场、法律等环境的需要，面向广大用户提供标准修订制的咨询服务。",
    type: "amend",
  },
  {
    name: "标准有效性确认",
    img: SERVICE2,
    desc: "“标准有效性确认服务”是由长期从事标准信息研究和服务的专业技术人员，依据各类大型标准信息数据库、标准组织的官方网站和期刊等多种权威信息渠道，对标准有效性进行确认，提供标准状态判定和现行标准信息等相关信息，出具权威的有效性确认报告。",
    type: "validity",
  },
  {
    name: "企业标准“领跑者”服务",
    img: SERVICE3,
    desc: "企业标准“领跑者”项目，是由国家市场监督管理总局、发展和改革委员会、科学技术部、工业和信息化部、财政部、中国人民银行等八部委共同组织开展，旨在通过高水平标准创建，培育一批致力于高质量发展且具有创新能力的行业领军企业。针对企业标准化管理的专业创建指导服务，帮助企业建立和完善标准化管理体系，提高企业的标准化水平和竞争力。",
    type: "leader",
  },
  {
    name: "企业标准良好行为建设",
    img: SERVICE4,
    desc: "依托标准大数据资源，对上传的企业产品标准进行标准合规性、标准结构合理性以及标准技术先进性等方面的检查评价，并向用户提供企业产品标准检查评价报告和标准质量水平提升的修改建议。提供企业标准化良好行为评价全流程工作服务，获得标良证书不仅有利于企业项目招投标也是企业对外展示标准化工作综合实力的有效证明。",
    type: "build",
  },
  {
    name: "标准战略分析",
    img: SERVICE5,
    desc: "帮助企业制定符合国家和本行业最佳实践的标准化战略服务，包含但不局限于标准咨询、标准培训、标准实施、标准审核等。",
    type: "strategy",
  },
  {
    name: "企业标准监督抽查",
    img: SERVICE6,
    desc: "帮助企业建立健全标准化管理体系，保证产品、服务符合标准和法律法规要求的服务，包含但不局限于标准咨询、标准检测、标准培训、标准考核等。",
    type: "check",
  },
  {
    name: "企业标准自我声明指导服务",
    img: SERVICE7,
    desc: "通过标准适用性评价、标准实施效果评价以及标准修订建议等对企业制定的标准进行评估和确认的服务，确保标准的有效性和可行性，以便企业能够更好地实现其战略目标和业务目标。",
    type: "declaration",
  },
  {
    name: "标准化示范试点建设服务",
    img: SERVICE8,
    desc: "按照《国家级服务业标准化示范项目管理办法（试行）》、《社会管理和公共服务综合标准化试点细则（试行）》等文件要求，在试点申报、创建及验收工作中，辅导、协助客户企业申报试点，提升企业标准化工作能力和主体地位，加强标准体系建设，开展标准化理论知识和重要标准的宣贯与培训，提高标准化意识，培养标准化人才队伍，完成试点建设及验收任务。",
    type: "pilot",
  },
];
currentItem.value = dataList[0];

const handleChange = (item: any, index: number) => {
  currentItem.value = item;
  currentIndex.value = index;
};
const handleMore = (type?: any) => {
  if(type){
    navigateTo(`/enterprise/${type}-service`)
  }else{
    navigateTo(`/enterprise/service`)
  }

}

</script>
<style lang="scss" scoped>
.service-wrap {
  margin-top: 70px;
  .service-container {
    display: flex;
    .service-list {
      width: 300px;
      display: flex;
      padding: 25px 0px;
      flex-direction: column;
      background: #f8f9fa;
      .service-item {
        height: 60px;
        line-height: 60px;
        padding-left: 30px;
        font-size: 16px;
        cursor: pointer;
        &:hover {
          color: $primary-color;
        }
        &.actived {
          color: $primary-color;
          font-weight: bold;
          background: url("@/assets/images/home/<USER>") no-repeat
            center;
          background-size: 100% 100%;
        }
      }
    }
    .service-content {
      margin-left: 20px;
      background: #f8f9fa;
      flex: 1;
      .name {
        margin: 25px 40px 0px 40px;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
      }
      .desc {
        margin: 20px 40px 0px 40px;
        font-size: 14px;
        color: #333333;
        line-height: 24px;
      }
      .more {
        margin: 50px 40px 0px 40px;
        width: 120px;
        height: 34px;
        background: $primary-color;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        span {
          margin-right: 5px;
        }
        &:hover {
          background: $primary-hover-color !important;
        }
      }
    }
  }
}
</style>
