<template>
  <el-popover
    v-model="visible"
    popper-class="support-wrap"
    placement="bottom-start"
    trigger="hover"
    :teleported="false"
    :offset="21"
    :show-arrow="false"
    ref="supportRef"
  >
    <div class="support-container scroller-bar-style">
      <div class="support-list">
        <dl v-for="(item,index) in dataList" :key="index">
          <dt>{{item.name}}</dt>
          <dd v-for="(row, i) in item.children" :key="i">
            <NuxtLink class="row-title" :to="row.path" @click.native.prevent="handleLink(row)">{{row.name}}</NuxtLink>
          </dd>
        </dl>
        <dl>
          <dt>咨询服务</dt>
          <dd class="mt-10">
            <div class="f-20 c-33">************</div>
            <div class="f-14 c-33 mt-10">微信客服</div>
            <img class="wx-service" src="@/assets/images/layout/wx-service.png" alt="">
          </dd>
        </dl>
      </div>
    </div>
    <template #reference>
      <div class="flex">
        <div class="pointer ml-50">支持与服务</div>
        <el-icon class="ml-5"><ArrowDown /></el-icon>
      </div>
    </template>
    <!-- 需求咨询 -->
    <SupportPopConsult v-if="openConsult" v-model:open="openConsult" />
    <!-- 服务反馈 -->
    <SupportPopFeedback v-if="openFeedback" v-model:open="openFeedback" />
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleLink({type: 'feedback'})"/>
  </el-popover>
  
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/userStore'

const userStore = useUserStore()
const supportRef = ref()
const visible = ref(false)
const { $modal } = useNuxtApp()
const openConsult = ref(false)
const openFeedback = ref(false)
const openLogin = ref(false)

const dataList = [
  {
    name: '需求&反馈',
    children: [{
      name: '需求咨询',
      path: '',
      type: 'consult'
    },{
      name: '服务反馈',
      path: '',
      type: 'feedback'
    }]
  },
  {
    name: '信息公告',
    children: [{
      name: '官网公告',
      path: '/notice',
    }]
  },
  {
    name: '服务帮助',
    children: [{
      name: '使用指南',
      path: '/guide',
    },{
      name: '服务条款',
      path: '/clause/service',
    },{
      name: '隐私政策',
      path: '/clause/privacy',
    }]
  },
]
const handleLink = (row: any) => {
  if(row.type == 'consult'){
    openConsult.value = true
    return
  }
  if(row.type == 'feedback'){
    if(!userStore.token){
      openLogin.value = true
      return
    }else{
      openLogin.value = false
      openFeedback.value = true
      return
    }
  }

  if(!row.path) {
    $modal.msgWarning('正在建设中，敬请期待')
  }
  supportRef.value.hide()
}

</script>
<style lang="scss">
.el-popover.el-popper.support-wrap {
  padding: 0px !important;
  margin: 0px !important;
  width: 100% !important;
  height: 360px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  inset: 59px 0px auto 0px!important;
  z-index: 9999 !important;

  .support-container {
    height: 360px;
    margin: 0 auto;
    width: 1200px;
    overflow-y: auto;
    .support-list {
      margin-top: 20px;
      display: flex;
      dl{
        flex: 0 0 20%;
        dt{
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        dd{
          padding: 0;
          margin: 15px 0 0 0;
          font-size: 14px;
          color: #888888;
          .wx-service{
            width: 95px;
            height: 95px;
            margin-top: 10px;
          }
        }
        .row-title{
          color: #888888;
          &:hover{
            color: $primary-color;
            cursor: pointer;
          }
        }
      }
    }
  }
  
}
</style>