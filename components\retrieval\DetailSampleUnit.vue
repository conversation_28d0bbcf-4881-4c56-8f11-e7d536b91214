<template>
  <div>
    <div class="flex flex-sb flex-ai-center">
      <div class="f-20 f-bold">研/复单位</div>
      <div v-if="props.data && props.data.length > 6" class="flex flex-column flex-sa flex-ai-center pointer">
        <span @click="handClick" class="iconfont c-88" :class="portion ? 'icon-xiala' : 'icon-shangla'"></span>
      </div>
    </div>
    <div class="unit mt-20">
      <div v-for="(item, index) in list" :key="index" class="unit-label">
        <img src="@/assets/images/retrieval/unit.png" class="unit-label-icon" alt="" />
        <RetrievalToolTip :text="item" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    data: {
      required: true,
      type: Array as () => string[],
    },
  });

  const portion = ref(true);

  let list = computed(() => {
    return portion.value ? props.data.slice(0, 6) : props.data;
  });

  const handClick = () => {
    portion.value = !portion.value;
  };
</script>

<style lang="scss" scoped>
  .unit {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0 10px;

    &-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333333;
      margin-bottom: 20px;
      min-width: 0;
      max-width: 100%;

      &-icon {
        width: 30px;
        height: 30px;
      }
    }
  }
</style>
