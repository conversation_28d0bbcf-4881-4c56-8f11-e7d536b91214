<template>
  <div class="container mt-60">
    <div class="main-wrap">
      <div class="f-18 c-33 f-bold text-center">
        {{ form.title }} &nbsp;
        <span>{{ form.noticeCode }}</span>
      </div>
      <div class="flex flex-center f-14 c-88 mt-10">
        <div class="mr-60">发布日期：{{ form.publishDate || '-' }}</div>
        <div>发布单位：{{ form.publishingUnit || '-' }}</div>
      </div>
      <el-divider />
      <div v-html="form.content" class="app-richtext"></div>
      <template v-if="form.noticeFilesList && form.noticeFilesList.length > 0">
        <div class="f-20 f-bold mt-40">公告文件</div>
        <div class="flex flex-wrap">
          <div
            @click="handlePreview(item)"
            v-for="(item, index) in form.noticeFilesList"
            :key="index"
            class="f-14 mt-20 mr-10 jump-link c-primary"
          >
            公告文件{{ index + 1 }}
          </div>
        </div>
      </template>
      <template v-if="tableData && tableData.length > 0">
        <div id="announcementId" class="f-20 f-bold mt-40">公告标准</div>
        <el-table :data="tableData" class="mt-20">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column type="index" label="序号" width="80">
            <template #default="{ $index }">
              {{ (query.pageNum - 1) * query.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="标准号" min-width="150">
            <template #default="{ row }">
              <span
                @click="handleClick('/retrieval/domesticDetail', { id: row.standardId })"
                :class="row.standardId ? 'c-primary pointer' : ''"
              >
                {{ row.standardCode }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="standardName" show-overflow-tooltip label="标准名称" />
          <el-table-column prop="allReplaceStandardCode" show-overflow-tooltip label="代替标准号" />
          <el-table-column prop="executeDate" show-overflow-tooltip label="实施日期" />
          <template v-if="form.noticeType != 0">
            <el-table-column prop="filingsNumber" show-overflow-tooltip label="备案号" />
            <el-table-column prop="issueDate" show-overflow-tooltip label="批准日期" />
            <el-table-column prop="manageDept" show-overflow-tooltip label="主管部门" />
          </template>
        </el-table>
        <BxcPagination
          v-show="tableTotal"
          v-model:page="query.pageNum"
          v-model:limit="query.pageSize"
          :total="tableTotal"
          :anchorPoint="'announcementId'"
          @pagination="getStandardData"
          class="mt-30"
        />
      </template>
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :type="5" @updateData="getDetail" />
    <BxcPreviewFile v-if="open" v-model:open="open" :url="url" />
  </div>
</template>

<script setup lang="ts">
  import { getAnnouncementDetail, getAnnouncementStandard } from '@/api/retrieval/announcement';
  import { handleJump } from '@/utils/common';
  import { type IStandardInfo, type IAnnouncementInfo, type IResponseData } from '@/types';

  const route = useRoute();
  const { $modal } = useNuxtApp();

  const open = ref(false);
  const url = ref('');
  const form = ref<IAnnouncementInfo>({
    title: '',
    noticeCode: '',
    noticeTypeName: '',
    stdNum: 0,
    publishDate: '',
    publishingUnit: '',
  });
  const query = ref({
    id: route.query.id,
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref<IStandardInfo[]>([]);
  const tableTotal = ref(0);

  let { data } = <IResponseData>await getAnnouncementDetail(route.query.id as string | number);
  data.noticeFilesList = data.noticeFiles ? splitStrToArray(data.noticeFiles, ',') : [];
  form.value = data || {};

  let { rows, total } = <IResponseData>await getAnnouncementStandard(query.value);
  tableData.value = rows || [];
  tableTotal.value = total || 0;

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/search/process/stdNotice/' + route.query.id);
    data.noticeFilesList = data.noticeFiles ? splitStrToArray(data.noticeFiles, ',') : [];
    form.value = data || {};
  };

  const getStandardData = async () => {
    let { rows, total } = <IResponseData>await useHttp.get('/search/process/stdNotice/getNoticeStdList', query.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleClick = (url: string, query?: Record<string, string>) => {
    if (query?.id) {
      handleJump(url, query);
    } else {
      $modal.msgWarning('该标准暂未收录！');
    }
  };

  const handlePreview = (row: string) => {
    url.value = row;
    open.value = true;
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;
  }

  :deep(.el-divider--horizontal) {
    margin: 12px 0 30px !important;
  }
</style>
