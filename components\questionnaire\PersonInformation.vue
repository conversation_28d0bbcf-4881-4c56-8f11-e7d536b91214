<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="mt-25 form-inline">
      <div class="h-title mt-10 mb-20 one-column">主导参与制修订标准</div>
      <div v-if="form.leadStandardList && form.leadStandardList.length < 20" class="flex-1 flex flex-jc-end">
        <div
          @click="handleClick('leadStandardList', 'add', '', '', '新增主导参与制修订标准')"
          class="flex flex-ai-center f-14 c-primary pointer"
        >
          <el-icon class="f-14 mr-5"><Plus /></el-icon>
          新增
        </div>
      </div>
      <el-form-item class="one-column">
        <el-table :data="form.leadStandardList" class="mt-15">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column label="标准号" prop="standardCode" min-width="200" show-overflow-tooltip />
          <el-table-column label="标准名称" prop="standardName" min-width="150" show-overflow-tooltip />
          <el-table-column label="标准类型" prop="standardTypeName" min-width="150" show-overflow-tooltip />
          <el-table-column label="发布年份" prop="publishDate" min-width="150" show-overflow-tooltip />
          <el-table-column label="起草单位排名" prop="draftingUnitRankingName" min-width="150" show-overflow-tooltip />
          <el-table-column label="标准状态" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span :class="getStatusColor(standardStatusToString(row.standardStatus), 'text')">
                {{ row.standardStatusName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="采标情况" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span :class="getStatusColor(procurementStatusToString(row.procurementStatus), 'text')">
                {{ row.procurementStatusName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="是否在标准信息公共服务平台公示"
            prop="isPubliclyDisplayedName"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column label="操作" show-overflow-tooltip min-width="150">
            <template #default="{ row }">
              <el-button
                @click="handleClick('leadStandardList', 'edit', row, $index, '编辑主导参与制修订标准')"
                type="primary"
                link
              >
                编辑
              </el-button>
              <el-button @click="handleClick('leadStandardList', 'delete', row, $index)" type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <div class="h-title mt-10 mb-20 one-column">主要执行标准</div>
      <div v-if="form.executionStandardList && form.executionStandardList.length < 20" class="flex-1 flex flex-jc-end">
        <div
          @click="handleClick('executionStandardList', 'add', '', '', '新增主要执行标准')"
          class="flex flex-ai-center f-14 c-primary pointer"
        >
          <el-icon class="f-14 mr-5"><Plus /></el-icon>
          新增
        </div>
      </div>
      <el-form-item class="one-column">
        <el-table :data="form.executionStandardList" class="mt-15">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column label="标准号" prop="standardCode" min-width="200" show-overflow-tooltip />
          <el-table-column label="标准名称" prop="standardName" min-width="150" show-overflow-tooltip />
          <el-table-column label="标准类型" prop="standardTypeName" min-width="150" show-overflow-tooltip />
          <el-table-column label="发布年份" prop="publishDate" min-width="150" show-overflow-tooltip />
          <el-table-column label="起草单位排名" prop="draftingUnitRankingName" min-width="150" show-overflow-tooltip />
          <el-table-column label="标准状态" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span :class="getStatusColor(standardStatusToString(row.standardStatus), 'text')">
                {{ row.standardStatusName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="采标情况" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span :class="getStatusColor(procurementStatusToString(row.procurementStatus), 'text')">
                {{ row.procurementStatusName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="是否在标准信息公共服务平台公示"
            prop="isPubliclyDisplayedName"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column label="操作" show-overflow-tooltip min-width="150">
            <template #default="{ row }">
              <el-button
                @click="handleClick('executionStandardList', 'edit', row, $index, '编辑主要执行标准')"
                type="primary"
                link
              >
                编辑
              </el-button>
              <el-button @click="handleClick('executionStandardList', 'delete', row, $index)" type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <div class="h-title mt-10 mb-20 one-column">填表人信息</div>
      <div class="one-column-content">
        <el-form-item label="填表人" prop="fillingPerson">
          <el-input v-model="form.fillingPerson" placeholder="请输入姓名" maxlength="50" />
        </el-form-item>
        <el-form-item label="联系电话" prop="fillingPersonContact">
          <el-input v-model="form.fillingPersonContact" placeholder="请输入联系电话" maxlength="100" />
        </el-form-item>
        <el-form-item label="验证码" prop="verificationCode">
          <el-input v-model="form.verificationCode" placeholder="请输入验证码" maxlength="4">
            <template #append>
              <div v-if="!isDisabled" @click="sendCode">发送验证码</div>
              <div v-else>{{ time }}s 后重新获取</div>
            </template>
          </el-input>
        </el-form-item>
      </div>
    </el-form>
    <div class="btn">
      <el-button @click="previousPage" :loading="loading" plain>上一页</el-button>
      <el-button @click="handleSubmit" :loading="loading" type="primary">提交</el-button>
    </div>
    <QuestionnaireRevisionStandardDialog
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      v-model:data="currentRow"
      v-model:title="dialogTitle"
      @updateData="updateData"
    />
  </div>
</template>

<script setup lang="ts">
  import { checkValidity } from '@/api/questionnaire';
  import { getCode } from '@/api/login';
  import { getStatusColor } from '@/utils/common';

  const { $modal } = useNuxtApp();
  const route = useRoute();
  const router = useRouter();

  interface FormValue {
    fillingPerson: string;
    fillingPersonContact: string;
    verificationCode: string;
    leadStandardList: any[];
    executionStandardList: any[];
  }

  const formRef = ref();
  const loading = ref(false);
  const form = ref<FormValue>({
    fillingPerson: '',
    fillingPersonContact: '',
    verificationCode: '',
    leadStandardList: [],
    executionStandardList: [],
  });
  const currentFieldName = ref<keyof FormValue>();
  const currentRow = ref<any>({});
  const currentIndex = ref();
  const dialogTitle = ref<string>('');
  const dialogVisible = ref(false);
  const time = ref(60);
  const isDisabled = ref(false);
  const rules = ref({
    fillingPerson: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '姓名长度必须介于 2 和 20 之间', trigger: 'blur' },
      { pattern: realNamePattern, message: '姓名2-20个字母、汉字', trigger: 'blur' },
    ],
    fillingPersonContact: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: mobileValidPattern, message: '请输入正确的联系电话', trigger: 'blur' },
    ],
    verificationCode: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' },
    ],
  });

  const standardStatusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const procurementStatusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'red';
        break;
      case 1:
        return 'green';
        break;
      default:
        return 'green';
        break;
    }
  };

  const handleClick = (fieldName: keyof FormValue, type: string, row?: any, index?: any, title: string = '') => {
    currentFieldName.value = fieldName;
    switch (type) {
      case 'add':
        currentRow.value = {};
        currentIndex.value = '';
        dialogTitle.value = title;
        dialogVisible.value = true;
        break;
      case 'edit':
        currentRow.value = row;
        currentIndex.value = index;
        dialogTitle.value = title;
        dialogVisible.value = true;
        break;
      case 'delete':
        (form.value[fieldName] as any[]).splice(index, 1);
        break;
      default:
        break;
    }
  };

  const updateData = (data: any) => {
    if (currentRow.value.standardCode && currentFieldName.value) {
      (form.value[currentFieldName.value] as any[]).splice(currentIndex.value, 1, data);
    } else {
      if (currentFieldName.value) (form.value[currentFieldName.value] as any[]).push(data);
    }
  };

  const sendCode = () => {
    formRef?.value.validateField('fillingPersonContact', (valid: boolean) => {
      if (valid) {
        getCode({ phonenumber: form.value.fillingPersonContact, checkType: 7 }).then(res => {
          isDisabled.value = true;
          time.value = 60;
          const interval = setInterval(() => {
            time.value--;
            if (time.value <= 0) {
              clearInterval(interval);
              isDisabled.value = false;
              time.value = 60;
            }
          }, 1000);
        });
      }
    });
  };

  const previousPage = () => {
    checkValidity(route.query.id as string | number).then((res: any) => {
      if (res.data) {
        emit('handleBack', 3);
      } else {
        $modal.msgError(res.message);
        router.replace('/questionnaire');
      }
    });
  };

  const handleSubmit = () => {
    loading.value = true;
    formRef?.value.validate((valid: boolean) => {
      if (valid) {
        emit('submitData', form.value);
      }
      loading.value = false;
    });
  };

  const emit = defineEmits(['submitData', 'handleBack']);
</script>

<style lang="scss" scoped>
  .form-inline {
    color: #333 !important;

    .half-column {
      width: 48% !important;
    }

    .one-column {
      width: 100% !important;

      &-content {
        width: 100%;
        display: flex;
        justify-content: space-between;

        :deep(.el-form-item) {
          width: calc(96% / 3);
        }

        :deep(.el-input) {
          width: 100% !important;
        }
      }
    }
  }

  :deep(.el-input) {
    height: 36px !important;
  }

  :deep(.el-input-group__append) {
    cursor: pointer;
    color: #fff !important;
    background-color: $primary-color !important;
  }

  .btn {
    display: flex;
    justify-content: center;
    margin: 40px auto 60px !important;

    :deep(.el-button) {
      width: 180px !important;
      height: 42px !important;
      border-radius: 3px !important;
      border-color: $primary-color !important;
    }

    :deep(.el-button + .el-button) {
      margin-left: 40px !important;
    }

    :deep(.el-button:hover) {
      background-color: $primary-color !important;
    }

    :deep(.el-button.is-plain) {
      color: $primary-color !important;
    }

    :deep(.el-button.is-plain:hover) {
      background-color: #fff !important;
    }
  }

  :deep(.el-input-group__append) {
    width: 100px !important;
    box-shadow: none !important;
  }
</style>
