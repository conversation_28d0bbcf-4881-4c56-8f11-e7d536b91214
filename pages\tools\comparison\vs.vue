<template>
  <div class="comparison-wrap">
    <div class="comparison-header">
      <div class="comparison-header-container main-wrap">
        <div class="title">标准比对 </div>
        <div class="comparison-choice-box">
          <div class="origin-box" @click="handleSelectStandard('origin')">
            <div class="tag origin-tag">原始标准</div>
            <div v-if="originInfo.id" class="s-name">{{ originInfo.standardName }}</div>
            <div v-else class="s-name">请选择原始标准</div>
          </div>
          <div class="change-btn" @click="handleExchange">
            <img src="@/assets/images/tools/comparison/change-icon.png" alt="" srcset="">
          </div>
          <div class="compare-box" @click="handleSelectStandard('compare')">
            <div class="tag compare-tag">比对标准</div>
            <div v-if="compareInfo.id" class="s-name">{{ compareInfo.standardName }}</div>
            <div v-else class="s-name">请选择比对标准</div>
          </div>
        </div>
      </div>
    </div>
    <div class="comparison-container">
      <div class="comparison-tabs">
        <ul class="tab-list">
          <li v-for="item in tabList" :key="item.id" @click="handleTabClick(item)" class="tab-item" :class="{'active-item':`${currentTabId}` == `${item.id}` }">{{ item.name }}</li>
        </ul>
        <div class="op-box main-wrap">
          <div class="op-left">
            <div class="op-item op-edit">
              <div class="tip-icon"></div>
              <div class="tip-text">修改</div>
            </div>
            <div class="op-item op-delete">
              <div class="tip-icon"></div>
              <div class="tip-text">删除</div>
            </div>
            <div class="op-item op-add">
              <div class="tip-icon"></div>
              <div class="tip-text">新增</div>
            </div>
          </div>
          <div class="op-right">
            <div class="op-item op-sync">
              <el-switch
                v-model="syncScroll"
                inline-prompt
                active-text="是"
                inactive-text="否"
              />
              <div class="tip-text">同步滚动</div>
            </div>
            <!-- <div class="op-item pointer">
              <i class="iconfont icon-xiazaimoban c-primary f-20"></i>
              <div class="tip-text">下载比对报告</div>
            </div> -->
            <div v-if="[1,2].includes(currentTabId)" @click="handleResult" class="op-item pointer">
              <i class="iconfont icon-bidui c-primary f-20"></i>
              <div class="tip-text">比对清单</div>
            </div>
          </div>
        </div>
      </div>
      <div class="comparison-tabs-content">
        <ToolsComparisonArticle v-if="currentTabId == 1" :syncScroll="syncScroll" />
        <ToolsComparisonFullText v-if="currentTabId == 2" :syncScroll="syncScroll" />
        <ToolsComparisonCatalog v-if="currentTabId == 3" :syncScroll="syncScroll" />
      </div>
    </div>
   
   <!-- 弹框-选择标准 -->
   <ToolsComparisonPopSelectStandard v-if="openSelectStandard" v-model:open="openSelectStandard" :starndardInfo="currentStandardInfo" :selectedIds="selectedIds" @selected="handleSelected" />
   <!-- 弹框-比对清单 -->
   <ToolsComparisonResultDrawer v-if="openResult"  v-model:open="openResult" />
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from '@/store/userStore'
import type { IStandardInfo } from '@/types'
import { useComparisonStore } from '@/store/comparisonStore'

definePageMeta({
  middleware: [
    function (to, from) {
      const { token } = useUserStore()
      if(!token){
        return navigateTo('/tools/comparison')
      }
    }
  ],
});
const { setUseHead } = useSeo()
setUseHead('/tools/comparison')

const openSelectStandard = ref(false)
const openResult = ref(false)
const syncScroll = ref(false)
const { $modal } = useNuxtApp();
const maxPage: number = 100

const currentStandardInfo = ref(<IStandardInfo>{})
const selectedIds = ref([])
const comparisonStore = useComparisonStore()

const { compareType, tabList, originInfo, compareInfo, currentTabId, isVsing } = storeToRefs(comparisonStore)

const handleExchange = () => {
  comparisonStore.exchangeInfo()
}

const handleSelectStandard = (type: string) => {
  openSelectStandard.value = true
  compareType.value = type
  if(type == 'origin'){
    currentStandardInfo.value = originInfo.value
    if(compareInfo.value.id){
      selectedIds.value = compareInfo.value.id && [compareInfo.value.id] 
    }
  }else{
    currentStandardInfo.value = compareInfo.value
    if(originInfo.value.id){
      selectedIds.value = originInfo.value.id && [originInfo.value.id]
    }
  }
}

const handleSelected = (info: any) => {
  if(info.standardTextPages > maxPage && currentTabId.value == 2){
    $modal.msgWarning("该标准文件超过100页，不可使用全文比对！")
    return
  }
  if(compareType.value == 'origin'){
    if(info.id == originInfo.value.id){
      return
    }
    originInfo.value = info
  }else{
    if(info.id == compareInfo.value.id){
      return
    }
    compareInfo.value = info
  } 

  if(currentTabId.value == 1){
    if(isVsing.value){
      comparisonStore.getArticleTree(originInfo.value.id, 'origin')
      comparisonStore.getArticleTree(compareInfo.value.id, 'compare')
    }else{
      if(compareType.value == 'origin'){
        comparisonStore.getArticleTree(originInfo.value.id, 'origin') 
      }else{
        comparisonStore.getArticleTree(compareInfo.value.id, 'compare')
      }
    }
  }else if(currentTabId.value == 2){
    if(isVsing.value){
      comparisonStore.getFullTextTree(originInfo.value.id, 'origin')
      comparisonStore.getFullTextTree(compareInfo.value.id, 'compare')
    }else{
      if(compareType.value == 'origin'){
        comparisonStore.getFullTextTree(originInfo.value.id, 'origin') 
      }else{
        comparisonStore.getFullTextTree(compareInfo.value.id, 'compare')
      }
    }
  }else if(currentTabId.value == 3){
    comparisonStore.getCatalogTree(info.id, compareType.value) 
  }
}

const handleTabClick = (item: any) => {
  if([4,5,6].includes(item.id)){
    $modal.msgWarning("该功能暂未开放,敬请期待!") 
    return
  }
  if([2].includes(item.id)){
    if(originInfo.value.standardTextPages > maxPage && compareInfo.value.standardTextPages > maxPage){
      $modal.msgWarning("原始标准和比对标准文件超过100页，不可使用全文比对！")
      return 
    }
    if(originInfo.value.standardTextPages > maxPage){
      $modal.msgWarning("原始标准文件超过100页，不可使用全文比对！")
      return
    }
    if(compareInfo.value.standardTextPages > maxPage){
      $modal.msgWarning("比对标准文件超过100页，不可使用全文比对！")
      return 
    }
  }
  comparisonStore.resetInitInfo()
  currentTabId.value = item.id

  comparisonStore.setTabClick()
}

const handleResult = () => {
  if(!isVsing.value){
    $modal.msgWarning("请先进行比对！")
    return
  }
  openResult.value = true
}
</script>
<style lang="scss" scoped>
.comparison-header {
  margin-top: 60px;
  height: 235px;
  background: #F8F9FB;
  .comparison-header-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #333333;
    .title {
      font-weight: bold;
      font-size: 26px;
    }
    .comparison-choice-box{
      margin-top: 40px;
      display: flex;
      .origin-box{
        width: 526px;
        height: 80px;
        background: #FFFFFF;
        border-radius: 5px;
        border: 1px solid #DFDFDF;
        cursor: pointer;
        position: relative;
        .origin-tag{
          background: url("@/assets/images/tools/comparison/origin-bg.png") no-repeat left top;
        }
      }
      .change-btn{
        margin: 0px 25px;
        width: 80px;
        cursor: pointer;
        image{
          width: 80px;
          height: 76px;
        }
      }
      .compare-box{
        width: 526px;
        height: 80px;
        background: #FFFFFF;
        border-radius: 5px;
        border: 1px solid #DFDFDF;
        cursor: pointer;
        position: relative;
        .compare-tag{
          background: url("@/assets/images/tools/comparison/compare-bg.png") no-repeat left top;
        }
      }
      .tag{
        width: 89px;
        height: 43px;
        position: absolute;
        top: -1px;
        left: -10px;
        font-weight: bold;
        font-size: 14px;
        color: #FFFFFF;
        text-align: center;
        line-height: 32px;
      }
      .s-name{
        padding: 46px 30px 0px 30px;
        font-weight: bold;
        font-size: 14px;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.comparison-container {
  .comparison-tabs{
    .tab-list{
      height: 75px;
      line-height: 75px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom: 1px solid #E8E8E8;
      gap: 0px 55px;
      box-sizing: border-box;
      .tab-item{
        height: 100%;
        font-size: 16px;
        color: #333333;
        cursor: pointer;
      }
      .active-item{
        font-weight: bold;
        color: $primary-color;
        position: relative;
        &::after{
          content: '';
          display: block;
          width: 100%;
          height: 3px;
          position: absolute;
          bottom: 0px;
          left: 0px;
          background: $primary-color;
          border-radius: 2px;
        }
      }
    }
    .op-box{
      height: 65px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .op-left{
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 0px 25px;
        .op-item{
          display: flex;
          align-items: center;
          gap: 0px 10px;
          .tip-icon{
            width: 30px;
            height: 4px;
          }
          .tip-text{                
            font-size: 14px;
          }
        }
        .op-edit{
          .tip-icon{
            background: #FFB400;
          }
          .tip-text{                
            font-size: 14px;
            color: #FFB400;
          }
        }
        .op-delete{
          .tip-icon{
            background: #FF0000;
          }
          .tip-text{                
            font-size: 14px;
            color: #FF0000;
          }
        }
        .op-add{
          .tip-icon{
            background: #00B42A;
          }
          .tip-text{                
            font-size: 14px;
            color: #00B42A;
          }
        }
      }
     .op-right{
        flex: 1;
        display: flex;
        justify-content: flex-end; 
        align-items: center;
        gap: 0px 25px;
        .op-item{
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 0px 10px;
          .tip-text{                
            font-size: 14px;
            color: #333333;
          }
        }
     }
    }
  }
}

</style>