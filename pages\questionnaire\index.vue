<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="form.fillingTheme"
                maxlength="100"
                placeholder="输入填报主题搜索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
        <el-form ref="formRef" :model="form" inline class="form-inline mt-30" label-width="auto" size="large">
          <el-form-item prop="fillingRegion" label="填报区域">
            <el-cascader
              @visible-change="e => handleCascader(e, 'fillingRegion')"
              @clear="handleClearCascader"
              @remove-tag="handleClearCascader"
              v-model="form.fillingRegion"
              clearable
              filterable
              collapse-tags
              :show-all-levels="false"
              :options="regionOptions"
              :props="{
                checkStrictly: true,
                emitPath: false,
                label: 'name',
                value: 'code',
                children: 'childRegion',
              }"
              placeholder="请选择填报区域"
              class="cascader-fold"
            ></el-cascader>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <template v-if="tableData && tableData.length > 0">
          <div v-for="item in tableData" :key="item.id" class="card">
            <div class="flex flex-ai-center overflow-ellipsis">
              <div class="card-title overflow-ellipsis">{{ item.fillingTheme }}</div>
              <div :class="getStatusColor(statusToString(item.fillingStatus))">{{ item.fillingStatusName }}</div>
            </div>
            <div class="card-date overflow-ellipsis">填报时间段：{{ item.fillingStartDate + ' 至 ' + item.fillingEndDate }}</div>
            <div class="card-info">
              <div class="card-info-item overflow-ellipsis">发起部门：{{ item.initiatingDepartment || '-' }}</div>
              <div class="card-info-item overflow-ellipsis">填报区域：{{ item.fullRegionName || '-' }}</div>
              <div class="card-info-item overflow-ellipsis">填报对象：{{ item.fillingObject || '-' }}</div>
            </div>
            <div v-if="item.fillingStatus == '0'" @click="toDetail(item)" class="card-btn">开始填报</div>
          </div>
        </template>
        <BxcEmpty v-else />
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
          class="mt-30"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { getQuestionnaireList, checkValidity } from '@/api/questionnaire';
  import { getDistrictList } from '@/api/common';
  import { type IFillInInfo, type IResponseData } from '@/types';
  import type { FormInstance } from 'element-plus';
  import { handleJump } from '@/utils/common';

  const { $modal } = useNuxtApp();

  interface IForm {
    fillingTheme?: string;
    pageNum: number;
    pageSize: number;
    fillingRegion?: string | number;
  }

  const formRef = ref<FormInstance>();
  const regionOptions = ref([]);
  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref<IFillInInfo[]>([]);
  const tableTotal = ref(0);

  let RegionRes = <IResponseData>await getDistrictList();
  regionOptions.value = RegionRes.data;

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'blue';
        break;
      case 1:
        return 'orange';
        break;
      case 2:
        return 'gray';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    let { rows, total } = <IResponseData>await getQuestionnaireList(form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  await getData();

  const handleReset = () => {
    if (formRef.value) formRef.value.resetFields();
    form.value.fillingTheme = '';
    getData('pageNum');
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(300);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    form.value.fillingTheme = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    form.value.fillingTheme = form.value.fillingTheme?.replace(/^\s+|\s+$/g, '');
    if (form.value.fillingTheme) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: form.value.fillingTheme,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };

  let orginalForm = JSON.parse(JSON.stringify(form.value));
  const handleComparison = (fieldName: keyof typeof form.value) => {
    if (fieldName) {
      if (form.value[fieldName] != orginalForm[fieldName]) {
        getData('pageNum');
        orginalForm = JSON.parse(JSON.stringify(form.value));
      }
    }
  };

  const handleCascader = (event: any, fieldName: keyof typeof form.value) => {
    if (!event) handleComparison(fieldName);
  };

  const handleClearCascader = () => {
    nextTick(() => {
      getData('pageNum');
    });
  };

  const toDetail = (row: any) => {
    checkValidity(row.id).then((res: any) => {
      if (res.data) {
        handleJump('/questionnaire/fillIn', { id: row.id });
      } else {
        $modal.msgError(res.message);
        handleSearch();
      }
    });
  };
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    :deep(.el-input) {
      width: 95% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 20px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-reset {
      position: absolute;
      right: -40px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }

  .card {
    border-radius: 3px;
    margin-bottom: 30px;
    padding: 20px;
    box-sizing: border-box;
    border: 1px solid #e8e8e8;
    position: relative;
    border-radius: 3px;

    &:first-child {
      margin-top: 15px;
    }

    &:hover {
      box-shadow: 0px 0px 8px 1px rgba(0, 0, 0, 0.09);

      .card-title {
        color: $primary-color !important;
      }
    }

    &-title {
      font-size: 16px;
      color: #333;
      font-weight: bold;
      margin-right: 15px;
    }

    &-date {
      font-size: 14px;
      color: #333;
      margin-top: 15px;
    }

    &-info {
      width: 100%;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: #333;
      margin-top: 15px;

      &-item {
        width: calc(98% / 3);
      }
    }

    &-btn {
      margin-top: 15px;
      width: 84px;
      height: 34px;
      text-align: center;
      line-height: 34px;
      background: $primary-color;
      border-radius: 3px;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
    }
  }
</style>
