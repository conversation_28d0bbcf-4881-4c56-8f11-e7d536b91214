<template>
  <div class="banner-search-wrap">
    <div class="banner-search-container main-wrap">
      <div class="search-title">全量标准数据<span>AI标准图谱</span>专业标准工具平台</div>
      <ul class="search-list">
        <li v-for="(item,index) in searchList" :key="item.type" class="search-item" :class="{actived: index==currentType}" @click="handleChange(item,index)">
          {{item.name}}
        </li>
      </ul>
      <div class="input-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="910"
          :offset="5"
        >
          <!-- 内容 -->
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{item.q}}</div>
                <div class="icon" @click.stop="handleClear(item)"><i class="iconfont icon-guanbi" :style="{'font-size': '12px'}"></i></div>
              </li>
            </ul>
          </div>

          <template #reference>
            <el-input
              class="input-box"
              ref="inputRef"
              :placeholder="currentItem.placeholder"
              v-model.trim="currentItem.keywords"
              @focus="setIsShow"
              @input="isShowPopover = false"
              @blur="handleBlur"
              @keyup.enter="handleSearch"
            >
              <template #append>
                <div @click="handleSearch" class="search">查询</div>
              </template>
            </el-input>
          </template>
        </el-popover>
      </div>
      <div class="word-wrap">
        <div class="hot-tip">
          <i class="iconfont icon-redian hot-icon mr-10"></i>
          <div>热搜数据：</div>
        </div>
        <ul class="word-list ml-5">
          <li v-for="(word,index) in hotWordsByType" :key="index" @click="handleWord(word)" class="word-item">
            {{word}}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { getHotList } from '@/api/home';
  
  const searchList = ref([{
    name: '查标准',
    type: 0,
    placeholder: '输入标准号或标准名称关键词检索',
    keywords: ''
  },{
    name: '查公告',
    type: 1,
    placeholder: '输入公告号或标题关键词检索',
    keywords: ''
  },{
    name: '查法规',
    type: 2,
    placeholder: '输入标准法规标题关键词检索',
    keywords: ''
  },{
    name: '查计划',
    type: 3,
    placeholder: '输入标准计划号或名称关键词检索',
    keywords: ''
  }]);

  const { $modal } = useNuxtApp();
  const historyRef = ref();
  const inputRef = ref();
  const isShowPopover = ref(false);
  const currentType = ref(0);
  const currentItem = ref(searchList.value[0]);
  const hotWords = ref([]);
  const { getHistory,addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  
  const historyList = computed(() => {
    return getHistory(currentType.value);
  })
  const hotWordsByType = computed(() => {
    let list = hotWords.value.find((item: any) => item.type == currentType.value) || {value: []}

    return list.value || [];
  })

  const handleChange = (item: any,index: number) => {
    currentType.value = index;
    currentItem.value = item;
    isShowPopover.value = false;
  }
  const handleBlur = () => {
    setTimeout(() => {
      isShowPopover.value = false;
    },200)

  }
  const setIsShow = () => {
    if(historyList.value && historyList.value.length > 0){
      isShowPopover.value = true;
    }else{
      isShowPopover.value = false;
    }
  }

  const handleSearch = () => {
    if(!currentItem.value.keywords) return
    const noCaretReg = /^[^\\^]*$/;
    if(!noCaretReg.test(currentItem.value.keywords)){
      $modal.msgError('不可使用非法字符：^')
      return
    }
    
    // 隐藏popover弹窗
    isShowPopover.value = false;
    historyRef?.value.hide();

    let item = {
      d: Date.now(),
      t: currentType.value,
      q: currentItem.value.keywords,
    }
    addHistoryItem(item);
    
    // 跳转操作
    handleJump();
  }
  const handleJump = () => {
    if(currentItem.value.type == 0){
      navigateTo({ path: '/retrieval/domestic', query: { title: encodeURI(currentItem.value.keywords) }})
    }else if(currentItem.value.type == 1){
      navigateTo({ path: '/retrieval/announcement', query: { keyword: encodeURI(currentItem.value.keywords) }})
    }else if(currentItem.value.type == 2){
      navigateTo({ path: '/retrieval/law', query: { keyword: encodeURI(currentItem.value.keywords) }})
    }else if(currentItem.value.type == 3){
      navigateTo({ path: '/retrieval/plan', query: { keyword: encodeURI(currentItem.value.keywords) }})
    }
  }
  const handleWord = (word: string) => {
    currentItem.value.keywords = word;

    handleSearch()
  }
  const handleClick = (item: any) => {
    currentItem.value.keywords = item.q;

    handleSearch()
  }

  const handleClear = (item : any = null) => {
    if(!item){ // 全部清空
      removeHistoryAll(currentType.value);
    }else{
      removeHistoryItem(item);
    }
    setIsShow();
  }
  const getHot = async () => {
    hotWords.value = []
    await getHotList().then((res: any) => {
      nextTick(() => {
        hotWords.value = res.data || []
      })
    })
  }
onMounted(() => {
  // 添加滚动监听器
  window.addEventListener('scroll', handleScroll);
});
 
onUnmounted(() => {
  // 移除滚动监听器
  window.removeEventListener('scroll', handleScroll);
});
const handleScroll = () => {
  isShowPopover.value = false
  inputRef.value?.blur();
}

  await getHot()
</script>
<style lang="scss" scoped>
.banner-search-wrap{
  height: 480px;
  background: url('@/assets/images/home/<USER>') no-repeat center;
  background-size: 100% 100%;
  .banner-search-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .search-title{
      margin-top: 55px;
      font-weight: bold;
      font-size: 36px;
      color: #333333;
      span{
        margin: 0px 50px;
      }
    }
    .search-list{
      margin-top: 50px;
      display: flex;
      align-items: center;
      gap: 10px;
      .search-item{
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        padding: 10px 15px 20px 15px;
        cursor: pointer;
        &.actived{
          font-weight: bold;
          color: #FFFFFF;
          background: url('@/assets/images/home/<USER>') no-repeat center;
          background-size: 100% 100%;
        }
        
      }
    }
    .input-box {
      width: 910px;
      height: 55px;
      margin-top: 20px;
    }
    .search{
      width: 88px;
      height: 55px;
      line-height: 55px;
      text-align: center;
      background: $primary-color;
      font-size: 16px;
      color: #ffffff;
      cursor: pointer;
    }
    :deep(.el-input-group__append){
      padding: 0px;
    }
    .word-wrap {
      margin-top: 15px;
      margin-left: -100px;
      display: flex;
      width: 810px;
      overflow: hidden;
      font-size: 16px;
      color: #333333;
      height: 18px;
      .hot-tip {
        width: 110px;
        color: #333333;
        display: flex;
      }
      .word-list {
        flex: 1;
        display: flex;
        overflow: hidden;
        flex-wrap: wrap;
        .word-item {
          margin-right: 25px;
          cursor: pointer;
        }
      }
    }
    .hot-icon {
      color: #FF0000;
    }
  }
}
</style>
<style lang="scss">
.el-popover.el-popper.search-history-wrap{
  padding: 0px !important;
  margin: 0px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  z-index: 9 !important;
  .search-history-container {
    padding: 20px 0px 10px 0px;
    box-sizing: border-box;
    .title-wrap{
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #888888;
      .title{
        font-weight: bold;
      }
      .clear{
        display: flex;
        align-items: center;
        cursor: pointer;
        &:hover{
          color: $primary-color;
        }
      }
    }
    .history-list{
      margin-top: 5px;
      display: flex;
      flex-wrap: wrap;
      .history-item{
        padding: 0 20px;
        width: 50%;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #888888;
        &:nth-child(2n+1){
          border-right: 1px solid #E8E8E8;
        }
        &:hover{
          color: $primary-color;
          background: #F8F9FB;
          cursor: pointer;
          .icon{
            display: block;
          }
        }
        .title{
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .icon{
          flex-shrink: 0;
          margin-left: 10px;
          cursor: pointer;
          color: #888888;
          display: none;
          &:hover{
            color: $primary-color;
          }
        }
      }
    }
  }

}
</style>