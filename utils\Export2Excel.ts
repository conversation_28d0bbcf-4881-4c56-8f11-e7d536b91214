import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import * as XLSX_STYLE from 'xlsx-style-vite';

function datenum(v: string | Date, date1904?: boolean): number {
  let epoch: number;
  if (typeof v === 'string') {
    epoch = Date.parse(v);
  } else if (v instanceof Date) {
    epoch = v.getTime();
  } else {
    throw new Error('Invalid date format');
  }
  if (date1904) epoch += 1462 * 24 * 60 * 60 * 1000;
  return (epoch - new Date(Date.UTC(1899, 11, 30)).getTime()) / (24 * 60 * 60 * 1000);
}

interface Cell {
  v: any;
  t?: string;
  z?: string;
}

interface Worksheet {
  [key: string]: Cell | string;
  '!ref'?: any;
}

function sheet_from_array_of_arrays(data: any): Worksheet {
  let ws: Worksheet = {}; // 使用 Worksheet 类型定义 ws
  let range = {
    s: { c: 10000000, r: 10000000 },
    e: { c: 0, r: 0 },
  };

  for (let R = 0; R < data.length; ++R) {
    for (let C = 0; C < data[R].length; ++C) {
      if (range.s.r > R) range.s.r = R;
      if (range.s.c > C) range.s.c = C;
      if (range.e.r < R) range.e.r = R;
      if (range.e.c < C) range.e.c = C;

      let cell: Cell = { v: data[R][C] };
      if (cell.v == null) continue;

      let cell_ref = XLSX.utils.encode_cell({ c: C, r: R });

      if (typeof cell.v === 'number') cell.t = 'n';
      else if (typeof cell.v === 'boolean') cell.t = 'b';
      else if (cell.v instanceof Date) {
        cell.t = 'n';
        cell.z = XLSX.SSF._table[14]; // 设置日期格式
        cell.v = datenum(cell.v); // 将日期转换为数字
      } else {
        cell.t = 's'; // 默认为字符串类型
      }

      ws[cell_ref] = cell;
    }
  }

  // 如果范围有效，设置工作表的 ref 属性
  if (range.s.c < 10000000) {
    ws['!ref'] = XLSX.utils.encode_range(range); // 设置工作表的范围
  }

  return ws;
}

interface Workbook {
  SheetNames: string[];
  Sheets: { [key: string]: any }; // 'any' 可以根据需要调整为具体的类型
}

// 定义 Workbook 构造函数
function Workbook(): Workbook {
  const wb: Workbook = {
    SheetNames: [],
    Sheets: {},
  };
  return wb;
}

function s2ab(s: any) {
  var buf = new ArrayBuffer(s.length);
  var view = new Uint8Array(buf);
  for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
  return buf;
}

export function export_json_to_excel({
  multiHeader = [],
  header,
  data,
  sheetname,
  filename,
  merges = [],
  autoWidth = true,
  bookType = 'xlsx',
}: {
  multiHeader?: any[];
  header: any[];
  data: any[];
  sheetname: any[];
  filename?: string;
  merges?: any[];
  autoWidth?: boolean;
  bookType?: 'xlsx' | 'xls';
}) {
  filename = filename || 'excel-list';
  data = [...data];

  // 向数据的每一行前添加 header
  for (let i = 0; i < header.length; i++) {
    data[i].unshift(header[i]);
  }

  // 向每行前添加 multiHeader
  for (let i = 0; i < data.length; i++) {
    data[i].unshift(multiHeader);
  }

  // 创建新的工作簿
  const ws_name = sheetname;
  const wb = XLSX.utils.book_new();
  const ws: any = [];

  // 将数据转为工作表格式
  for (let j = 0; j < header.length; j++) {
    ws.push(XLSX.utils.aoa_to_sheet(data[j]));
  }

  // 设置样式
  ws.forEach((item: any) => {
    for (let key in item) {
      if (key === '!ref' || key === '!merges' || key === '!cols' || key === '!rows') {
        continue;
      } else if (key === 'A1') {
        item[key].s = {
          alignment: {
            horizontal: 'left',
            vertical: 'center',
            wrapText: false,
            indent: 0,
          },
          fill: {
            fgColor: { rgb: 'ffffff' },
          },
        };
      } else if (key.match(/\d+/g)?.join('') === '2') {
        item[key].s = {
          border: {
            top: { style: 'thin' },
            bottom: { style: 'thin' },
            left: { style: 'thin' },
            right: { style: 'thin' },
          },
          fill: { fgColor: { rgb: 'dddddd' } },
          alignment: {
            horizontal: 'center',
            vertical: 'center',
            wrapText: false,
            indent: 0,
          },
        };
      } else {
        item[key].s = {
          border: {
            top: { style: 'thin' },
            bottom: { style: 'thin' },
            left: { style: 'thin' },
            right: { style: 'thin' },
          },
          fill: { fgColor: { rgb: 'ffffff' } },
          alignment: {
            horizontal: 'center',
            vertical: 'center',
            wrapText: false,
            indent: 0,
          },
        };
      }
    }
  });

  // 合并单元格
  const mergesArr: any[] = [];
  const cellArr = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    'AA',
    'AB',
    'AC',
    'AD',
    'AE',
    'AF',
    'AG',
    'AH',
    'AI',
    'AJ',
    'AK',
    'AL',
    'AM',
    'AN',
    'AO',
    'AP',
    'AQ',
    'AR',
    'AS',
    'AT',
    'AU',
    'AV',
    'AW',
    'AX',
    'AY',
    'AZ',
  ];

  data.forEach(item => {
    const cell = 'A1:' + cellArr[item[1].length - 1] + '1';
    mergesArr.push(cell);
  });

  if (ws.length > 0) {
    ws.forEach((wsItem: any, wsIndex: any) => {
      if (!wsItem['!merges']) wsItem['!merges'] = [];
      wsItem['!merges'].push(XLSX.utils.decode_range(mergesArr[wsIndex]));
      if (!wsItem['!rows']) wsItem['!rows'] = [];
      wsItem['!rows'] = [
        { hpt: 150, hpx: 150 },
        { hpt: 30, hpx: 30 },
      ];
    });
  } else {
    if (!ws['!merges']) ws['!merges'] = [];
    merges.forEach(item => {
      ws['!merges'].push(XLSX.utils.decode_range(item));
    });
  }

  // 自动调整列宽
  if (autoWidth) {
    const colWidth = [];
    for (let k = 0; k < header.length; k++) {
      colWidth.push(
        data[k].map((row: any) =>
          row.map((val: any) => {
            if (val == null) {
              return { wch: 10 };
            } else if (val.toString().charCodeAt(0) > 255) {
              return { wch: val.toString().length * 3 };
            } else {
              return { wch: val.toString().length + 15 };
            }
          })
        )
      );
    }

    const result = [];
    for (let k = 0; k < colWidth.length; k++) {
      result[k] = colWidth[k][1];
      for (let i = 1; i < colWidth[k].length; i++) {
        for (let j = 0; j < colWidth[k][i].length; j++) {
          if (result[k][j]['wch'] < colWidth[k][i][j]['wch']) {
            result[k][j]['wch'] = colWidth[k][i][j]['wch'];
          }
        }
      }
    }

    for (let l = 0; l < result.length; l++) {
      ws[l]['!cols'] = result[l];
    }
  }

  // 将工作表加入到工作簿中
  for (let k = 0; k < header.length; k++) {
    wb.SheetNames.push(ws_name[k]);
    wb.Sheets[ws_name[k]] = ws[k];
  }

  // 写入 Excel 文件
  const wbout = XLSX_STYLE.write(wb, {
    bookType: bookType,
    bookSST: false,
    type: 'binary',
  });

  // 下载文件
  saveAs(
    new Blob([s2ab(wbout)], {
      type: 'application/octet-stream',
    }),
    `${filename}.${bookType}`
  );
}
