<template>
  <el-dialog
    width="620"
    v-model="props.visible"
    :title="props.data?.standardCode ? '编辑主导参与制修订标准' : '新增主导参与制修订标准'"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" :label-position="'top'" :rules="rules">
      <el-form-item label="标准号" prop="standardCode">
        <el-input v-model="form.standardCode" maxlength="50" placeholder="请输入标准号" />
      </el-form-item>
      <el-form-item label="标准名称" prop="standardName">
        <el-input v-model="form.standardName" maxlength="50" placeholder="请输入标准名称" />
      </el-form-item>
      <el-form-item label="标准类型" prop="standardType">
        <el-select
          ref="standardTypeRef"
          @change="standardTypeChange"
          v-model="form.standardType"
          placeholder="请选择标准类型"
          clearable
        >
          <el-option
            v-for="item in standardTypeOptions"
            :key="item['dictValue']"
            :label="item['dictLabel']"
            :value="item['dictValue']"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布年份" prop="publishDate">
        <el-date-picker
          v-model="form.publishDate"
          clearable
          type="date"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          placeholder="年/月/日"
        />
      </el-form-item>
      <el-form-item label="起草单位排名" prop="draftingUnitRanking">
        <el-select
          ref="draftingUnitRankingRef"
          @change="draftingUnitRankingChange"
          v-model="form.draftingUnitRanking"
          placeholder="请选择起草单位排名"
          clearable
        >
          <el-option
            v-for="item in draftingUnitRankingOptions"
            :key="item['dictValue']"
            :label="item['dictLabel']"
            :value="item['dictValue']"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标准状态" prop="standardStatus">
        <el-select
          ref="standardStatusRef"
          @change="standardStatusChange"
          v-model="form.standardStatus"
          placeholder="请选择标准状态"
          clearable
        >
          <el-option
            v-for="item in standardStatusOptions"
            :key="item['dictValue']"
            :label="item['dictLabel']"
            :value="item['dictValue']"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="采标情况" prop="procurementStatus">
        <el-select
          ref="procurementStatusRef"
          @change="procurementStatusChange"
          v-model="form.procurementStatus"
          placeholder="请选择采标情况"
          clearable
        >
          <el-option
            v-for="item in procurementStatusOptions"
            :key="item['dictValue']"
            :label="item['dictLabel']"
            :value="item['dictValue']"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否在标准信息公共服务平台公示" prop="isPubliclyDisplayed">
        <el-select
          ref="isPubliclyDisplayedRef"
          @change="isPubliclyDisplayedChange"
          v-model="form.isPubliclyDisplayed"
          placeholder="请选择是否在标准信息公共服务平台公示"
          clearable
        >
          <el-option
            v-for="item in isPubliclyDisplayedOptions"
            :key="item['dictValue']"
            :label="item['dictLabel']"
            :value="item['dictValue']"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex flex-jc-end">
        <el-button type="primary" plain @click="handleClose">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleConfirm(formRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { getDicts } from '@/api/common';
  import type { FormInstance } from 'element-plus';
  import { type IDicts } from '@/types';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
    },
  });

  const standardTypeRef = ref();
  const draftingUnitRankingRef = ref();
  const standardStatusRef = ref();
  const procurementStatusRef = ref();
  const isPubliclyDisplayedRef = ref();
  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const standardTypeOptions = ref([]);
  const draftingUnitRankingOptions = ref([]);
  const standardStatusOptions = ref([]);
  const procurementStatusOptions = ref([]);
  const isPubliclyDisplayedOptions = ref([]);
  const form = ref<any>({
    standardCode: '',
    standardName: '',
    standardType: '',
    publishDate: '',
    draftingUnitRanking: '',
    standardStatus: '',
    procurementStatus: '',
    isPubliclyDisplayed: '',
  });
  const rules = reactive({
    standardCode: [{ required: true, message: '请输入标准号', trigger: 'blur' }],
    standardName: [{ required: true, message: '请输入标准名称', trigger: 'blur' }],
    standardType: [{ required: true, message: '请选择标准类型', trigger: 'change' }],
    publishDate: [{ required: true, message: '请选择发布年份', trigger: 'change' }],
    draftingUnitRanking: [{ required: true, message: '请选择起草单位排名', trigger: 'change' }],
    standardStatus: [{ required: true, message: '请选择标准状态', trigger: 'change' }],
    procurementStatus: [{ required: true, message: '请选择采标情况', trigger: 'change' }],
    isPubliclyDisplayed: [{ required: true, message: '请选择是否在标准信息公共服务平台公示', trigger: 'change' }],
  });

  if (props.data?.standardCode) {
    form.value = { ...props.data };
  }

  let standardTypeDict = <IDicts>await getDicts('bxc_standard_type');
  standardTypeOptions.value = standardTypeDict?.data || [];

  let draftingUnitRankingDict = <IDicts>await getDicts('bxc_drafting_unit_ranking');
  draftingUnitRankingOptions.value = draftingUnitRankingDict?.data || [];

  let standardStatusDict = <IDicts>await getDicts('bxc_standard_status');
  standardStatusOptions.value = standardStatusDict?.data || [];

  let procurementStatusDict = <IDicts>await getDicts('bxc_procurement_status');
  procurementStatusOptions.value = procurementStatusDict?.data || [];

  let isPubliclyDisplayedDict = <IDicts>await getDicts('yes_no');
  isPubliclyDisplayedOptions.value = isPubliclyDisplayedDict?.data || [];

  const standardTypeChange = () => {
    nextTick(() => {
      form.value.standardTypeName = standardTypeRef.value?.selectedLabel;
    });
  };

  const draftingUnitRankingChange = () => {
    nextTick(() => {
      form.value.draftingUnitRankingName = draftingUnitRankingRef.value?.selectedLabel;
    });
  };

  const standardStatusChange = () => {
    nextTick(() => {
      form.value.standardStatusName = standardStatusRef.value?.selectedLabel;
    });
  };

  const procurementStatusChange = () => {
    nextTick(() => {
      form.value.procurementStatusName = procurementStatusRef.value?.selectedLabel;
    });
  };

  const isPubliclyDisplayedChange = () => {
    nextTick(() => {
      form.value.isPubliclyDisplayedName = isPubliclyDisplayedRef.value?.selectedLabel;
    });
  };

  const handleConfirm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    loading.value = true;
    formEl.validate(valid => {
      if (valid) {
        emit('updateData', form.value);
        handleClose();
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
    emit('update:data', {});
  };

  const emit = defineEmits(['update:visible', 'update:data', 'updateData']);
</script>

<style lang="scss" scoped>
  .tip {
    width: 100%;
    font-size: 14px;
    line-height: 20px;
    padding: 13px 20px;
    box-sizing: border-box;
    color: #888888;
    background-color: #f8f9fb;
  }

  :deep(.el-radio-button) {
    &:nth-child(n + 2) {
      margin-left: 20px;
    }
  }

  :deep(.el-radio-button__inner) {
    border-radius: var(--el-border-radius-base) !important;
    border: var(--el-border);
  }

  :deep(.el-radio-button__inner:hover) {
    border-color: var(--el-color-primary) !important;
  }

  :deep(.el-date-editor) {
    width: 100% !important;
  }
</style>
