<template>
  <div>
    <el-table :data="props.tableData">
      <template #empty>
        <BxcEmpty />
      </template>
      <el-table-column type="index" label="序号" width="80">
        <template #default="{ $index }">
          {{ (props.params?.pageNum - 1) * props.params?.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="标准号" min-width="180">
        <template #default="{ row }">
          <span @click="toDetail(row)" class="c-primary pointer">
            {{ row.standardCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="standardName" show-overflow-tooltip label="标准名称" min-width="180" />
      <el-table-column prop="standardTypeName" show-overflow-tooltip label="标准类型" min-width="120" />
      <el-table-column show-overflow-tooltip label="标准状态" min-width="120">
        <template #default="{ row }">
          <span :class="getStatusColor(statusToString(row.standardStatus), 'text')">{{ row.standardStatusName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="publishDate" show-overflow-tooltip label="发布日期" min-width="120" />
      <el-table-column prop="executeDate" show-overflow-tooltip label="实施日期" min-width="120" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <span
            @click="handleTrusteeship(row)"
            class="iconfont f-25 pointer"
            :class="row.trusteeshipType == 1 ? 'icon-kuangjituoguan c-primary' : 'icon-tuoguan c-88'"
          ></span>
        </template>
      </el-table-column>
    </el-table>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleLoginSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ElMessageBox } from 'element-plus';
  import { getStatusColor, handleJump } from '@/utils/common';
  import { type IStandardInfo, type IResponseData } from '@/types';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();
  const { $modal } = useNuxtApp();

  const props = defineProps({
    params: {
      type: Object,
    },
    tableData: {
      required: true,
      type: Array<IStandardInfo>,
    },
    url: {
      required: true,
      type: String,
    },
  });

  const openLogin = ref(false);

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const toDetail = (row: any) => {
    handleJump(props.url, { id: row.id, content: encodeURI(props.params?.content || '') });
  };

  const handleTrusteeship = async (row: any) => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      if (row.trusteeshipType == 1) {
        ElMessageBox.confirm('确认取消托管标准【' + row.standardCode + '】？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          lockScroll: false,
        })
          .then(() => {
            useHttp.delete('/search/trusteeshipManage/remove', { ids: [row.id] }).then(res => {
              let data = res as IResponseData;
              if (data.code == 200) {
                $modal.msgSuccess('取消标准托管成功！');
                emit('updateData');
              }
            });
          })
          .catch(() => {});
      } else {
        let data = <IResponseData>await useHttp.post('/search/trusteeshipManage', {
          standardId: row.id,
          standardCode: row.standardCode,
          standardType: row.standardType,
        });
        if (data.code == 200) {
          $modal.msgSuccess('标准托管成功！');
          emit('updateData');
        }
      }
    }
  };

  const handleLoginSuccess = () => {
    emit('updateData');
  }

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped></style>
