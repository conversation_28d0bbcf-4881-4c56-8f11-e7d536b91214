<template>
  <div>
    <div class="banner">
      <div class="main-wrap">
        <div class="f-32 c-33 f-bold">标准知识</div>
        <div class="banner-title mt-20 lh-26">
          专注各类标准知识科普与解读，涵盖国家标准、行业标准、地方标准等主要标准，通过名师讲解与多样化形式呈现，深入解读标准内容，助您快速掌握标准知识。
        </div>
      </div>
    </div>
    <div class="content main-wrap">
      <div class="content-title">知识分类</div>
      <div class="content-nav">
        <div
          @click="handleCut(index)"
          v-for="(item, index) in categoryList"
          :key="index"
          class="content-nav-title"
          :class="categoryActiveIndex == index ? 'active' : ''"
        >
          {{ item }}
        </div>
      </div>
      <div class="content-category">
        <ClassroomKnowledgeIndustry v-if="categoryActiveIndex == 0" :data="categoryData" />
        <ClassroomKnowledgeMotif v-if="categoryActiveIndex == 1" :data="categoryData" />
      </div>
      <div class="flex flex-sb mt-40">
        <div class="content-title">为您推荐</div>
        <div @click="handleJump('/classroom/knowledge/search')" class="flex flex-ai-center pointer">
          <span class="f-16 c-primary">查看更多</span>
          <el-icon class="c-primary f-16"><ArrowRight /></el-icon>
        </div>
      </div>
      <div v-if="tableData && tableData.length > 0" class="content-card mt-25">
        <div
          @click="handleJump('/classroom/knowledge/detail', { id: item.id })"
          v-for="item in tableData"
          :key="item.id"
          class="content-card-item"
        >
          <img
            v-if="item.mainImageList && item.mainImageList.length > 0"
            :src="item.mainImageList[0].url"
            class="content-card-item-img"
            alt=""
          />
          <div class="content-card-item-info">
            <div class="content-card-item-title overflow-ellipsis">{{ item.knowledgeName }}</div>
            <div class="flex mt-10" style="min-height: 30px">
              <div
                v-for="(childItem, childIndex) in item.economicTypeNameList"
                :key="childIndex"
                class="content-card-item-label mr-10 overflow-ellipsis"
                :class="getStatusColor('purple')"
              >
                {{ childItem }}
              </div>
            </div>
            <div class="f-14 c-99 mt-10">{{ item.publishDate.split(' ')[0] }}</div>
          </div>
        </div>
      </div>
      <BxcEmpty v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准知识_标准课堂_标准知识课堂_标准学院_标信查平台',
    meta: [
      { name: 'keywords', content: '标准学院，标准解读，标准知识，标准课堂' },
      {
        name: 'description',
        content:
          '标信查平台标准学院频道，名师在线讲解标准知识、深入解读标准内容，及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务。',
      },
    ],
  });

  import { getNationalEconomicTypeTree, getKnowledgeRecommend } from '@/api/classroom/knowledge';
  import { getDicts } from '@/api/common';
  import { type IKnowledgeInfo, type IDicts, type IResponseData } from '@/types';

  const categoryActiveIndex = ref(0);
  const categoryList = reactive(['按产业分类', '按主题分类']);
  const industryOptions = ref([]);
  const themeOptions = ref<IDicts[]>([]);
  const categoryData = ref<any>([]);
  const tableData = ref<IKnowledgeInfo[]>([]);

  let industryRes = <IResponseData>await getNationalEconomicTypeTree({ deep: 2 });
  industryOptions.value = industryRes.data || [];

  let themeRes = <IResponseData>await getDicts('bxc_knowledge_theme');
  themeOptions.value = themeRes.data || [];

  let { data } = <IResponseData>await getKnowledgeRecommend();
  if (data && data.length > 0) {
    data.forEach((item: any) => {
      item.economicTypeNameList = item.economicTypeName ? splitStrToArray(item.economicTypeName, ' | ').slice(0, 3) : [];
    });
  }
  tableData.value = data || [];

  const handleCut = (index: number) => {
    if (index != categoryActiveIndex.value) {
      categoryActiveIndex.value = index;
      categoryData.value = categoryActiveIndex.value == 0 ? industryOptions.value : themeOptions.value;
    }
  };

  categoryData.value = industryOptions.value;
</script>

<style scoped lang="scss">
  .banner {
    width: 100%;
    min-height: 380px;
    background: url('@/assets/images/classroom/knowledge.png') no-repeat center;
    background-size: 100% 100%;
    display: block;

    .main-wrap {
      padding-top: 165px;
    }

    &-title {
      font-size: 16px;
      color: #333;
      width: 750px;
      line-height: 24px;
    }
  }

  .content {
    padding: 40px 0 65px;
    box-sizing: border-box;

    &-title {
      font-size: 22px;
      color: #333;
      font-weight: 600;
    }

    &-nav {
      width: 100%;
      height: 60px;
      background: url('@/assets/images/classroom/classify.png') no-repeat center;
      background-size: 100% 100%;
      margin-top: 25px;
      display: flex;
      align-items: center;
      padding: 0 25px;
      box-sizing: border-box;

      &-title {
        position: relative;
        font-size: 16px;
        color: #fff;
        cursor: pointer;
        margin-right: 50px;
      }

      .active {
        font-weight: 600;

        &::after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: calc(50% - 20px);
          width: 40px;
          height: 3px;
          background-color: #fff;
          transition: all 0.3s ease;
          border-radius: 2px;
        }
      }
    }

    &-category {
      background-color: #f8f9fb;
      padding: 20px 25px 40px;
      box-sizing: border-box;
    }

    &-card {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 24px;

      &-item {
        border-radius: 5px;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #e8e8e8;

        &:hover {
          box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.19);
        }

        &:hover &-title {
          color: $primary-color;
        }

        &:hover &-info {
          background-color: #fff;
        }

        &-img {
          width: 100%;
          height: 157px;
          display: block;
        }

        &-label {
          max-width: 48%;
        }

        &-info {
          padding: 17px 14px 20px;
          box-sizing: border-box;
          background-color: #f8f9fb;
          height: 100%;
        }

        &-title {
          font-size: 16px;
          color: #333;
          font-weight: 600;
        }
      }
    }
  }
</style>
