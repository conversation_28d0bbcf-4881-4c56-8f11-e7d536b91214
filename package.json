{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"dev": "nuxt dev --dotenv .env.development --host", "build:prod": "nuxt build --dotenv .env.production", "build:stage": "nuxt build --dotenv .env.staging", "generate:prod": "nuxt generate --dotenv .env.production", "generate:stage": "nuxt generate --dotenv .env.staging", "preview:prod": "nuxt preview --dotenv .env.production", "preview:stage": "nuxt preview --dotenv .env.staging", "postinstall": "nuxt prepare"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@pinia/nuxt": "^0.5.5", "@vueuse/core": "^11.1.0", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "nuxt": "^3.13.0", "pinia": "^2.2.4", "qrcode": "^1.5.4", "vue": "latest", "vue-router": "latest", "xlsx": "^0.18.5", "xlsx-style-vite": "^0.0.2"}, "devDependencies": {"@element-plus/nuxt": "^1.0.10", "@types/crypto-js": "^4.2.2", "@types/qrcode": "^1.5.5", "element-plus": "^2.9.0", "nuxt-echarts": "^0.2.3", "sass": "1.78"}}