<template>
  <div class="main-wrap">
    <div class="notice-wrap">
      <div class="title-wrap">
        <div class="title">公告列表</div>
        <div class="search">
          <el-input
            :style="{width: '280px'}"
            v-model="queryParams.title"
            placeholder="输入公告标题名称"
            @keyup.enter="getList"
          >
            <template #suffix>
              <el-icon @click="getList" class="c-primary pointer"><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
      <div class="notice-container">
        <template v-if="noticeList && noticeList.length > 0">
          <div class="notice-list">
            <div v-for="item in noticeList" :key="item.id" @click="handleDetail(item)" class="notice-item">
              <div class="item-title">{{item.title}}</div>
              <div class="item-date">{{parseTime(item.publishDate,'{y}-{m}-{d}')}}</div>
            </div>
          </div>
          <BxcPagination v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
        </template>
        <template v-else>
          <BxcEmpty class="mt-30"/>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getNoticeList } from '@/api/notice'
import type { INoticeInfo } from '@/types'

const total = ref(0)
const noticeList = ref(<INoticeInfo[]>[])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  title: ''
})

const getList = () => {
  getNoticeList(queryParams.value).then((res: any) => {
    nextTick(() => {
      total.value = res.total || 0
      noticeList.value = res.rows || []
    })
  })
}
const handleDetail = (item: any) => {
  navigateTo(`/notice/detail?id=${item.id}`,{
    open: {
      target: '_blank'
    }
  })
}

getList()
</script>
<style lang="scss" scoped>
.notice-wrap{
  margin-top: 60px;
  .title-wrap{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 0 20px 0;
    .title{
      font-weight: bold;
      font-size: 26px;
      color: #333333;
    }
  }
  .notice-container{
    margin-bottom: 40px;
    .notice-list{
      padding-bottom: 20px;
      .notice-item{
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #E8E8E8;
        cursor: pointer;
        &:last-child{
          border-bottom: none;
        }
        &:hover{
          .item-title{
            color: $primary-color;
          }
        }

        .item-title{
          flex: 1;
          font-size: 16px;
          color: #333333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .item-date{
          margin-left: 20px;
          flex-shrink: 0;
          font-size: 14px;
          color: #888888;
        }
      }
    }
  }
}
</style>