<template>
  <div class="flex flex-jc-end">
    <ClientOnly>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :size="size"
        :background="background"
        :layout="layout"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
  import type { ComponentSize } from 'element-plus';

  const props = defineProps({
    size: {
      type: String as PropType<ComponentSize>,
      default: 'default',
      validator: (value: string) => {
        return ['small', 'medium', 'large', 'default'].includes(value);
      },
    },
    disabled: {
      type: Boolean as PropType<boolean>,
    },
    total: {
      type: Number as PropType<number>,
      required: true,
    },
    page: {
      type: Number as PropType<number>,
      default: 1,
    },
    limit: {
      type: Number as PropType<number>,
      default: 20,
    },
    pageSizes: {
      type: Array as PropType<number[]>,
      default: () => [10, 20, 30, 50],
    },
    layout: {
      type: String as PropType<string>,
      default: 'total, sizes, prev, pager, next, jumper',
    },
    background: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    anchorPoint: {
      type: String,
      default: '',
    },
  });

  const currentPage = computed({
    get() {
      return props.page;
    },
    set(val) {
      emit('update:page', val);
    },
  });

  const pageSize = computed({
    get() {
      return props.limit;
    },
    set(val) {
      emit('update:limit', val);
    },
  });

  const handleSizeChange = (val: number) => {
    currentPage.value = 1;
    emit('pagination');
    handleRoll();
  };

  const handleCurrentChange = (val: number) => {
    emit('pagination');
    handleRoll();
  };

  const handleRoll = () => {
    nextTick(() => {
      window.scrollTo({
        top: getElementDistanceFromTop(),
        behavior: 'smooth',
      });
    });
  };

  const getElementDistanceFromTop = () => {
    const element = document.getElementById(props.anchorPoint);
    if (element) {
      const rect = element.getBoundingClientRect();
      console.log(rect.top, window.scrollY);

      return rect.top + window.scrollY - 70;
    } else {
      return 0;
    }
  };

  const emit = defineEmits(['update:page', 'update:limit', 'pagination']);
</script>
