<template>
  <div class="csc-wrap">
    <div class="csc-header">
      <div class="csc-header-container main-wrap">
        <div class="title">客户服务中心</div>
        <div class="desc">为您提供一站式的咨询和自动服务</div>
      </div>
    </div>
    <div class="csc-container main-wrap">
      <!-- 客户服务中心 -->
      <div class="f-22 f-bold c-33">客户服务中心</div>
      <ul class="service-list">
        <li class="service-item">
          <div class="item-left">
            <div class="title">电话客服</div>
            <div class="desc">服务时间：工作日9：00 - 17:30</div>
            <div class="mobile">************</div>
          </div>
          <img src="@/assets/images/csc/phone.png" alt="" class="icon">
        </li>
        <li class="service-item">
          <div class="item-left">
            <div class="title">在线客服</div>
            <div class="desc">服务时间：工作日9：00 - 17:30</div>
            <el-button class="consult-button" type="primary" @click="handleConsult">立即咨询</el-button>
          </div>
          <img src="@/assets/images/csc/online.png" alt="" class="icon">
        </li>
      </ul>
      <template v-if="dataList && dataList.length > 0">
        <!-- 常见问题 -->
        <div class="f-22 f-bold c-33 mt-40">常见问题</div>
        <el-tabs v-model="activeName" class="common-problem-tabs">
          <el-tab-pane v-for="item in dataList" :key="item.type" :label="item.typeName" :name="item.type">
            <CscCommonProblemItem :list="item.list" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </div>
    <ClientOnly>
      <!-- 选择客服 -->
      <CscPopSelectService v-if="openService" v-model:open="openService" :list="customerList" @success="selectServiceSuccess"/>
      <!-- 在线咨询 -->
      <CscPopOnlineConsult v-if="open" v-model:open="open" :customer="currentCustomer" />
    </ClientOnly>
  </div>
</template>
<script lang="ts" setup>
import { getCscList } from '@/api/csc'
import { getCustomerList } from '@/api/online-service'
import { type ICustomer } from "@/types"

interface ICommonProblemItem {
  type: string
  typeName: string
  list: any[]
}
const activeName = ref('0')
const dataList = ref(<ICommonProblemItem[]>[])
const open = ref(false)
const openService = ref(false)
const customerList = ref(<ICustomer[]>[])
const currentCustomer = ref(<ICustomer>{})
const { $modal } = useNuxtApp()

const getData = async () => {
  try {
    const res: any = await getCscList()
    dataList.value = res.data || []
  } catch (error) {}
}
const selectServiceSuccess = (item: ICustomer) => {
  currentCustomer.value = item
  open.value = true
  openService.value = false
}
const handleConsult = () => {
  getCustomers()
}

const getCustomers = () => {
  getCustomerList().then((res: any) => {
    if (res.data && res.data.length > 0) {
      customerList.value = res.data

      if (customerList.value.length > 1) {
        openService.value = true
      } else {
        currentCustomer.value = customerList.value[0] || {}
        open.value = true
      }
    }else{
      $modal.msgError('暂无客服在线')
    }
  })
}

await getData()

</script>
<style lang="scss" scoped>
.csc-header {
  height: 380px;
  background: url("@/assets/images/csc/csc-header-bg.png") no-repeat center;
  background-size: 100% 100%;
  .csc-header-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    font-size: 16px;
    color: #333333;
    .title {
      margin: 60px 0 25px 0;
      font-weight: bold;
      font-size: 32px;
    }
    .desc{
      width: 638px;
      font-size: 16px;
      color: #333333;
      line-height: 26px;
    }
  }
}
.csc-container{
  padding: 40px 0px;
  .service-list{
    margin-top: 20px;
    display: flex;
    gap: 25px;
    .service-item{
      width: 408px;
      height: 130px;
      padding: 20px;
      box-sizing: border-box;
      background: #F2F6FC;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item-left{
        display: flex;
        flex-direction: column;
        justify-content: center;
        .title{
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        .desc{
          margin-top: 15px;
          font-size: 14px;
          color: #888888;
        }
        .mobile{
          margin-top: 15px;
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        .consult-button{
          margin-top: 15px;
          width: 80px;
          height: 30px;
          background: $primary-color;
          border-radius: 3px;
          font-size: 14px;
          color: #FFFFFF;
          border-color: none;
          &:hover{
            background: $primary-hover-color;
          }
        }
      }
      .icon{
        height: 71px;
      }
    }
  }
  .common-problem-tabs{
    margin-top: 25px;
  }
}
:deep(.el-tabs){
  padding: 0px !important;
  .el-tabs__content{
    padding: 25px 0px;
    min-height: auto !important;
  }
}
:deep(.el-tabs__nav){
  padding-bottom: 10px;
  .el-tabs__item{
    font-size: 16px !important;
  }
  .el-tabs__item.is-active{
    font-weight: bold !important;
  }
}
</style>