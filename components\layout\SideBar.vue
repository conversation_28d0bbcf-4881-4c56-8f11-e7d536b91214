<template>
  <div v-if="!pathList.includes(route.path)" class="side-bar-wrap">
    <div class="side-bar-container">
      <img class="side-icon" src="@/assets/images/layout/side-icon.png" alt="" />
      <div class="side-bar-list">
        <div class="side-bar-item" @click="handleConsult">
          <i class="iconfont icon-kefu f-28"></i>
          <span>咨询</span>
        </div>
        <div class="line"></div>
        <el-popover width="180" placement="left" trigger="hover" :offset="22">
          <div class="img-wx">
            <img src="@/assets/images/layout/wx-official.png" alt="" />
            <div class="img-tip">关注标信查，讯息不迟到！</div>
          </div>
          <template #reference>
            <div class="side-bar-item">
              <i class="iconfont icon-a-ziyuan578 f-28"></i>
              <span>公众号</span>
            </div>
          </template>
        </el-popover>
        <div class="line"></div>
        <div class="side-bar-item" @click="handleDemand">
          <i class="iconfont icon-xuqiu f-28"></i>
          <span>需求</span>
        </div>
        <template v-if="isShowTop">
          <div class="line"></div>
          <div class="side-bar-item" @click="handleToTop">
            <i class="iconfont icon-icon3 f-28"></i>
            <span>返回顶部</span>
          </div>
        </template>
      </div>
    </div>
    <SupportPopConsult v-if="openConsult" v-model:open="openConsult" />
  </div>
</template>
<script lang="ts" setup>
  const route = useRoute();
  const router = useRouter();
  const openConsult = ref(false);
  const isShowTop = ref(false);
  const pathList = reactive([
    '/retrieval/domesticDetail',
    '/retrieval/internationDetail',
    '/retrieval/tcDetail',
    '/retrieval/sampleDetail',
    '/retrieval/expertDetail',
    '/retrieval/announcementDetail',
    '/retrieval/lawDetail',
    '/retrieval/planDetail',
    '/classroom/knowledge/detail',
  ]);

  function handleScroll() {
    isShowTop.value = window.scrollY > 200;
  }

  onMounted(() => {
    window.addEventListener('scroll', handleScroll);
  });

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
  });

  const handleConsult = () => {
    router.push('/csc');
  };
  const handleDemand = () => {
    openConsult.value = true;
  };
  const handleToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };
</script>
<style lang="scss" scoped>
  .side-bar-wrap {
    position: fixed;
    right: 8px;
    bottom: 150px;
    width: 74px;
    overflow: hidden;
    z-index: 999;
    .side-bar-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .side-icon {
        width: 64px;
        height: 56px;
      }
      .side-bar-list {
        flex: 1;
        background: $primary-color;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        font-size: 14px;
        .side-bar-item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-radius: 5px;
          cursor: pointer;
          padding: 10px 0px;
          box-sizing: border-box;
          span {
            margin-top: 5px;
          }
        }
        .line {
          width: 100%;
          height: 1px;
          background: RGBA(255, 255, 255, 0.17);
        }
      }
    }
  }
  .img-wx {
    width: 160px;
    height: 160px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 110px;
      height: 110px;
    }
    .img-tip {
      margin-top: 10px;
      font-size: 12px;
      color: #333333;
    }
  }
</style>
