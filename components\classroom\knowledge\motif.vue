<template>
  <div>
    <div v-if="props.data && props.data.length > 0" class="motif">
      <div
        @click="handleJump('/classroom/knowledge/search', { theme: encodeURI(item.dictValue) })"
        v-for="(item, index) in props.data"
        :key="index"
        class="motif-item"
      >
        <div class="motif-item-title">{{ item.dictLabel }}</div>
      </div>
    </div>
    <BxcEmpty v-else />
  </div>
</template>

<script setup lang="ts">
  import { handleJump } from '@/utils/common';

  interface dataType {
    dictValue: string;
    dictLabel: string;
  }

  const props = defineProps({
    data: {
      type: Array as () => dataType[],
      default: () => [],
    },
  });
</script>

<style scoped lang="scss">
  .motif {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-gap: 20px;
    border-radius: 5px;
    overflow: hidden;

    &-item {
      display: flex;
      align-items: center;
      width: 215px;
      height: 80px;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      background: url('@/assets/images/classroom/motif.png') no-repeat center;
      background-size: 100% 100%;
      padding: 0 80px 0 20px;
      box-sizing: border-box;
      cursor: pointer;

      &:hover {
        background: url('@/assets/images/classroom/motif-active.png') no-repeat center;
        box-shadow: 0px 0px 9px 0px rgba(0, 0, 0, 0.32);
      }

      &-title {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        line-clamp: 1;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: normal;
        word-wrap: break-word;
        word-break: break-word;
      }
    }
  }
</style>
