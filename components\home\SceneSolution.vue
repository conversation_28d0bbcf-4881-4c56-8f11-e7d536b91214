<template>
  <div class="scene-wrap main-wrap">
    <h3 class="title">场景&解决方案</h3>
    <div class="scene-container">
      <div class="scene-left">
        <div class="title">
          <img src="@/assets/images/home/<USER>" alt="">
          <span>标准数字化管理系统</span>
        </div>
        <div class="f-bold f-16 c-33 mt-30 ml-40">适用对象</div>
        <div class="f-14 c-88 mt-10 desc ml-40"><div class="flex"><i class="dot"/></div><span>适用及管理标准量在千以上的企业</span></div>
        <div class="f-bold f-16 c-33 mt-30 ml-40">方案简述</div>
        <div class="f-14 c-88 mt-10 desc ml-40"><div class="flex"><i class="dot"/></div><span>标准数字化管理系统是一个连续高效、可靠、集实用性和先进性为一体的系统，能够对企业标准体系和标准资源进行有效管理，并且实现了标准的数字化、智能化、流程化管理过程、标准资源数据管理、标准咨询和服务的信息化。</span></div>
        <div class="btns ml-40">
          <div class="detail" @click="handleDetail">了解详情</div>
          <div class="demo" @click="handleOrder">预约演示</div>
        </div>
      </div>
      <div class="scene-right">
        <img src="@/assets/images/home/<USER>" alt="">
      </div>
    </div>
    <!-- 预约演示弹框 -->
    <SolutionPopOrder v-if="openOrder" v-model:open="openOrder" />
  </div>
</template>
<script lang="ts" setup>

const openOrder = ref(false)

const handleDetail = () => {
  navigateTo('/solution/esms')
}
const handleOrder = () => {
  openOrder.value = true
}
</script>
<style lang="scss" scoped>
.scene-wrap{
  margin-top: 70px;
  .scene-container{
    padding: 30px;
    height: 490px;
    box-sizing: border-box;
    background: #F8F9FA;
    display: flex;
    align-items: center;
    .scene-left{
      flex: 1;
      .title{
        font-weight: bold;
        font-size: 20px;
        color: #333333;
        display: flex;
        align-items: center;
        span{
          margin-left: 10px;
        }
      }
      .desc{
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        line-height: 22px;
      }
      .dot{
        margin-top: 7px;
        display: inline-block;
        line-height: 100%;
        width: 7px;
        height: 7px;
        background: $primary-color;
        border-radius: 50%;
        margin-right: 10px;
      }
      .btns{
        display: flex;
        margin-top: 50px;
        .detail{
          width: 131px;
          height: 46px;
          background: $primary-color;
          color: #FFFFFF;
          text-align: center;
          line-height: 46px;
          cursor: pointer;
          &:hover{
            background: $primary-hover-color;
          }
        }
        .demo{
          margin-left: 25px;
          width: 131px;
          height: 46px;
          border: 1px solid $primary-color;
          background: #FFFFFF;
          color: $primary-color;
          text-align: center;
          line-height: 46px;
          cursor: pointer;
          &:hover{
            background: $primary-color;
            color: #FFFFFF;
          }
        }
      }
    }
    .scene-right{
      margin-left: 30px;
      width: 552px;
      img{
        width: 552px;
      }
    }
  }
}
</style>