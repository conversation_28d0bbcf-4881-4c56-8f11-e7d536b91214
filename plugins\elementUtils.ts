import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'

let loadingInstance: any;

const modal =  {
  // 消息提示
  msg(content='') {
    ElMessage({
      type: 'info',
      message: content,
      grouping: true,
    })
  },
  // 错误消息
  msgError(content='') {
    ElMessage({
      type: 'error',
      message: content,
      grouping: true,
    })
  },
  // 成功消息
  msgSuccess(content='') {
    ElMessage({
      type: 'success',
      message: content,
      grouping: true,
    })
  },
  // 警告消息
  msgWarning(content='') {
    ElMessage({
      type: 'warning',
      message: content,
      grouping: true,
    })
  },
  // 弹出提示
  alert(content='',confirmButtonText='确定',showClose=true) {
    return ElMessageBox.alert(content, "系统提示",{ showClose, confirmButtonText})
  },
  // 错误提示
  alertError(content='',confirmButtonText='确定',showClose=true) {
    return ElMessageBox.alert(content, "系统提示", { type: 'error', showClose, confirmButtonText })
  },
  // 成功提示
  alertSuccess(content='',confirmButtonText='确定',showClose=true) {
    return ElMessageBox.alert(content, "系统提示", { type: 'success', showClose, confirmButtonText })
  },
  // 警告提示
  alertWarning(content='',confirmButtonText='确定',showClose=true) {
    return ElMessageBox.alert(content, "系统提示", { type: 'warning', showClose, confirmButtonText })
  },
  // 通知提示
  notify(content='') {
    ElNotification.info(content)
  },
  // 错误通知
  notifyError(content='') {
    ElNotification.error(content);
  },
  // 成功通知
  notifySuccess(content='') {
    ElNotification.success(content)
  },
  // 警告通知
  notifyWarning(content='') {
    ElNotification.warning(content)
  },
  // 确认窗体
  confirm(content='',title='',confirmButtonText='确定') {
    return ElMessageBox.confirm(content, title == '' ? "系统提示" : title, {
      confirmButtonText: confirmButtonText,
      cancelButtonText: '取消',
      type: "warning",
    })
  },
  // 提交内容
  prompt(content='') {
    return ElMessageBox.prompt(content, "系统提示", {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: "warning",
    })
  },
  // 打开遮罩层
  loading(content='') {
    loadingInstance = ElLoading.service({
      lock: true,
      text: content,
      background: "rgba(0, 0, 0, 0.7)",
    })
  },
  // 关闭遮罩层
  closeLoading() {
    if(loadingInstance){
      loadingInstance.close();
    }
  }
}

export default defineNuxtPlugin((nuxtApp) => {
  return {
    provide: {
      modal: modal,
    },
  };
});