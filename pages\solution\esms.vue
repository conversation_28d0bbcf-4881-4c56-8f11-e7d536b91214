<template>
  <div class="esms-wrap">
    <div class="esms-header">
      <div class="esms-header-container main-wrap">
        <div class="title">标准数字化管理系统</div>
        <div class="desc">
          标准数字化管理系统是一个连续高效、可靠、集实用性和先进性为一体的系统，能够对企业标准体系和标准资源进行有效管理，并且实现了标准的数字化、智能化、流程化管理过程、标准资源数据管理、标准咨询和服务的信息化
        </div>
        <div class="order-btn" @click="handleOrder">
          预约演示
        </div>
        <div class="tip">
          您可在线提交预约演示信息或致电 400-109-7887，我们将安排专人为您服务!
        </div>
      </div>
    </div>
    <!-- 版本规格 -->
    <div class="version-wrap main-wrap">
      <div class="v-title">版本规格</div>
      <div class="version-list">
        <div v-for="(item,index) in versionList" :key="index" class="version-item" :class="`version-item-bg${index}`">
          <div class="item-title">{{item.name}}</div>
          <div class="item-desc">{{item.desc}}</div>
          <ul class="item-list">
            <li v-for="(fun, i) in item.children" :key="i" class="item">
              <img src="@/assets/images/solution/check.png" alt="">
              <div>{{fun}}</div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- 产品优势 -->
    <div class="advantage-wrap">
      <div class="main-wrap">
        <div class="v-title">产品优势</div>
        <div class="advantage-list">
          <div v-for="(item,index) in advantageList" :key="index" class="advantage-item">
            <img :src="item.icon" alt="">
            <div class="item-container">
              <div class="item-title">{{item.name}}</div>
              <div class="item-desc">{{item.desc}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 核心功能 -->
    <div class="core-wrap main-wrap">
      <div class="v-title">核心功能</div>
      <ul class="core-menu-list">
        <li v-for="(item,index) in coreList" :key="index" @mouseenter="changeCore(index)" :class="{actived: currentCoreIndex==index}" class="core-menu-item">
          <img :src="item.icon" alt="">
          <div class="menu-name">{{item.name}}</div>
        </li>
      </ul>
      <ul class="item-list">
        <li v-for="(item,index) in coreList[currentCoreIndex].children" :key="index" class="item">
          <div class="title">{{item.title}}</div>
          <div class="desc">{{item.desc}}</div>
        </li>
      </ul>
    </div>
    <!-- 预约演示弹框 -->
    <SolutionPopOrder v-if="openOrder" v-model:open="openOrder" />
  </div>
</template>
<script setup lang="ts">
import ADVANTAGE0 from '@/assets/images/solution/advantage-item-0.png'
import ADVANTAGE1 from '@/assets/images/solution/advantage-item-1.png'
import ADVANTAGE2 from '@/assets/images/solution/advantage-item-2.png'
import ADVANTAGE3 from '@/assets/images/solution/advantage-item-3.png'
import COREITEM0 from '@/assets/images/solution/core-item-0.png'
import COREITEM1 from '@/assets/images/solution/core-item-1.png'
import COREITEM2 from '@/assets/images/solution/core-item-2.png'
import COREITEM3 from '@/assets/images/solution/core-item-3.png'
import COREITEM4 from '@/assets/images/solution/core-item-4.png'
import COREITEM5 from '@/assets/images/solution/core-item-5.png'

const { setUseHead } = useSeo()
setUseHead('/solution/esms')

const versionList = [{
  name: '企业标准版',
  desc: '配置灵活，简单易用高效',
  children: [
    '满足企业标准信息化管理',
    '标准文本数字化与智能检索',
    '专业技术领域标准体系构建',
    '标准化工作流程管理',
    '标准材料知识库管理'
  ]
},{
  name: '企业定制版',
  desc: '功能定制，业务功能更加丰富',
  children: [
    '标准管理信息化、数字化、智能化管理',
    '满足企业个性化功能定制开发',
    '标准化流程与业务功能更加丰富',
    '标准材料管理与标准培训课堂'
  ]
},{
  name: '集团企业版',
  desc: '业务领域覆盖广泛',
  children: [
    '集团领域覆盖广泛，多子公司统一认证登录(sso)协同应用管理',
    '专业技术领域标准全量信息化、数字化、智能化管理应用，功能丰富',
    '集团标准应用数据统计分析'
  ]
}]
const advantageList = [{
  name: '云端中心资源库',
  desc: '依托于标信查云端大数据中心，涵盖标准（国家、行业、地方、团体，以及部分企业、国际、国外）、公告、意见稿、公示、法律法规等海量资源数据，通过云端中心数据库与企业标准库之间的互联互通，帮助企业高效获取样数据资源',
  icon: ADVANTAGE0
},{
  name: '标准文本结构化数据解析',
  desc: '云端中心数据库通过对海量标准文本进行结构化数据解析，实现对标准文本内容的检索需求以及对标准文本的可视化应用',
  icon: ADVANTAGE1
},{
  name: '标准一键维护',
  desc: '通过与标信查云端中心数据库的互联互通，可实现企业标准库与云端中心数据库数据交叉比对，帮助企业实现标准数据快速维护更新，高效提升企业标准维护成本',
  icon: ADVANTAGE2
},{
  name: '标准动态监测',
  desc: '通过对标准库中全量标准进行动态监测，及时预警标准状态变化，有效帮助企业提高标准企业正确性与有效性',
  icon: ADVANTAGE3
}]
const coreList = [{
  name: '数据资源管理&应用',
  icon: COREITEM0,
  children: [{
    title: '标准数据查询',
    desc: '针对标准关键性题录信息进行组合式检索查询，同时可实现对标准文本内容关键字检索'
  },{
    title: '标准资源管理',
    desc: '自主维护企业标准库信息，通过手动标准入库和互联标准入库等多方式、快捷实现'
  },{
    title: '标准文本解析',
    desc: '企业实现对自主拥有的标准文本进行结构化数据解析，实现标准的可视化应用'
  },{
    title: '标准数据查新',
    desc: '批量标准状态有效性快速查询，确保标准实际应用过程中的准确性'
  },{
    title: '函文函电查询',
    desc: '针对国家、部委、地方、企业发布应用性函文(法律、法规、规章)的查询阅览'
  }]
},{
  name: '标准体系&应用',
  icon: COREITEM1,
  children: [{
    title: '标准体系',
    desc: '构建企业自身需求的各类型标准体系结构，实现企业对标准的专业化管理'
  },{
    title: '标准应用',
    desc: '以企业项目应用为基础，构建针对企业实际项目应用的各类标准的实际应用管理'
  }]
},{
  name: '标准实施与监督',
  icon: COREITEM2,
  children: [{
    title: '标准反馈',
    desc: '有效聆听各部门各岗位人员在标准实际使用过程的意见与建议性信息'
  },{
    title: '标准复审',
    desc: '实施企业标准内容定期审查，确保其有效性、先进性和适用性情况'
  },{
    title: '制修订过程管理',
    desc: '科学、系统性管理标准制修订过程，做到记录留痕、结果有据'
  }]
},{
  name: '标准同步&预警',
  icon: COREITEM3,
  children: [{
    title: '导入同步',
    desc: '内网部署环境下，企业对标准库数据进行批量更新操作，实现对标准数据维护'
  },{
    title: '扫描同步',
    desc: '内网部署环境可连通外网时，通过与云端中心库互联，实现标准自动化更新'
  },{
    title: '标准预警',
    desc: '针对标准状态变化时，提交生成系统预警信息，确保企业对标准库的及时维护'
  }]
},{
  name: '知识资源库',
  icon: COREITEM4,
  children: [{
    title: '标准模板库',
    desc: '标准实施工作模板库管理与使用'
  },{
    title: '标准解读与学习',
    desc: '标准专家专业化标准条款讲解，深度掌握标准实施与应用'
  },{
    title: '资料共享库',
    desc: '企业内部管理、学习资源共享平台'
  }]
},{
  name: '数据统计与应用分析',
  icon: COREITEM5,
  children: [{
    title: '图形化分析与报表',
    desc: '依据标准常规使用使用形式专业化数据分析模型与数据分析报表，多维度分析企业标准使用情况与迭代更新'
  }]
}]
const currentCoreIndex = ref(0)
const openOrder = ref(false)

const changeCore = (index: number) => {
  currentCoreIndex.value = index
}
const handleOrder = () => {
  openOrder.value = true
}

</script>
<style lang="scss" scoped>
.esms-header {
  height: 380px;
  background: url("@/assets/images/solution/esms-header-bg.png") no-repeat center;
  background-size: 100% 100%;
  .esms-header-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    font-size: 16px;
    color: #333333;
    .title {
      margin: 75px 0 25px 0;
      font-weight: bold;
      font-size: 32px;
    }
    .desc{
      width: 638px;
      font-size: 16px;
      color: #333333;
      line-height: 26px;
    }
    .order-btn{
      margin-top: 20px;
      width: 240px;
      height: 40px;
      background: $primary-color;
      border-radius: 20px;
      font-size: 16px;
      color: #FFFFFF;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      &:hover{
        background: $primary-hover-color;
      }
    }
    .tip{
      margin-top: 15px;
      font-size: 14px;
      color: #888888;
    }
  }
}
.v-title{
  padding-top: 65px;
  font-weight: bold;
  font-size: 26px;
  color: #333333;
  text-align: center;
}
.version-wrap{
  .version-list{
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    margin-bottom: 75px;
    gap: 45px;
    .version-item{
      flex: 1;
      height: 400px;
      padding: 25px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .item-title{
        margin-top: 15px;
        font-weight: bold;
        font-size: 20px;
        color: #333333;
      }
      .item-desc{
        margin-top: 15px;
        font-size: 16px;
        color: #888888;
      }
      .item-list{
        margin-top: 70px;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .item{
          display: flex;
          align-items: center;
          img{
            width: 16px;
            height: 16px;
            margin-top: 3px;
            align-self: flex-start;
          }
          div{
            margin-left: 10px;
            font-size: 14px;
            color: #333333;
            line-height: 24px;
          }
        }
        
      }
    }
    .version-item-bg0{
      background: url("@/assets/images/solution/version-item-bg-0.png") no-repeat center;
      background-size: 100% 100%;
    }
    .version-item-bg1{
      background: url("@/assets/images/solution/version-item-bg-1.png") no-repeat center;
      background-size: 100% 100%;
    }
    .version-item-bg2{
      background: url("@/assets/images/solution/version-item-bg-2.png") no-repeat center;
      background-size: 100% 100%;
    }
  }
}
.advantage-wrap{
  height: 544px;
  box-sizing: border-box;
  background: url("@/assets/images/solution/advantage-bg.png") no-repeat center;
  background-size: 100% 100%;
  .advantage-list{
    margin-top: 45px;
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    .advantage-item{
      width: 585px;
      height: 150px;
      background: #FFFFFF;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px;
      box-sizing: border-box;
      &:hover{
        box-shadow: 0px 0px 8px 1px rgba(0,31,89,0.19);
        .item-container{
          .item-title{
            color: $primary-color;
          }
        }
      }
      img{
        width: 59px;
      }
      .item-container{
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        .item-title{
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        .item-desc{
          margin-top: 15px;
          font-size: 14px;
          color: #888888;
          line-height: 22px;
        }
      }
    }
  }
}
.core-wrap{
  .core-menu-list{
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    gap: 20px;
    .core-menu-item{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      &:hover,&.actived{
        .menu-name{
          font-weight: bold;
          color: $primary-color;
        }
      }

      img{
        height: 86px;
      }
      .menu-name{
        font-size: 16px;
        color: #333333;
      }
    }
  }
  .item-list{
    margin-top: 40px;
    display: flex;
    flex-wrap: wrap;
    gap:35px;
    height: 360px;
    justify-content: flex-start;
    align-items: flex-start;
    align-content: flex-start;
    margin-bottom: 65px;
    .item{
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 582px;
      height: 100px;
      background: #F8F9FB;
      box-sizing: border-box;
      padding: 0px 20px;
      .title{
        font-size: 16px;
        color: #333333;
      }
      .desc{
        margin-top: 10px;
        font-size: 14px;
        color: #888888;
        line-height: 20px;
      }
    }
  }
}
</style>