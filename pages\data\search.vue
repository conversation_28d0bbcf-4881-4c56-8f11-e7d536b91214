<template>
  <div>
    <div class="banner">
      <div class="main-wrap">
        <div class="f-32 c-33 f-bold">标准查新</div>
        <ClientOnly>
          <template v-if="type == 'search'">
            <el-input
              v-model="keyword"
              :rows="3"
              :maxlength="300"
              type="textarea"
              placeholder="请输入完整且正确标准号，多个查新标准时，请换行输入。示例:
GB/T 213798-2025
GB/T 16739-2021"
              class="mt-20"
            />
            <div class="flex flex-ai-center mt-30">
              <div @click="handleSearchData" class="banner-btn pointer">开始查新</div>
              <span class="iconfont icon-piliangcaozuo c-primary f-16 ml-15"></span>
              <span @click="handleSearchType('upload')" class="f-14 text-underline c-primary ml-5 pointer">批量查新</span>
            </div>
          </template>
          <template v-else>
            <el-upload
              ref="uploadRef"
              v-model:file-list="fileList"
              :limit="1"
              :before-upload="handleBeforeUpload"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :on-error="handleFileError"
              :auto-upload="true"
              :show-file-list="false"
              method="post"
              class="mt-25"
            >
              <div class="upload w-630">
                <span class="iconfont icon-shangchuanshuju f-46 c-primary text-center" style="display: block"></span>
                <div class="mt-10 f-14 c-33 text-center">上传查新数据</div>
                <div class="mt-10 f-14 c-88 text-center">请按批量查新导入模块说明，正确填写查新数据，并上传文件</div>
              </div>
            </el-upload>
            <div class="flex flex-center mt-20 w-630">
              <span class="iconfont icon-xiazaimoban c-primary f-16"></span>
              <span @click="handleDownLoad" class="f-14 text-underline c-primary ml-5 pointer">下载查新模版</span>
              <span class="iconfont icon-SX_023 c-primary f-16 ml-40"></span>
              <span @click="handleSearchType('search')" class="f-14 text-underline c-primary ml-5 pointer">手动查新</span>
            </div>
          </template>
        </ClientOnly>
      </div>
    </div>
    <div class="content main-wrap">
      <DataSearchTable v-if="isShow" :info="info" :tableData="tableData" />
      <div class="f-26 f-bold c-33 text-center">标准查新</div>
      <div class="f-14 c-88 text-center mw-1000">
        标准查新作为一种在科研、产品开发、技术创新及知识产权保护等领域广泛应用的工具，旨在帮助用户快速、准确的检索并确认某项技术、产品、方法的已知执行标准的有效性，避免因为使用标准有效性问题，造成用户在设计、生产、执行等领域的问题。
      </div>
      <div class="flex flex-ai-center flex-sa mt-40">
        <div>
          <img src="@/assets/images/search/search-1.png" alt="" class="content-img" />
          <div class="content-title">全量标准数据</div>
        </div>
        <div>
          <img src="@/assets/images/search/search-2.png" alt="" class="content-img" />
          <div class="content-title">状态实时跟踪</div>
        </div>
        <div>
          <img src="@/assets/images/search/search-3.png" alt="" class="content-img" />
          <div class="content-title">智能精准匹配</div>
        </div>
        <div>
          <img src="@/assets/images/search/search-4.png" alt="" class="content-img" />
          <div class="content-title">全链修订关系</div>
        </div>
      </div>
      <div class="f-26 c-33 f-bold text-center mt-60">如何进行查新</div>
      <div class="flex flex-ai-center flex-sb mt-50">
        <div class="content-card">
          <img src="@/assets/images/search/search-5.png" alt="" class="content-card-img" />
          <div class="content-card-item">
            <div class="f-16 c-33 f-bold">手动查新</div>
            <div class="f-14 c-88 mt-15 lh-24">
              在查新文本框中按输入完整且正确的标准号，多个查新标准时，采用换行输入方式，输入完成后，执行查新操作，即可展示数据查新结果
            </div>
          </div>
        </div>
        <div class="content-card">
          <img src="@/assets/images/search/search-6.png" alt="" class="content-card-img" />
          <div class="content-card-item">
            <div class="f-16 c-33 f-bold">批量查新</div>
            <div class="f-14 c-88 mt-15 lh-24">
              下载查新模板，按模板表格中的要求说明填写完整且正确的标准号，填写完成后保存并上传模板文本，系统将自动执行查新操作，并展示数据查新结果
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准查新_标准查新服务_标准更新报告_标信查平台',
    meta: [
      { name: 'keywords', content: '标准查新，标准查新服务，查新检索服务，标准查新报告' },
      {
        name: 'description',
        content:
          '标信查平台数据服务频道，可以为企业提供标准托管、标准查新、标准情报、标准灯塔、标准战略分析等多种标准信息化服务，助力广大企业更好发展。',
      },
    ],
  });

  import { useUserStore } from '@/store/userStore';
  import { type IStandardInfo, type IResponseData } from '@/types';
  import type { UploadInstance } from 'element-plus';

  interface InfoType {
    count: string | number;
    onCount: string | number;
    nonCount: string | number;
    tobeCount: string | number;
    activeCount: string | number;
    beReplacedCount: string | number;
    abolishCount: string | number;
  }

  const { $modal } = useNuxtApp();
  const userStore = useUserStore();
  const config = useRuntimeConfig();

  let downloadLoadingInstance: any;
  const uploadRef = ref<UploadInstance>();
  const type = ref('search');
  const keyword = ref('');
  const upload = ref({
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + userStore.token },
    // 上传的地址
    url: config.public.baseURL + '/search/sdc/stdStandard/importNoveltySearch',
  });
  const fileSize = ref(100);
  const fileType = ref(['xlsx', 'xls']);
  const fileList = ref([]);
  const isShow = ref(false);
  const info = ref<InfoType>({
    count: '',
    onCount: '',
    nonCount: '',
    tobeCount: '',
    activeCount: '',
    beReplacedCount: '',
    abolishCount: '',
  });
  const tableData = ref<IStandardInfo[]>([]);

  const handleSearchData = async () => {
    if (!keyword.value) return;
    isShow.value = false;
    let { data, code } = <IResponseData>await useHttp.get('/search/sdc/stdStandard/noveltySearch', { search: keyword.value });
    if (code == 200) {
      info.value = data;
      tableData.value = data.list;
      isShow.value = true;
    } else {
      isShow.value = false;
    }
  };

  const handleSearchType = (data: string) => {
    type.value = data;
    keyword.value = '';
    info.value = {
      count: '',
      onCount: '',
      nonCount: '',
      tobeCount: '',
      activeCount: '',
      beReplacedCount: '',
      abolishCount: '',
    };
    tableData.value = [];
    isShow.value = false;
  };

  const handleDownLoad = () => {
    window.location.href = '/file/标准查新批量导入模板.xlsx';
  };

  const handleBeforeUpload = (file: any) => {
    let isValid = false;
    if (fileType.value.length) {
      let fileExtension = '';
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
      }
      isValid = fileType.value.some(type => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
    } else {
      isValid = file.type.indexOf('image') > -1;
    }
    if (!isValid) {
      $modal.msgError(`文件格式不正确, 请上传${fileType.value.join('/')}格式文件!`);
      return false;
    }
    if (fileSize.value) {
      const isLt = file.size / 1024 / 1024 < fileSize.value;
      if (!isLt) {
        $modal.msgError(`上传文件大小不能超过 ${fileSize.value} MB!`);
        return false;
      }
    }
    return true;
  };

  const handleFileUploadProgress = () => {
    tableData.value = [];
    isShow.value = false;
    upload.value.isUploading = true;
    downloadLoadingInstance = ElLoading.service({ text: '正在上传数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  };

  const handleFileSuccess = (res: any) => {
    upload.value.isUploading = false;
    uploadRef.value!.clearFiles();
    downloadLoadingInstance.close();
    if (res.code == 200) {
      info.value = res.data;
      tableData.value = res.data.list;
      isShow.value = true;
    } else {
      $modal.msgError(res.message);
    }
  };

  const handleFileError = (error: any) => {
    $modal.msgError('上传失败, 请重试');
    upload.value.isUploading = false;
    downloadLoadingInstance.close();
  };
</script>

<style lang="scss" scoped>
  .banner {
    width: 100%;
    min-height: 380px;
    background: url('@/assets/images/search/cover.png') no-repeat center;
    background-size: 100% 100%;

    .main-wrap {
      padding-top: 100px;
    }

    &-btn {
      width: 240px;
      height: 40px;
      background-color: #045cff;
      border-radius: 20px;
      font-size: 16px;
      color: #fff;
      text-align: center;
      line-height: 40px;
    }
  }

  .upload {
    padding: 15px 0 20px;
    box-sizing: border-box;
    background-color: #ffffff;
  }

  .content {
    padding: 60px 0 65px;
    box-sizing: border-box;

    &-img {
      display: block;
      width: 128px;
    }

    &-title {
      font-size: 16px;
      color: #333;
      text-align: center;
      margin-top: 20px;
    }

    &-card {
      width: 578px;
      background: #ffffff;
      border-radius: 3px;
      border: 1px solid #e8e8e8;

      &-img {
        display: block;
        width: 100%;
      }

      &-item {
        padding: 20px 25px;
        box-sizing: border-box;
      }
    }
  }

  .w-630 {
    width: 630px;
  }

  .mw-1000 {
    max-width: 1000px;
    line-height: 22px;
    margin: 15px auto 0;
  }

  .f-46 {
    font-size: 46px;
  }

  :deep(.el-textarea__inner) {
    width: 630px;
    border: none;
    box-shadow: none;
    resize: none;
    padding: 18px 20px;
    box-sizing: border-box;
  }

  :deep(.el-textarea__inner::-webkit-scrollbar) {
    display: none;
  }
</style>
