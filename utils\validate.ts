export const mobileValidPattern = /(^1[0-9]{10}$)/
export const telValidPattern = /(^0\d{2,3}\-?\d{7,8}$)|(^\d{2,3}\-?\d{3,4}\-?\d{3,4}$)/
export const telMobileValidPattern = /(^0\d{2,3}\-?\d{7,8}$)|(^\d{2,3}\-?\d{3,4}\-?\d{3,4}$)|(^1[0-9]{10}$)/
// 长度应在6～20位，须包含字母、数字、特殊符号至少两种，且非特殊符号开头，不能包含空格
export const passwordPattern =  /(?=.*[A-Za-z])^[a-zA-Z0-9](?=.*[\d!@#$%&*_+\?.<>:;])[A-Za-z\d!@#$%&*_+\?.<>:;]{5,19}(?!.*\s)$/
// 长度应在4～20位，可包含字母、数字、特殊符号，且非特殊符号开头
export const userNamePattern = /^(?![!@#$%^&*_+\?.<>:;])[a-zA-Z0-9!@#$%^&*_+\?.<>:;]{4,20}$/
// 长度应在2～20位，真实名称2-20个字母、汉字
export const realNamePattern = /^([a-zA-Z\u4e00-\u9fff]{2,20})$/