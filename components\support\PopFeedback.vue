<template>
  <div class="pop-container">
    <el-dialog :append-to-body="true" :lock-scroll="false" v-model="open" width="600px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
        <el-form-item label="反馈类别" prop="feedbackType">
          <el-select
            style="width: 100%;"
            v-model="form.feedbackType"
            placeholder="请选择反馈类别"
            clearable
          >
            <el-option
              v-for="dict in feedbackTypeList"
              :key="dict['dictValue']"
              :label="dict['dictLabel']"
              :value="dict['dictValue']"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈内容" prop="feedbackContent">
          <el-input
            v-model="form.feedbackContent"
            type="textarea"
            :rows="5"
            placeholder="请输入反馈说明信息"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="附件" prop="feedbackFilesList">
          <BxcUploadImage
            :responseFn="handleResponse"
            :limit="9"
            :multiple="true"
            :fileSize="5"
            :fileType="fileType"
            v-model:value="form.feedbackFilesList"
          >
          </BxcUploadImage>
          <div style="width:100%;"></div>
          <span class="m-red lh-20 mt-10">支持格式：jpeg、jpg、png；单个文件大小不超过5MB；最多上传文件数：9</span>
        </el-form-item>
        <el-form-item label="单位名称" prop="unitName">
          <el-input
            v-model="form.unitName"
            placeholder="请输入单位名称"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="联系人" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入联系人"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入联系方式"
            maxlength="20"
          />
        </el-form-item>

        <div class="tip-box mt-30">
          您也可以通过直接拨打我们的官方客服热线：<span class="mobile">************</span>，进行需求内容咨询。
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close" plain>关闭</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { addFeedback } from '@/api/support'
import { useUserStore } from '@/store/userStore';
import { getDicts } from '@/api/common'
import type { IUserInfo } from '@/types';

const userStore = useUserStore()
const { $modal } = useNuxtApp()

const props = defineProps({
  open: Boolean,
});
const { open } = toRefs(props)

const title = ref('服务反馈')
const loading = ref(false)
const fileType = ref(['jpeg','jpg','png'])
const formRef = ref()
const feedbackTypeList = ref([])
const userInfo = <IUserInfo>userStore.userInfo

const form = ref({
  feedbackType: undefined,
  feedbackContent: '',
  unitName: userInfo.unitName || '',
  name: userInfo.realName || '',
  phone: userStore.phonenumber || '',
  feedbackFilesList: []
});
const rules = ref({
  feedbackType: [{ required: true, message: '请选择反馈类别', trigger: 'change' }],
  feedbackContent: [{ required: true, message: '请输入需求说明', trigger: 'blur' }],
  name: [
    { required: true, message: '请输入联系人名称', trigger: 'blur' },
    { min: 2, max: 20, message: "联系人名称长度必须介于 2 和 20 之间", trigger: "blur" },
    { pattern: realNamePattern, message: "联系人名称2-20个字母、汉字", trigger: "blur" },
  ],
  phone: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: telMobileValidPattern, message: '请输入正确的联系方式（手机号/座机号）', trigger: 'blur' },
  ],
});

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const handleResponse = (response: any, file: any, fileList: any) => {
  return {'id': response.data.id, 'url': response.data.url, 'name':response.data.name}
}
const save = () => {
  formRef?.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      addFeedback(form.value).then(response => {
        emit('update:open',false);
        emit('success');
        $modal.msgSuccess("您所提交的反馈信息已收到，我们将有专员进行处理！");
      }).finally(() => {
        loading.value = false;
      });
    }
  });
}
const getFeedbackType = () => {
  getDicts('bxc_serve_feedback_type').then((res: any) => {
    feedbackTypeList.value = res.data
  })
}

getFeedbackType()

</script>