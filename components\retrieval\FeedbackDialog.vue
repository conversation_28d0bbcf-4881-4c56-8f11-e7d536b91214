<template>
  <el-dialog
    width="620"
    title="数据报错"
    v-model="props.visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" :label-position="'top'" :rules="rules">
      <el-form-item v-if="props.type != 1" label="错误类型" prop="errorType">
        <el-radio-group v-model="form.errorType">
          <el-radio-button v-for="item in errorTypeOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
            {{ item.dictLabel }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="错误说明" prop="errorDes">
        <el-input
          v-model="form.errorDes"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入错误位置信息说明"
        />
      </el-form-item>
      <el-form-item label="附件" prop="errorFileList">
        <div>
          <BxcUploadImage
            v-model:value="form.errorFileList"
            :fileSize="5"
            :multiple="true"
            :fileType="['jpeg', 'jpg', 'png', 'bmp']"
            :responseFn="handleResponse"
            @success="handleUpload('errorFileList', formRef)"
          />
          <div class="c-99 mt-10 flex flex-ai-center f-14">
            <el-icon class="f-16 mr-5"><Warning /></el-icon>
            支持文件格式：jpeg、jpg、png、bmp；单张图片大小不超过5MB；最多上传9个
          </div>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="tip">
          我们目前还不够完善，每一步的成长都需要来自您及和您一样热心人士的帮扶！感谢您为我们指正错误，我们将砥砺前行！
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex flex-jc-end">
        <el-button type="primary" plain @click="handleClose">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleConfirm(formRef)">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { getDicts } from '@/api/common';
  import type { FormInstance } from 'element-plus';
  import { type IResponseData, type IDicts } from '@/types';

  const { $modal } = useNuxtApp();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    type: {
      required: true,
      type: [Number, String],
    },
  });

  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const errorTypeOptions = ref<IDicts[]>([]);
  const form = ref<{
    relationId: string | number;
    errorType: string | number;
    dataType: string | number;
    errorDes: string;
    errorFileList: File[];
  }>({
    relationId: '',
    errorType: '0',
    errorDes: '',
    dataType: '',
    errorFileList: [],
  });
  const rules = reactive({
    errorType: [{ required: true, message: '请选择错误类型', trigger: 'change' }],
    errorDes: [{ required: true, message: '请输入错误位置信息说明', trigger: 'blur' }],
  });

  let { data } = <IResponseData>await getDicts('bxc_data_error_type');
  errorTypeOptions.value = data;

  const handleResponse = (response: any) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleUpload = (type: string, formEl: FormInstance | undefined) => {
    if (!formEl) return;
    nextTick(() => {
      formEl.validateField(type);
    });
  };

  const handleConfirm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    loading.value = true;
    formEl.validate(valid => {
      if (valid) {
        form.value.relationId = props.data?.id;
        form.value.dataType = props.type;
        useHttp
          .post('/business/errorManage', form.value)
          .then(res => {
            $modal.msgSuccess('您的报错信息已提交，我们将有专人负责修正！');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible']);
</script>

<style lang="scss" scoped>
  .tip {
    width: 100%;
    font-size: 14px;
    line-height: 20px;
    padding: 13px 20px;
    box-sizing: border-box;
    color: #888888;
    background-color: #f8f9fb;
  }

  :deep(.el-radio-button) {
    &:nth-child(n + 2) {
      margin-left: 20px;
    }
  }

  :deep(.el-radio-button__inner) {
    border-radius: var(--el-border-radius-base) !important;
    border: var(--el-border);
  }

  :deep(.el-radio-button__inner:hover) {
    border-color: var(--el-color-primary) !important;
  }
</style>
