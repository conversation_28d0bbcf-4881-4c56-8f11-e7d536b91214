import { defineStore } from 'pinia';
import { type IResponseData, type IUserInfo } from '@/types';
import { login, mobileLogin, getUserInfo, logout } from '@/api/login';
import { useCookie } from 'nuxt/app';
import Avatar from '@/assets/images/layout/avatar.png';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    userInfo: {},
    unReadCount: 0,
    showApplication: false,
  }),
  getters: {
    userId: state => (<IUserInfo>state.userInfo).userId,
    userName: state => (<IUserInfo>state.userInfo).userName,
    nickName: state => (<IUserInfo>state.userInfo).nickName,
    avatar: state => ((<IUserInfo>state.userInfo).avatar ? (<IUserInfo>state.userInfo).avatar : Avatar),
    phonenumber: state => useAes().decrypt((<IUserInfo>state.userInfo).phonenumber),
    email: state => (<IUserInfo>state.userInfo).email,
    membershipType: state => (<IUserInfo>state.userInfo).membershipType,
    enterpriseId: state => (<IUserInfo>state.userInfo).enterpriseId,
    validityPeriodDate: state => (<IUserInfo>state.userInfo).validityPeriodDate,
  },
  actions: {
    // 初始化用户数据（从 cookie 中读取）
    initUserData() {
      if (import.meta.client) {
        const tokenCookie = useCookie('token');
        const userInfoCookie = useCookie('userInfo');

        if (tokenCookie.value) {
          this.setToken(tokenCookie.value);
        }
        if (userInfoCookie.value) {
          try {
            const userInfo = JSON.parse(userInfoCookie.value) as IUserInfo;
            this.setUserInfo(userInfo);
          } catch (e) {
            this.setUserInfo({} as IUserInfo);
          }
        }
      }
    },
    // 账号登录
    login(params: any) {
      const username = params.username.trim();
      const password = params.password;
      return new Promise((resolve, reject) => {
        login({ username, password })
          .then(res => {
            const { data } = res as IResponseData;

            if (data.accessToken) {
              this.setToken(data.accessToken);

              this.getUserInfo()
                .then(res => {
                  resolve(res);
                })
                .catch(error => {
                  this.resetData();
                  reject(error);
                });
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 手机登录
    mobileLogin(params: any) {
      const phonenumber = params.phonenumber.trim();
      const smsCode = params.smsCode;
      return new Promise((resolve, reject) => {
        mobileLogin({ phonenumber, smsCode })
          .then(res => {
            const { data } = res as IResponseData;

            if (data.accessToken) {
              this.setToken(data.accessToken);

              this.getUserInfo()
                .then(res => {
                  resolve(res);
                })
                .catch(error => {
                  this.resetData();
                  reject(error);
                });
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getUserInfo() {
      return new Promise((resolve, reject) => {
        getUserInfo()
          .then(res => {
            const { code, msg, data } = res as IResponseData;
            if (code == 200) {
              if (data.user) {
                this.setUserInfo(data.user);
              }
              resolve(res);
            } else {
              reject(msg);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 退出登录
    logout() {
      return new Promise(async (resolve, reject) => {
        if (!this.token) {
          resolve('');
        }
        try {
          await logout();
        } catch (error) {
        } finally {
          this.resetData();
        }

        resolve('退出成功');
      });
    },
    setToken(token: string) {
      this.token = token;
      useCookie('token').value = token;
    },
    setUserInfo(userInfo: IUserInfo) {
      this.userInfo = userInfo;
      useCookie('userInfo').value = JSON.stringify(userInfo);
    },
    resetData() {
      this.token = '';
      this.userInfo = {};
      useCookie('token').value = null;
      useCookie('userInfo').value = null;
    },
    setUnReadCount(val: number) {
      this.unReadCount = val;
    },
    setShowApplication(val: boolean) {
      this.showApplication = val;
    },
  },
});
