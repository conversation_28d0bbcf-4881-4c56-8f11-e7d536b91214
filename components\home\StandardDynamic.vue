<template>
  <div class="dynamic-wrap main-wrap">
    <h3 class="title">标准动态</h3>
    <div class="dynamic-container">
      <div class="dynamic-list">
        <div v-for="item in dynamicList" :key="item.type" class="dynamic-item">
          <div class="d-title">{{item.title}}</div>
          <ul v-if="item.list && item.list.length > 0" class="s-list">
            <li v-for="row in item.list" :key="row.id" @click="handleDetail(row)" class="s-row">
              <div class="s-title-wrap">
                <div class="s-title overflow-ellipsis">{{row.standardCode}}</div>
                <div v-if="item.type == '0'" class="s-date">{{row.publishDate}}</div>
                <div v-if="item.type == '2'" class="s-date">{{row.executeDate}}</div>
                <div v-if="item.type == '4'" class="s-date">{{row.repealDate}}</div>
              </div>
              <div class="s-desc overflow-ellipsis">
                {{row.standardName}}
              </div>
            </li>
          </ul>
          <div v-else class="s-list">
            <BxcEmpty class="mt-30" />
          </div>
          <div class="d-more" @click="handleMore(item.type)">
            <span>了解更多<el-icon class="ml-5"><Right /></el-icon></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getStandardDynamicList } from '@/api/home'
import type { IStandardInfo } from '@/types'

const dynamicList = ref([{
  type: '0',
  title: '最新发布标准',
  list: <IStandardInfo[]>[]
},{
  type: '2',
  title: '最新实施标准',
  list: <IStandardInfo[]>[]
},{
  type: '4',
  title: '最新废止标准',
  list: <IStandardInfo[]>[]
}])
const queryParams = ref({
  pageNum: 1,
  pageSize: 5,
  queryDynamicType: '0'
})
const getList = async (type: string) => {
  try {
    queryParams.value.queryDynamicType = type
    const res: any = await getStandardDynamicList(queryParams.value)
    dynamicList.value.forEach((item: any) => {
      if(item.type == type) {
        item.list = res.rows || []
      }
    })
  } catch (error) {}
}
const handleDetail = (row: any) => {
  navigateTo(`/retrieval/domesticDetail?id=${row.id}`)
}
const handleMore = (type: string) => {
  navigateTo('/data/dynamic?type=' + type)
}

await getList('0')
await getList('2')
await getList('4')
</script>
<style lang="scss" scoped>
.dynamic-wrap {
  margin-top: 70px;
  .dynamic-list {
    display: flex;
    justify-content: space-between;
    gap: 25px;
    .dynamic-item {
      flex: 1;
      height: 515px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      box-shadow: 0px 0px 8px 1px rgba(0, 0, 0, 0.1);
      .d-title {
        height: 70px;
        line-height: 70px;
        font-size: 18px;
        color: #ffffff;
        font-weight: bold;
        background: url("@/assets/images/home/<USER>") no-repeat
          center;
        background-size: 100% 100%;
        text-align: center;
      }
      .s-list {
        height: 400px;
        padding: 0px 20px;
        box-sizing: border-box;
        background: #ffffff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .s-row {
          height: 80px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          overflow: hidden;
          border-bottom: 1px solid #e8e8e8;
          &:last-child {
            border-bottom: none;
          }
          &:hover {
            cursor: pointer;
            .s-title-wrap .s-title,.s-title-wrap .s-date,.s-desc{
              color: $primary-color;
            }
          }
          .s-title-wrap{
            display: flex;
            justify-content: space-between;
            .s-title {
              font-size: 16px;
              color: #333333;
              font-weight: bold;
            }
            .s-date {
              margin-left: 10px;
              flex-shrink: 0;
              font-size: 14px;
              color: #888888;
            }
          }
          .s-desc {
            margin-top: 10px;
            font-size: 14px;
            color: #888888;
          }
        }
      }
      .d-more {
        margin-top: 10px;
        font-size: 14px;
        color: $primary-color;
        padding-right: 20px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        span{
          display: flex;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
