<template>
  <div class="container mt-60">
    <div class="main-wrap flex flex-sb">
      <div class="container-left">
        <RetrievalDetailIntro :form="form" />
        <RetrievalDetailInfo :form="form" class="mt-30" />
        <template v-if="form.replaceStdList && form.replaceStdList.length > 0">
          <div class="f-20 f-bold mt-30">替代标准</div>
          <div class="flex flex-wrap">
            <div
              @click="handleClick('/retrieval/internationDetail', { id: item.id })"
              v-for="(item, index) in form.replaceStdList"
              :key="index"
              class="f-14 c-33 mt-20 mr-10"
              :class="{ valid_link: item.id }"
            >
              {{ item.standardCode || '-' }} | {{ item.standardName || '-' }}
            </div>
          </div>
        </template>
        <template v-if="form.beReplacedStdList && form.beReplacedStdList.length > 0">
          <div class="f-20 f-bold mt-40">被替代标准</div>
          <div class="flex flex-wrap">
            <div
              @click="handleClick('/retrieval/internationDetail', { id: item.id })"
              v-for="(item, index) in form.beReplacedStdList"
              :key="index"
              class="f-14 c-33 mt-20 mr-10"
              :class="{ valid_link: item.id }"
            >
              {{ item.standardCode || '-' }} | {{ item.standardName || '-' }}
            </div>
          </div>
        </template>
      </div>
      <div class="container-right">
        <div class="container-right-title">推荐</div>
        <div class="container-right-card">
          <template v-if="tableData.length > 0">
            <div
              @click="handleJump('/retrieval/internationDetail', { id: item.id })"
              v-for="item in tableData"
              :key="item.id"
              class="container-right-card-item"
            >
              <div class="flex flex-ai-center" style="width: 100%">
                <span :class="getStatusColor(statusToString(item.standardStatus), 'text')">
                  【{{ item.standardStatusName }}】
                </span>
                <span class="flex-1 overflow-ellipsis container-right-card-item-title">{{ item.standardCode }}</span>
              </div>
              <div class="mt-10 c-88 overflow-ellipsis" style="width: 100%">&nbsp;{{ item.standardName }}</div>
            </div>
          </template>
          <BxcEmpty v-else />
        </div>
      </div>
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :type="1" @updateData="getDetail" />
  </div>
</template>

<script setup lang="ts">
  import { getInternationDetail, getRecommendedList } from '@/api/retrieval/internation';
  import { splitStrToArray, getStatusColor, handleJump } from '@/utils/common';
  import { type IStandardInfo, type IResponseData } from '@/types';

  const route = useRoute();

  const form = ref<IStandardInfo>({
    standardCode: '',
    standardName: '',
    standardType: '',
    standardStatus: '',
    standardTypeName: '',
    publishDate: '',
    executeDate: '',
    standardNameEn: '',
    repealDate: '',
    standardTypeCodeGbName: '',
    standardTypeCodeIsoName: '',
  });
  const tableData = ref<IStandardInfo[]>([]);

  let { data } = <IResponseData>await getInternationDetail(route.query.id as string | number);
  form.value = data || {};

  useHead({
    title: form.value.standardCode + ' | ' + form.value.standardName,
  });

  let recommendedData = <IResponseData>await getRecommendedList(route.query.id as string | number);
  tableData.value = recommendedData.data || [];

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/search/sdc/stdStandardForeign/' + route.query.id);
    form.value = data || {};
  };

  const handleClick = (url: string, query?: Record<string, string>) => {
    if (query?.id) handleJump(url, query);
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;

    &-left {
      width: 800px;
      overflow: hidden;
    }

    &-right {
      width: 360px;
      overflow: hidden;

      &-icon {
        display: block;
        width: 100%;
        margin-bottom: 20px;
      }

      &-title {
        font-size: 20px;
        color: #fff;
        width: 100%;
        height: 55px;
        line-height: 55px;
        background-color: #f2a511;
        padding: 0 20px;
        box-sizing: border-box;
      }

      &-card {
        padding: 10px 18px;
        box-sizing: border-box;
        border: 1px solid #e8e8e8;

        &-item {
          font-size: 14px;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: self-start;
          overflow: hidden;
          cursor: pointer;

          &:hover &-title {
            color: $primary-color;
          }

          &:not(:first-child) {
            border-top: 1px solid #e5e8ef;
          }
        }
      }
    }
  }
</style>
