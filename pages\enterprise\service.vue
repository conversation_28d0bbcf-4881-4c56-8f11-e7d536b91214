<template>
  <div class="service-wrap">
    <div class="service-header">
      <div class="service-header-container main-wrap">
        <div class="title">企业标准服务</div>
        <div class="desc">
          标准专家将根据企业服务需求与企业特性，为企业提供全过程的的标准信息服务
        </div>
      </div>
    </div>
    <div class="service-container main-wrap">
      <div class="service-list">
        <div v-for="item in serviceList" :key="item.type" @click="handleDetail(item)" class="service-item">
          <div class="item-header" :style="{background:'url('+item.icon+')','background-size': '100% 100%'}">
            <span>{{item.name}}</span>
          </div>
          <div class="item-desc">
            {{item.desc}}
          </div>
          <div class="item-op" @click.stop="handleApply(item)">
            <span>申请服务</span><el-icon><DArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>
    <!-- 申请服务 -->
    <EnterprisePopApply v-if="open" v-model:open="open" :item="currentItem" />
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleApply(currentItem)"/>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from '@/store/userStore'

const { serviceList } = useEnterpriseService()
const router = useRouter()
const userStore = useUserStore()
const open = ref(false)
const openLogin = ref(false)
const currentItem = ref({})

const handleDetail = (item: any) => {
  router.push({
    path: `/enterprise/${item.type}-service`
  })
}
const handleApply = (item: any) => {
  if(!userStore.token){
    openLogin.value = true
    return
  }else{
    openLogin.value = false
    currentItem.value = item
    open.value = true
  }
}
</script>
<style lang="scss" scoped>
.service-header {
  height: 380px;
  background: url("@/assets/images/enterprise/service-header-bg.png") no-repeat center;
  background-size: 100% 100%;
  .service-header-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    font-size: 16px;
    color: #333333;
    .title {
      margin: 55px 0 25px 0;
      font-weight: bold;
      font-size: 32px;
    }
  }
}
.service-container{
  .service-list{
    margin: 30px 0px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 25px;
    .service-item{
      width: calc((1200px - 50px)/3);
      height: 250px;
      display: flex;
      flex-direction: column;
      background: #F8F9FB;
      border-radius: 5px;
      position: relative;
      &:hover{
        box-shadow: 0px 0px 15px 1px rgba(16,31,61,0.23);
        cursor: pointer;
        .item-header{
          color: $primary-color;
        }
        .item-op{
          background: $primary-color;
          color: #FFFFFF
        }
      }

      .item-header{
        font-weight: bold;
        font-size: 18px;
        color: #333333;
        height: 90px;
        line-height: 90px;
        padding-left: 20px;
        box-sizing: border-box;
      }
      .item-desc{
        height: 50px;
        font-size: 14px;
        color: #888888;
        line-height: 22px;
        padding: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        line-clamp: 3;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .item-op{
        width: 98px;
        height: 28px;
        background: #FFFFFF;
        border-radius: 3px;
        font-size: 14px;
        color: $primary-color;
        border: 1px solid $primary-color;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 20px;
        position: absolute;
        left: 0px;
        bottom: 20px;
        cursor: pointer;
      }
    }
  }
}
</style>
