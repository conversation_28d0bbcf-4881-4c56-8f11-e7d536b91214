<template>
  <div class="services-card">
    <div v-for="item in row" :key="item.id" class="membership-item-subitem">
      <img
        v-if="item.number === '无' || item.number === '有'"
        :src="item.number === '无' ? none : have"
        alt=""
        class="membership-item-icon"
      />
      <span v-else>{{ item.number }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { IServiceItem } from '@/types';
  import none from '@/assets/images/vip/none.png';
  import have from '@/assets/images/vip/have.png';

  const props = withDefaults(
    defineProps<{
      row: IServiceItem[];
    }>(),
    {
      row: () => [],
    }
  );

  const { row } = toRefs(props);
</script>

<style scoped lang="scss">
  .membership {
    &-item {
      &-subitem {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #333;
        height: 54px;
        border: 1px solid #ddecf8;
        box-sizing: border-box;
        border-left: none;
        border-top: none !important;
      }

      &-icon {
        width: 26px;
      }
    }
  }
</style>
