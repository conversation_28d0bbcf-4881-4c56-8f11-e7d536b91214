<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" inline class="form-inline">
      <div class="h-title one-column">标准化组织投入情况</div>
      <div class="one-column-content mt-20">
        <span style="color: var(--el-color-danger)">*&nbsp;</span>
        <div>是否设置标准化部门</div>
        <el-form-item prop="isSetStandardizationDept">
          <el-select v-model="form.isSetStandardizationDept" placeholder="请选择" clearable>
            <el-option
              v-for="item in isSetStandardizationDeptOptions"
              :key="item['dictValue']"
              :label="item['dictLabel']"
              :value="item['dictValue']"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="one-column-content">
        <span style="color: var(--el-color-danger)">*&nbsp;</span>
        <div>法定代表人标准化工作人员共</div>
        <el-form-item prop="standardizationStaffNum">
          <el-input v-model="form.standardizationStaffNum" maxlength="4" />
        </el-form-item>
        <div>人，其中，专职人员</div>
        <el-form-item prop="fullTimeStaffNum">
          <el-input v-model="form.fullTimeStaffNum" maxlength="4" />
        </el-form-item>
        <div>人，兼职人员</div>
        <el-form-item prop="partTimeStaffNum">
          <el-input v-model="form.partTimeStaffNum" maxlength="4" />
        </el-form-item>
        <div>人</div>
      </div>
      <div class="one-column-content">
        <span style="color: var(--el-color-danger)">*&nbsp;</span>
        <div>标准化工程师共</div>
        <el-form-item prop="standardizationEngineerNum">
          <el-input v-model="form.standardizationEngineerNum" maxlength="4" />
        </el-form-item>
        <div>人，其中，初级人员</div>
        <el-form-item prop="juniorEngineerNum">
          <el-input v-model="form.juniorEngineerNum" maxlength="4" />
        </el-form-item>
        <div>人，中级人员</div>
        <el-form-item prop="intermediateEngineerNum">
          <el-input v-model="form.intermediateEngineerNum" maxlength="4" />
        </el-form-item>
        <div>人</div>
      </div>
      <div class="one-column-content">
        <span style="color: var(--el-color-danger)">*&nbsp;</span>
        <div>标准化经费投入（年度）共计</div>
        <el-form-item prop="standardizationFunds">
          <el-input v-model="form.standardizationFunds" class="w-150" />
        </el-form-item>
        <div>万元，其中：标准化人员经费投入</div>
        <el-form-item prop="standardizationPersonnelFunds">
          <el-input v-model="form.standardizationPersonnelFunds" class="w-150" />
        </el-form-item>
        <div>万元，标准化项目（活动）经费投入</div>
        <el-form-item prop="standardizationProjectFunds">
          <el-input v-model="form.standardizationProjectFunds" class="w-150" />
        </el-form-item>
        <div>万元</div>
      </div>
      <div class="h-title one-column mt-10">标准化成果</div>
      <div class="one-column-content mt-20">
        <span style="color: var(--el-color-danger)">*&nbsp;</span>
        <div>参与标准化活动共</div>
        <el-form-item prop="standardizationActivityNum">
          <el-input v-model="form.standardizationActivityNum" maxlength="4" />
        </el-form-item>
        <div>项，其中，试点示范项目</div>
        <el-form-item prop="pilotDemonstrationProjectNum">
          <el-input v-model="form.pilotDemonstrationProjectNum" maxlength="4" />
        </el-form-item>
        <div>项，创新贡献奖</div>
        <el-form-item prop="innovationContributionAwardNum">
          <el-input v-model="form.innovationContributionAwardNum" maxlength="4" />
        </el-form-item>
        <div>项，企业标准领跑者</div>
        <el-form-item prop="enterpriseStandardLeaderNum">
          <el-input v-model="form.enterpriseStandardLeaderNum" maxlength="4" />
        </el-form-item>
        <div>项，对标达标产品</div>
        <el-form-item prop="benchmarkingStandardProductsNum">
          <el-input v-model="form.benchmarkingStandardProductsNum" maxlength="4" />
        </el-form-item>
        <div>项，其他</div>
        <el-form-item prop="otherStandardizationActivityNum">
          <el-input v-model="form.otherStandardizationActivityNum" maxlength="4" />
        </el-form-item>
        <div>项，其他项目名称</div>
        <el-form-item prop="otherProjectName">
          <el-input v-model="form.otherProjectName" placeholder="请输入项目名称" class="w-430" />
        </el-form-item>
      </div>
      <div class="one-column-content">
        <div>参加标准化技术组织名称</div>
        <el-form-item prop="standardizationTechnicalOrganizationName">
          <el-input v-model="form.standardizationTechnicalOrganizationName" placeholder="请输入" class="w-430" />
        </el-form-item>
        <div>参加人员（人）</div>
        <el-form-item prop="participantNum">
          <el-input v-model="form.participantNum" maxlength="4" class="w-430" />
        </el-form-item>
      </div>
      <div class="h-title mt-10 mb-20 one-column">标准化荣誉</div>
      <div v-if="form.honorList && form.honorList.length < 10" class="flex-1 flex flex-jc-end">
        <div @click="handleClick('add')" class="flex flex-ai-center f-14 c-primary pointer">
          <el-icon class="f-14 mr-5"><Plus /></el-icon>
          新增
        </div>
      </div>
      <el-form-item class="one-column">
        <el-table :data="form.honorList" class="mt-15">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column label="荣誉名称" prop="honorName" min-width="200" show-overflow-tooltip />
          <el-table-column label="获得日期" prop="obtainDate" min-width="150" show-overflow-tooltip />
          <el-table-column label="附件" min-width="120">
            <template #default="{ row }">
              <template v-if="row.honorFileList && row.honorFileList.length > 0">
                <div class="flex flex-jc-center">
                  <BxcUploadImage v-model:value="row.honorFileList" :isShowUploadIcon="false" />
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" show-overflow-tooltip min-width="100">
            <template #default="{ row, $index }">
              <el-button @click="handleClick('edit', row)" type="primary" link>编辑</el-button>
              <el-button @click="handleClick('delete', row, $index)" type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <div class="btn">
      <el-button @click="previousPage" :loading="loading" plain>上一页</el-button>
      <el-button @click="nextPage" :loading="loading" type="primary">下一页</el-button>
    </div>
    <QuestionnaireHonorDialog v-if="dialogVisible" v-model:visible="dialogVisible" :data="currentRow" @updateData="updateData" />
  </div>
</template>

<script setup lang="ts">
  import { checkValidity } from '@/api/questionnaire';
  import { getDicts } from '@/api/common';
  import { type IDicts } from '@/types';

  const { $modal } = useNuxtApp();
  const route = useRoute();
  const router = useRouter();

  interface FormValue {
    isSetStandardizationDept?: string;
    standardizationStaffNum?: number;
    fullTimeStaffNum?: number;
    partTimeStaffNum?: number;
    standardizationEngineerNum?: number;
    juniorEngineerNum?: number;
    intermediateEngineerNum?: number;
    standardizationFunds?: number;
    standardizationPersonnelFunds?: number;
    standardizationProjectFunds?: number;
    standardizationActivityNum?: number;
    pilotDemonstrationProjectNum?: number;
    innovationContributionAwardNum?: number;
    enterpriseStandardLeaderNum?: number;
    benchmarkingStandardProductsNum?: number;
    otherStandardizationActivityNum?: number;
    otherProjectName?: string;
    standardizationTechnicalOrganizationName?: string;
    participantNum?: number;
    honorList: any[];
  }

  const formRef = ref();
  const loading = ref(false);
  const isSetStandardizationDeptOptions = ref([]);
  const form = ref<FormValue>({
    honorList: [],
  });
  const currentRow = ref<any>({});
  const currentIndex = ref();
  const dialogVisible = ref(false);
  const numberRegex = /^(0|[1-9]\d{0,3})$/;
  const maxNumberRegex = /^(0|[1-9]\d{0,3})$/;
  const decimalsRegex = /^\d+(\.\d{1,2})?$/;
  const maxDecimalsRegex = /^(?:0|[1-9]\d{0,3})(?:\.\d{1,2})?$/;
  const rules = ref({
    isSetStandardizationDept: [{ required: true, message: '请选择', trigger: 'change' }],
    standardizationStaffNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    fullTimeStaffNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    partTimeStaffNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    standardizationEngineerNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    juniorEngineerNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    intermediateEngineerNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    standardizationFunds: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: decimalsRegex, message: '最多保留两位小数', trigger: 'blur' },
      { pattern: maxDecimalsRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    standardizationPersonnelFunds: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: decimalsRegex, message: '最多保留两位小数', trigger: 'blur' },
      { pattern: maxDecimalsRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    standardizationProjectFunds: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: decimalsRegex, message: '最多保留两位小数', trigger: 'blur' },
      { pattern: maxDecimalsRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    standardizationActivityNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    pilotDemonstrationProjectNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    innovationContributionAwardNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    enterpriseStandardLeaderNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    benchmarkingStandardProductsNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    otherStandardizationActivityNum: [
      { required: true, message: '请输入', trigger: 'blur' },
      { pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' },
      { pattern: maxNumberRegex, message: '最大不超过9999', trigger: 'blur' },
    ],
    participantNum: [{ pattern: numberRegex, message: '请输入正整数或0', trigger: 'blur' }],
  });

  let isSetStandardizationDeptDict = <IDicts>await getDicts('yes_no');
  isSetStandardizationDeptOptions.value = isSetStandardizationDeptDict?.data || [];

  const handleClick = (type: string, row?: any, index?: any) => {
    switch (type) {
      case 'add':
        currentRow.value = {};
        currentIndex.value = '';
        dialogVisible.value = true;
        break;
      case 'edit':
        currentRow.value = row;
        currentIndex.value = index;
        dialogVisible.value = true;
        break;
      case 'delete':
        form.value.honorList.splice(index, 1);
        break;
      default:
        break;
    }
  };

  const updateData = (data: any) => {
    if (currentRow.value?.honorName) {
      form.value.honorList.splice(currentIndex.value, 1, data);
    } else {
      form.value.honorList.push(data);
    }
  };

  const previousPage = () => {
    checkValidity(route.query.id as string | number).then((res: any) => {
      if (res.data) {
        emit('handleBack', 2);
      } else {
        $modal.msgError(res.message);
        router.replace('/questionnaire');
      }
    });
  };

  const nextPage = () => {
    loading.value = true;
    checkValidity(route.query.id as string | number).then((res: any) => {
      if (res.data) {
        formRef?.value.validate((valid: boolean) => {
          if (valid) {
            emit('handleNext', form.value, 2);
          }
          loading.value = false;
        });
      } else {
        $modal.msgError(res.message);
        router.replace('/questionnaire');
      }
    });
  };

  const emit = defineEmits(['handleNext', 'handleBack']);
</script>

<style lang="scss" scoped>
  .form-inline {
    color: #333 !important;

    :deep(.el-form-item) {
      width: auto !important;
    }

    .half-column {
      width: 48% !important;
    }

    .one-column {
      width: 100% !important;

      &-content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        div {
          font-size: 14px;
        }

        span,
        div {
          line-height: 36px !important;
        }

        div,
        el-input,
        el-select {
          margin-right: 15px;
        }

        :deep(.el-input) {
          width: 80px !important;
        }

        :deep(.el-select) {
          width: 540px !important;
        }

        .w-150 {
          width: 150px !important;
        }

        .w-430 {
          width: 430px !important;
        }
      }
    }
  }

  :deep(.el-input) {
    height: 36px !important;
  }

  :deep(.el-select__wrapper) {
    height: 36px !important;
  }

  .btn {
    display: flex;
    justify-content: center;
    margin: 40px auto 60px !important;

    :deep(.el-button) {
      width: 180px !important;
      height: 42px !important;
      border-radius: 3px !important;
      border-color: $primary-color !important;
    }

    :deep(.el-button + .el-button) {
      margin-left: 40px !important;
    }

    :deep(.el-button:hover) {
      background-color: $primary-color !important;
    }

    :deep(.el-button.is-plain) {
      color: $primary-color !important;
    }

    :deep(.el-button.is-plain:hover) {
      background-color: #fff !important;
    }
  }
</style>
