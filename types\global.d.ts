declare namespace MathJax {
  // Tex 输入解析器配置
  interface TexOptions {
    inlineMath?: string[][]; // 内联数学公式的起始和结束符号
    displayMath?: string[][]; // 块级数学公式的起始和结束符号
    packages?: string[]; // 使用的 TeX 包
    tags?: 'none' | 'ams' | 'all'; // 公式标签配置
    tagSide?: 'left' | 'right'; // 公式标签位置
    tagIndent?: string; // 公式标签缩进
    useLabelIds?: boolean; // 是否使用标签 ID
    multlineWidth?: string; // 多行公式宽度
    maxMacros?: number; // 最大宏定义数量
    maxBuffer?: number; // 最大缓冲区大小
  }

  // MathJax 配置
  interface MathJaxConfig {
    tex?: TexOptions; // TeX 配置
    startup?: {
      typeset?: boolean; // 是否自动渲染
      ready?: () => void; // 初始化完成回调
      defaultReady?: () => void; // 默认初始化完成回调
    };
    loader?: {
      load?: string[]; // 需要加载的模块
      paths?: { [key: string]: string }; // 自定义模块路径
    };
    options?: {
      enableMenu?: boolean; // 是否启用右键菜单
      menuOptions?: {
        settings?: {
          texHints?: boolean; // 是否显示 TeX 提示
          semantics?: boolean; // 是否显示语义信息
          zoom?: 'None' | 'Hover' | 'Click' | 'DoubleClick'; // 缩放行为
          zscale?: string; // 缩放比例
          renderer?: 'CHTML' | 'SVG'; // 渲染器类型
        };
      };
    };
  }

  // 数学文档对象
  interface MathDocument {
    options: {
      enableAssistiveMml: boolean; // 是否启用辅助 MathML
    };
  }

  // 数学项对象
  interface MathItem {
    start: { node: HTMLElement }; // 数学公式的起始节点
    end: { node: HTMLElement }; // 数学公式的结束节点
  }

  // 渲染选项
  interface TypesetOptions {
    display?: boolean; // 是否为块级公式
  }

  // MathJax 核心对象
  interface MathJaxObject {
    typesetPromise: (elements?: HTMLElement[]) => Promise<void>; // 异步渲染方法
    startup: {
      document: MathDocument; // 文档对象
      ready?: () => void; // 初始化完成回调
      defaultReady?: () => void; // 默认初始化完成回调
    };
    config: MathJaxConfig; // 配置对象
    tex?: TexOptions; // TeX 配置
  }
}

// 声明全局 MathJax 变量
declare var MathJax: MathJax.MathJaxObject;
