<template>
  <el-dialog
    width="80%"
    :title="title"
    append-to-body
    v-model="props.visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="standard-analysis-wrap">
      <div class="standard-analysis-left scroller-bar-style">
        <div class="flex flex-ai-center mb-10">
          <span class="iconfont icon-wenzhang f-20 c-primary"></span>
          <span class="f-18 c-33 f-bold ml-10">{{ props.standardCode }}</span>
        </div>
        <el-tree
          ref="treeRef"
          empty-text="暂无数据"
          node-key="id"
          :data="treeData"
          :highlight-current="true"
          :auto-expand-parent="true"
          :expand-on-click-node="false"
          :default-expanded-keys="treeHighLight"
          :props="{ children: 'children', label: 'name' }"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <div class="flex flex-ai-center" style="width: 100%">
              <span v-if="node.level == 1" class="iconfont icon-levels f-16 c-primary mr-10"></span>
              <RetrievalToolTip :text="node.label" :className="isHighlight(data) ? 'c-ff0000' : ''" />
            </div>
          </template>
        </el-tree>
      </div>
      <div v-html="treeHtml" class="standard-analysis-right"></div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { getDomesticTree, getDomesticTreeHighLight } from '@/api/retrieval/domestic';
  import { type IResponseData } from '@/types';
  import { ElTree } from 'element-plus';

  const route = useRoute();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    standardId: { type: [String, Number] },
    standardName: { type: String },
    standardCode: { type: [String, Number] },
    treeId: {
      type: [String, Number],
      required: true,
      default: '',
    },
    searchValue: { type: String },
  });

  const treeRef = ref<InstanceType<typeof ElTree>>();
  const title = ref('');
  const treeData = ref([]);
  const treeHtml = ref('');
  const treeHighLight = ref([]);

  title.value = props.standardCode + ' | ' + props.standardName;

  let treeRes = <IResponseData>await getDomesticTree(props.standardId as string | number);
  treeData.value = treeRes.data.tree || [];
  treeHtml.value = treeRes.data.text || '';

  if (props.searchValue) {
    let treeHighLightRes = <IResponseData>await getDomesticTreeHighLight({
      standardId: props.standardId,
      title: props.searchValue,
    });
    treeHighLight.value = treeHighLightRes.data || [];
  }

  onMounted(() => {
    nextTick(() => {
      treeRef.value!.setCurrentKey(props.treeId, true);
      handleNodeClick({ id: props.treeId });
    });
  });

  const isHighlight = (data: any) => {
    return treeHighLight.value.some(n => n === data.id);
  };

  const handleNodeClick = (item: any) => {
    setTimeout(() => {
      let nId = 't-' + item.id;
      document.getElementById(nId)?.scrollIntoView({
        behavior: 'smooth',
      });
    }, 300);
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss" scoped>
  html {
    scroll-behavior: smooth; /* 启用平滑滚动 */
  }

  .standard-analysis-wrap {
    display: flex;
    height: calc(100vh - 200px);

    .standard-analysis-left {
      margin-right: 20px;
      width: 400px;
      flex-shrink: 0;
      background: #ffffff;
      overflow-y: auto;
      border-right: 2px solid #e8e8e8;

      :deep(.el-tree-node__content) {
        height: 40px !important;

        &:hover {
          background: #e9f0fe;
          border-radius: 5px;
          color: #3377ff;
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          font-weight: bold;
          padding-right: 8px;
          height: 40px;
          overflow: hidden;

          .custom-label {
            flex: 1;
            white-space: nowrap;
            overflow: hidden; //文本超出隐藏
            text-overflow: ellipsis;
          }
        }
      }
    }

    .standard-analysis-right {
      flex: 1;
      height: 100%;
      overflow: scroll;

      &::-webkit-scrollbar-track-piece {
        background: #e8e8e8;
      }

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #d1d1d1;
        border-radius: 20px;
      }
    }
  }
</style>
