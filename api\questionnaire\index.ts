export const getQuestionnaireList = (params: object) => {
  return request.get('/business/fillingTask/list', params);
};

export const checkValidity= (id: number | string) => {
  return request.get('/business/fillingData/checkFilling/' + id);
};

export const getQuestionnaireDetail = (id: number | string) => {
  return request.get('/business/fillingTask/' + id);
};

export const addQuestionnaire = (data: object) => {
  return request.post('/business/fillingData', data);
};

export const checkFillingSocialCode = (params: object) => {
  return request.get('/business/fillingData/checkFillingSocialCode', params);
};

export const getNationalEconomicTypeTree = () => {
  return request.get('/search/nationalEconomicType/tree');
};
