<template>
  <div class="guide-wrap">
    <div class="main-wrap guide-container">
      
      <div class="guide-menu-wrap">
        <div class="title">标信查使用指南</div>
        <div class="search-box">
          <el-input
            v-model="filterText"
            placeholder="检索目录名称"
          >
            <template #append>
              <div @click="handleSearch" class="search">
                <el-icon><Search /></el-icon>
              </div> 
            </template>
          </el-input>
        </div>
        <el-tree
          :data="guideList"
          ref="treeRef"
          node-key="id"
          empty-text="暂无数据"
          :highlight-current="true"
          :props="defaultProps" 
          default-expand-all
          :expand-on-click-node="false"
          :current-node-key="info.id"
          @node-click="handleNodeClick"
          :filter-node-method="filterNode"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span v-if="node.level == 1" class="custom-label">
                  <i class="iconfont icon-levels c-primary ml-5 mr-8"></i>
                  {{ node.label }}
                </span>
                <span v-else class="custom-label">
                  {{ node.label }}
                </span>
              </span>
            </template>
        </el-tree>
      </div>
      <div class="guide-content">
        <template v-if="info.id">
          <div class="guide-title">{{info.guideName}}</div>
          <div class="guide-show" v-html="info.content"></div>
        </template>
        <template v-else>
          <BxcEmpty />
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getGuideList, getGuideDetail } from '@/api/guide'
import { ref, watch } from 'vue'

const filterText = ref('')
const treeRef = ref()
const { setUseHead } = useSeo()
setUseHead('/guide')

const defaultProps = {
  children: 'children',
  label: 'name',
}

interface IGuideInfo {
  id: string;
  guideName: string;
  status: string;
  statusName: string;
  content: string;
  [key: string]: any;
}
const guideList = ref([])
const info = ref(<IGuideInfo>{})
const getList = () => {
  getGuideList().then((res: any) => {
    nextTick(() => {
      guideList.value = res.data || []
      if(guideList.value.length > 0) {
        handleNodeClick(guideList.value[0], guideList.value)
      }
    })
  })
}
const handleSearch = () => {
  
}
watch(filterText, (val) => {
  treeRef.value && treeRef.value!.filter(val)
})
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.name.includes(value)
}
const handleNodeClick = (item: any, nodes: any) => {
  getGuideDetail(item.id).then((res: any) => {
    nextTick(() => {
      info.value = res.data || {}
    })
  })
  
}

getList()
</script>
<style lang="scss" scoped>
.guide-wrap{
  margin-top: 60px;
  background: #F8F9FB;
  min-height: calc(100vh - 528px);
  .guide-container{
    padding: 30px 0px;
    display: flex;
    justify-content: space-between;
    gap: 25px;
    .guide-menu-wrap{
      display: flex;
      flex-direction: column;
      width: 370px;
      min-height: 460px;
      padding: 35px 20px;
      box-sizing: border-box;
      background: #FFFFFF;
      .title{
        font-weight: bold;
        font-size: 18px;
        color: #333333;
      }
      .search-box{
        margin: 20px 0px;
        .search{
          width: 40px;
          height: 100%;
          text-align: center;
          background: $primary-color;
          font-size: 16px;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 0px 3px 3px 0px;
          cursor: pointer;
          .el-icon{
            color: #FFFFFF !important;
            height: 14px;
            width: 14px;
          }
        }
        :deep(.el-input-group__append){
          padding: 0px;
        }
      }
      :deep(.el-tree-node__content > .el-tree-node__expand-icon) {
        padding-left: 6px !important;
      }
    }
    .guide-content{
      width: 805px;
      padding: 30px 30px;
      background: #FFFFFF;
      box-sizing: border-box;
      .guide-title{
        font-weight: bold;
        font-size: 18px;
        color: #333333;
        text-align: center;
      }
      .guide-show{
        margin-top: 20px;
        font-size: 14px;
        color: #333333;
        line-height: 27px;
        word-wrap: break-word;
        :deep(img) {
          max-width: 100%;
          height: auto;
        }
      }
    }
  }
  
}
</style>