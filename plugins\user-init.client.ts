import { useUserStore } from '@/store/userStore';

export default defineNuxtPlugin(() => {
  // 只在客户端执行
  if (process.client) {
    const userStore = useUserStore();
    
    // 初始化用户数据
    userStore.initUserData();
    
    // 如果有 token，获取用户信息
    if (userStore.token) {
      userStore.getUserInfo().catch(() => {
        // 如果获取用户信息失败，清除无效的 token
        userStore.resetData();
      });
    }
  }
});
