<template>
  <div>
    <div class="banner">
      <div class="main-wrap">
        <div class="f-32 c-33 f-bold">标准托管</div>
        <div class="banner-title mt-20">
          标准托管是对用户所关注的各类标准进行长期的跟踪与分析，定向定时向用户提供最新的标准信息、标准更替信息、标准废止信息等。
          <br />
          系统将自动对用户托管标准进行定向跟踪，当标准信息、状态发生变化时，系统将自动通过多渠道方式通知用户标准变化信息，以确保企业在标准使用方面的正确性与有效性。
        </div>
        <div class="flex flex-ai-center mt-30">
          <div @click="toList" class="banner-btn pointer">开始托管</div>
          <span class="iconfont icon-tuoguan c-primary f-16 ml-15"></span>
          <span @click="handleClick" class="f-14 text-underline c-primary ml-5 pointer">查看我的托管</span>
        </div>
      </div>
    </div>
    <div class="content main-wrap">
      <div class="f-26 f-bold c-33 text-center">托管流程</div>
      <div class="flex flex-ai-center flex-sa mt-40">
        <div>
          <img src="@/assets/images/trusteeship/flow-1.png" alt="" class="content-flow-img" />
          <div class="content-flow-title">添加托管标准</div>
          <img src="@/assets/images/trusteeship/01.png" alt="" class="content-flow-number" />
        </div>
        <img src="@/assets/images/trusteeship/flow-line.png" alt="" class="content-flow-line" />
        <div>
          <img src="@/assets/images/trusteeship/flow-1.png" alt="" class="content-flow-img" />
          <div class="content-flow-title">设置监测通知</div>
          <img src="@/assets/images/trusteeship/02.png" alt="" class="content-flow-number" />
        </div>
        <img src="@/assets/images/trusteeship/flow-line.png" alt="" class="content-flow-line" />
        <div>
          <img src="@/assets/images/trusteeship/flow-1.png" alt="" class="content-flow-img" />
          <div class="content-flow-title">标准动态监测</div>
          <img src="@/assets/images/trusteeship/03.png" alt="" class="content-flow-number" />
        </div>
        <img src="@/assets/images/trusteeship/flow-line.png" alt="" class="content-flow-line" />
        <div>
          <img src="@/assets/images/trusteeship/flow-1.png" alt="" class="content-flow-img" />
          <div class="content-flow-title">标准预警通知</div>
          <img src="@/assets/images/trusteeship/04.png" alt="" class="content-flow-number" />
        </div>
      </div>
      <div class="flex flex-ai-center flex-sb mt-40">
        <div class="content-card">
          <div class="flex flex-ai-center f-14 c-33">
            <span class="iconfont icon-icon_duihao-mian c-20C300"></span>
            <span class="ml-10 f-14 c-33 f-bold">标准状态变化</span>
          </div>
          <div class="f-14 c-88 mt-10 ml-25">标准状态变化即时通知，确保标准使用准确性</div>
        </div>
        <div class="content-card">
          <div class="flex flex-ai-center f-14 c-33">
            <span class="iconfont icon-icon_duihao-mian c-20C300"></span>
            <span class="ml-10 f-14 c-33 f-bold">标准替代关系</span>
          </div>
          <div class="f-14 c-88 mt-10 ml-25">标准修订版本全覆盖，确保使用最新标准</div>
        </div>
        <div class="content-card">
          <div class="flex flex-ai-center f-14 c-33">
            <span class="iconfont icon-icon_duihao-mian c-20C300"></span>
            <span class="ml-10 f-14 c-33 f-bold">相近标准推荐</span>
          </div>
          <div class="f-14 c-88 mt-10 ml-25">标准智能推荐模型，确保标准使用高效、便捷性</div>
        </div>
      </div>
      <div class="flex flex-ai-center flex-sb mt-40">
        <div>
          <img src="@/assets/images/trusteeship/flow-5.png" alt="" class="content-step-img" />
          <div class="content-step-title">标准库添加</div>
        </div>
        <div>
          <img src="@/assets/images/trusteeship/flow-6.png" alt="" class="content-step-img" />
          <div class="content-step-title">批量导入</div>
        </div>
        <div>
          <img src="@/assets/images/trusteeship/flow-7.png" alt="" class="content-step-img" />
          <div class="content-step-title">短信、推送、邮件...</div>
        </div>
        <div>
          <img src="@/assets/images/trusteeship/flow-8.png" alt="" class="content-step-img" />
          <div class="content-step-title">数据资源湖</div>
        </div>
        <div>
          <img src="@/assets/images/trusteeship/flow-9.png" alt="" class="content-step-img" />
          <div class="content-step-title">数据预警算法模型</div>
        </div>
      </div>
      <div class="f-26 c-33 f-bold text-center mt-60">通知方式</div>
      <div class="f-14 c-88 text-center mt-15">标准实时监测，状态变化多种通知方式自定义，灵活选择</div>
      <div class="flex flex-ai-center flex-sa mt-50">
        <div>
          <img src="@/assets/images/trusteeship/flow-10.png" alt="" class="content-message-img" />
          <div class="content-message-title">短信通知</div>
        </div>
        <div>
          <img src="@/assets/images/trusteeship/flow-11.png" alt="" class="content-message-img" />
          <div class="content-message-title">公众号推送</div>
        </div>
        <div>
          <img src="@/assets/images/trusteeship/flow-12.png" alt="" class="content-message-img" />
          <div class="content-message-title">邮件通知</div>
        </div>
        <div>
          <img src="@/assets/images/trusteeship/flow-13.png" alt="" class="content-message-img" />
          <div class="content-message-title">站内信通知</div>
        </div>
      </div>
    </div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleClick" />
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准托管_标准托管服务_标准企业托管_标准预警_标信查平台',
    meta: [
      { name: 'keywords', content: '标准托管，标准托管服务，标准更新服务，标准预警' },
      {
        name: 'description',
        content:
          '标信查平台数据服务频道，可以为企业提供标准托管、标准查新、标准情报、标准灯塔、标准战略分析等多种标准信息化服务，助力广大企业更好发展。',
      },
    ],
  });

  import { handleJump } from '@/utils/common';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();

  const openLogin = ref(false);

  const handleClick = () => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      openLogin.value = false;
      handleJump('/user-center/trusteeship/index');
    }
  };

  const toList = async () => {
    await navigateTo({
      path: '/retrieval/domestic',
    });
  };
</script>

<style lang="scss" scoped>
  .banner {
    width: 100%;
    min-height: 380px;
    background: url('@/assets/images/trusteeship/cover.png') no-repeat center;
    background-size: 100% 100%;

    .main-wrap {
      padding-top: 110px;
    }

    &-title {
      font-size: 16px;
      color: #333;
      width: 750px;
      line-height: 24px;
    }

    &-btn {
      width: 240px;
      height: 40px;
      background-color: #045cff;
      border-radius: 20px;
      font-size: 16px;
      color: #fff;
      text-align: center;
      line-height: 40px;
    }
  }

  .content {
    padding: 60px 0 65px;
    box-sizing: border-box;

    &-flow {
      &-img {
        display: block;
        width: 100px;
      }

      &-line {
        display: block;
        width: 137px;
        height: 19px;
        position: relative;
        top: -55px;
      }

      &-title {
        font-size: 16px;
        color: #333;
        text-align: center;
        margin-top: 15px;
      }

      &-number {
        display: block;
        width: 25px;
        margin: 15px auto 0;
      }
    }

    &-card {
      width: 390px;
      padding: 25px;
      box-sizing: border-box;
      background-color: #f4f8ff;
    }

    &-step {
      &-img {
        display: block;
        width: 220px;
      }

      &-title {
        font-size: 16px;
        color: #333;
        text-align: center;
        margin-top: 20px;
      }
    }

    &-message {
      &-img {
        display: block;
        width: 73px;
      }

      &-title {
        font-size: 16px;
        color: #333;
        text-align: center;
        margin-top: 15px;
      }
    }
  }

  .c-20C300 {
    color: #20c300;
  }
</style>
