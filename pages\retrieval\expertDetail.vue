<template>
  <div class="container mt-60">
    <div class="main-wrap">
      <div class="container-top">
        <div class="flex flex-ai-center">
          <div class="f-20 c-22 f-bold">{{ form.commissionerName }}</div>
          <div v-for="(item, index) in form.committeeNumberLists" :key="index" :class="getStatusColor('blue')" class="ml-15">
            {{ item }}
          </div>
        </div>
        <div class="f-14 c-88 mt-10">{{ form.workUnit }}</div>
      </div>
      <div class="f-20 f-bold mt-30">专业范围</div>
      <div class="mt-20 f-14 c-88">{{ form.responsibleProfessional }}</div>
      <div class="f-20 f-bold mt-30">擅长领域</div>
      <div class="mt-20 f-14 c-88">{{ form.goodProfessional }}</div>
      <div class="f-20 f-bold mt-30">所属标委会</div>
      <RetrievalDetailDescriptions
        v-for="(item, index) in form.committeeCommissionerList"
        :key="index"
        :form="item"
        :data="basicInfo"
        class="mt-20"
      />
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :type="4" @updateData="getDetail" />
  </div>
</template>

<script setup lang="ts">
  import { getExpertDetail } from '@/api/retrieval/expert';
  import { splitStrToArray } from '@/utils/common';
  import { type IExpertInfo, type IResponseData } from '@/types';

  const route = useRoute();

  const form = ref<IExpertInfo>({
    commissionerName: '',
    workUnit: '',
    committeeNumberList: '',
  });
  const basicInfo = reactive([
    { label: '委员会编号：', fieldName: 'committeeNumber' },
    { label: '委员会名称：', fieldName: 'cnCommitteeName' },
    { label: '委员会职务：', fieldName: 'committeePosition' },
    { label: '届号：', fieldName: 'sessionNumber' },
    { label: '加入时间：', fieldName: 'participationTime' },
  ]);

  let { data } = <IResponseData>await getExpertDetail(route.query.id as string | number);
  if (data.committeeNumberList) data.committeeNumberLists = splitStrToArray(data.committeeNumberList, ',');
  form.value = data || {};

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/search/sdc/tcCommissioner/' + route.query.id);
    if (data.committeeNumberList) data.committeeNumberLists = splitStrToArray(data.committeeNumberList);
    form.value = data || {};
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;

    &-top {
      padding: 25px 35px;
      box-sizing: border-box;
      width: 100%;
      background-color: #f8f9fb;
    }
  }
</style>
