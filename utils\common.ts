export function getStatusColor(status: string, type: string = 'tag') {
  switch (status) {
    case 'sky-blue':
      return `status-00AEFF ${type === 'tag' ? 'status-tag status-bg00AEFF' : ''}`;
    case 'orange':
      return `status-FFB400 ${type === 'tag' ? 'status-tag status-bgFFB400' : ''}`;
    case 'yellow':
      return `status-FF6600 ${type === 'tag' ? 'status-tag status-bgFFE6D6' : ''}`;
    case 'purple':
      return `status-A800FF ${type === 'tag' ? 'status-tag status-bgA800FF' : ''}`;
    case 'blue':
      return `status-045CFE ${type === 'tag' ? 'status-tag status-bg045CFE' : ''}`;
    case 'green':
      return `status-04AE00 ${type === 'tag' ? 'status-tag status-bg04AE00' : ''}`;
    case 'gray':
      return `status-999999 ${type === 'tag' ? 'status-tag status-bg999999' : ''}`;
    case 'red':
      return `status-FF0000 ${type === 'tag' ? 'status-tag status-bgFF0000' : ''}`;
    default:
      return '';
  }
}
interface IFormatDateObj {
  y: number;
  m: number;
  d: number;
  h: number;
  i: number;
  s: number;
  a: number;
  [key: string]: any;
}

// 日期格式化
export const parseTime = (time: any, pattern? :any) => {
  if (!time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj: IFormatDateObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result: string, key: string) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}
export const splitStrToArray = (str: string, sym = '、') => {
  let array: string[] = [];
  if (!str || str == '') {
    return array;
  }
  array = str.split(sym).filter(item => item != '');
  return array;
};

export const handleJump = async (url: string, query?: Record<string, string | number>) => {
  const urlPattern = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
  await navigateTo(
    { path: (urlPattern.test(url) ? url : window.location.origin + url), query: query },
    {
      open: {
        target: '_blank',
      },
    }
  );
};
export function getOS() {
  var sUserAgent = navigator.userAgent;
  var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
  var isMac = (navigator.platform == "Mac68K") || (navigator.platform == "MacPPC") || (navigator.platform == "Macintosh") || (navigator.platform == "MacIntel");
  if (isMac) return "Mac";
  var isUnix = (navigator.platform == "X11") && !isWin && !isMac;
  if (isUnix) return "Unix";
  var isLinux = (String(navigator.platform).indexOf("Linux") > -1);
  if (isLinux) return "Linux";
  if (isWin) {
    var isWin2K = sUserAgent.indexOf("Windows NT 5.0") > -1 || sUserAgent.indexOf("Windows 2000") > -1;
    if (isWin2K) return "Windows2000";
    var isWinXP = sUserAgent.indexOf("Windows NT 5.1") > -1 || sUserAgent.indexOf("Windows XP") > -1;
    if (isWinXP) return "WindowsXP";
    var isWin2003 = sUserAgent.indexOf("Windows NT 5.2") > -1 || sUserAgent.indexOf("Windows 2003") > -1;
    if (isWin2003) return "Windows2003";
    var isWinVista = sUserAgent.indexOf("Windows NT 6.0") > -1 || sUserAgent.indexOf("Windows Vista") > -1;
    if (isWinVista) return "WindowsVista";
    var isWin7 = sUserAgent.indexOf("Windows NT 6.1") > -1 || sUserAgent.indexOf("Windows 7") > -1;
    if (isWin7) return "Windows7";
    var isWin10 = sUserAgent.indexOf("Windows NT 10") > -1 || sUserAgent.indexOf("Windows 10") > -1;
    if (isWin10) return "Windows10";
    var isWin11 = sUserAgent.indexOf("Windows NT 11") > -1 || sUserAgent.indexOf("Windows 11") > -1;
    if (isWin11) return "Windows11";
  }
  return "other";
}

export function getBrowserInfo() {
  let agent = navigator.userAgent.toLowerCase();
  // let system = agent.split(' ')[1].split(' ')[0].split('(')[1];
  let REGSTR_EDGE = /edge\/[\d.]+/gi;
  let REGSTR_IE = /trident\/[\d.]+/gi;
  let OLD_IE = /msie\s[\d.]+/gi;
  let REGSTR_FF = /firefox\/[\d.]+/gi;
  let REGSTR_CHROME = /chrome\/[\d.]+/gi;
  let REGSTR_SAF = /safari\/[\d.]+/gi;
  let REGSTR_OPERA = /opr\/[\d.]+/gi;
  let REGSTR_QQ = /qqbrowser\/[\d.]+/gi;
  let REGSTR_METASR = /metasr\+/gi;
  let REGSTR_WECHAT = /MicroMessenger\/[\d.]+/gi;
  if (agent.indexOf('trident') > 0) {
    // IE
    return agent.match(REGSTR_IE)![0].split('/')[0] + ' ' + agent.match(REGSTR_IE)![0].split('/')[1];
  } else if (agent.indexOf('msie') > 0) {
    // OLD_IE
    return agent.match(OLD_IE)![0].split('/')[0] + ' ' + agent.match(OLD_IE)![0].split('/')[1];
  } else if (agent.indexOf('edge') > 0) {
    // Edge
    return agent.match(REGSTR_EDGE)![0].split('/')[0] + ' ' + agent.match(REGSTR_EDGE)![0].split('/')[1];
  } else if (agent.indexOf('firefox') > 0) {
    // firefox
    return agent.match(REGSTR_FF)![0].split('/')[0] + ' ' + agent.match(REGSTR_FF)![0].split('/')[1];
  } else if (agent.indexOf('opr') > 0) {
    // Opera
    return agent.match(REGSTR_OPERA)![0].split('/')[0] + ' ' + agent.match(REGSTR_OPERA)![0].split('/')[1];
  } else if (agent.indexOf('safari') > 0 && agent.indexOf('chrome') < 0) {
    // Safari
    return agent.match(REGSTR_SAF)![0].split('/')[0] + ' ' + agent.match(REGSTR_SAF)![0].split('/')[1];
  } else if (agent.indexOf('qqbrowse') > 0) {
    // qqbrowse
    return agent.match(REGSTR_QQ)![0].split('/')[0] + ' ' + agent.match(REGSTR_QQ)![0].split('/')[1];
  } else if (agent.indexOf('metasr') > 0) {
    // metasr火狐
    return 'metasr';
  } else if (agent.indexOf('micromessenger') > 0) {
    // 微信内置浏览器
    return agent.match(REGSTR_WECHAT)![0].split('/')[0] + ' ' + agent.match(REGSTR_WECHAT)![0].split('/')[1];
  } else if (agent.indexOf('chrome') > 0) {
    // Chrome
    return agent.match(REGSTR_CHROME)![0].split('/')[0] + ' ' + agent.match(REGSTR_CHROME)![0].split('/')[1];
  } else {
    return '未获取到浏览器信息';
  }
}
// \n to  <br/>
export function toBr(str: string) {
  if(!str) return ''
  return str.replaceAll('\n','<br/>')
}

export function formatDecimal(value: number, decimals = 2, returnNumber = false) {
  const num = Number(value)
  const factor = Math.pow(10, decimals)
  const rounded = Math.round((num + Number.EPSILON) * factor) / factor
  
  if (returnNumber) {
    return rounded
  }
  
  return rounded.toFixed(decimals)
}
