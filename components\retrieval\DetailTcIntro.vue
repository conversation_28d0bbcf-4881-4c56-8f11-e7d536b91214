<template>
  <div class="intro">
    <div class="f-18 c-33 f-bold">{{ form.committeeNumber || '-' }} | {{ form.cnCommitteeName || '-' }}</div>
    <div v-if="form.enCommitteeName" class="f-14 c-99 mt-10">{{ form.enCommitteeName }}</div>
    <div class="f-14 c-99 mt-10">
      {{ form.cnCommitteeName || '-' }}编号{{ form.committeeNumber || '-' }}由{{ form.preparationUnit || '-' }}筹建，{{
        form.businessGuidanceUnit || '-'
      }}进行业务指导。
    </div>
    <div class="f-14 c-99 mt-5">本届届号第{{ form.sessionNumber }}届，现任秘书长{{ form.currentSecretaryGeneral }}。</div>
    <div class="f-14 c-99 mt-5">负责专业范围为{{ form.responsibleProfessionalScope }}</div>
    <el-divider />
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);
</script>

<style lang="scss" scoped></style>
