window.MathJax = {
  tex: {
    inlineMath: [
      ["$", "$"],
      ["\\(", "\\)"],
    ], // 行内公式选择符
    displayMath: [
      ["$$", "$$"],
      ["\\[", "\\]"],
    ], // 段内公式选择符
  },
  options: {
    skipHtmlTags: ['script', 'noscript','style', 'textarea', 'pre', 'code',
    'a'], // 避开某些标签
    ignoreHtmlClass: 'tex2jax_ignore',
    processHtmlClass: 'tex2jax_process'
  },
  startup: {
    ready() {
      MathJax.startup.defaultReady();
    },
  },
};