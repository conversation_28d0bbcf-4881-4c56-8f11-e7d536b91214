body {
  position: relative;
}
.header-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  line-height: 60px;
  background-color: rgba(255, 255, 255, 0.5);
  color: #333333;
  font-size: 16px;
  z-index: 999;
  box-shadow: -8px 6px 6px 0px rgba(12, 44, 105, 0.02);
  &.opaqueHeader {
    background: #ffffff !important;
    box-shadow: 0px 7px 9px 0px rgba(8, 22, 47, 0.08);
  }

  .header-container {
    height: 100%;
    line-height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-left {
      height: 100%;
      line-height: 100%;
      display: flex;
      align-items: center;
      a {
        line-height: 100%;
        display: flex;
        img {
          height: 36px;
        }
      }
      div:hover {
        color: $primary-color;
      }
    }
    .header-right {
      margin-left: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .header-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-left: 20px;
        cursor: pointer;
      }
      .header-login {
        color: #333333;
        padding: 0 15px;
        &:hover {
          color: $primary-color;
        }
      }
      .header-register {
        color: #ffffff;
        background: $primary-color;
        padding: 0 15px;
      }
      a {
        height: 60px;
        line-height: 60px;
      }
    }
  }
}
.main-content-wrap {
  min-height: calc(100vh - 528px);
}
.footer-wrap {
  width: 100%;
  background: #151b26;
  .footer-container {
    margin: 0 auto;
    width: 1200px;
  }
}
.main-wrap {
  margin: 0 auto;
  width: 1200px;
  // min-height: calc(100vh - 530px);
}

.main-w {
  width: 1200px !important;
}
.h-title-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  .more-btn {
    position: absolute;
    top: 42px;
    right: 0px;
    font-size: 16px;
    color: $primary-color;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
h3.title {
  font-size: 28px;
  font-weight: bold;
  color: #333333;
  text-align: center;
}

.modules {
  padding: 0 0 30px;
  box-sizing: border-box;
  background-color: #f5f5f5;

  &-container {
    width: 1200px;
    margin: 0 auto;
  }

  &-card {
    margin-top: 15px;
    width: 100%;
    background-color: #ffffff;
    border-radius: 3px;
    padding: 20px 20px 30px;
    box-sizing: border-box;

    // 公共头部
    &-top {
      border-bottom: 1px solid #e8e8e8;

      &-title {
        font-size: 18px;
        color: $primary-color;
        font-weight: bold;
        border-bottom: 2px solid $primary-color;
        padding-bottom: 13px;
        box-sizing: border-box;
      }

      &-more {
        margin-top: 4px;
        font-size: 14px;
        color: #999999;
        cursor: pointer;
      }

      &-img {
        width: 11px;
        height: 12px;
        margin-left: 5px;
      }
    }

    &-content {
      min-height: 330px;
    }

    // 通知公告 && 政策法规
    &-item {
      font-size: 14px;
      color: #555555;
      height: 60px;
      line-height: 60px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;

      &:hover {
        color: $primary-color !important;
      }

      &-img {
        width: 16px;
        height: 16px;
      }

      &-title {
        width: 350px;
        margin-left: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-time {
        flex: 1;
        text-align: right;
      }
    }

    // 政务服务
    &-nqi {
      width: 211px;
      color: #333333;
      padding: 15px 14px;
      box-sizing: border-box;
      margin: 30px 25px 0 0;
      border: 1px solid #e8e8e8;
      border-radius: 3px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;

      &:nth-child(5n) {
        margin-right: 0;
      }

      &:hover {
        color: $primary-color !important;
        font-weight: bold !important;
        border: 1px solid $primary-color !important;
      }

      &-img {
        height: 50px;
      }

      &-title {
        height: 100%;
        font-size: 14px;
        text-align: center;
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    // 优秀企业 && 奖项申报
    &-brand {
      width: 171px;
      height: 120px;
      border: 1px solid #e8e8e8;
      border-radius: 3px;
      margin: 20px 17px 0 0;
      padding: 0 15px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      &:nth-child(3n) {
        margin-right: 0;
      }

      &:hover {
        color: $primary-color !important;
        font-weight: bold !important;
        border: 1px solid $primary-color !important;
      }

      &-img {
        width: 100%;
        height: 96px;
        display: block;
      }
    }
  }

  &-content {
    background-color: #ffffff;
    padding: 33px 22px 38px;
    box-sizing: border-box;
    margin-top: 15px;
    border-radius: 3px;
  }
}

.search-input {
  display: block !important;
  margin: 0 auto !important;
  width: 750px !important;
  .el-input__inner {
    width: 750px;
    height: 55px;
    background: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 27px;
    padding: 0px 55px 0 30px !important;
    box-sizing: border-box;
    text-align: center;
  }

  .el-icon-search:before {
    position: relative;
    top: 4px;
    right: 15px;
    font-size: 22px;
    color: $primary-color;
  }

  .el-input__inner:hover {
    border-color: #e8e8e8;
  }

  .el-input__icon.el-icon-search {
    cursor: pointer;
  }

  .el-input__inner::placeholder {
    font-size: 14px;
    color: #999999;
    text-align: center;
  }
}

.shadow-divider {
  text-align: center;
  box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.05);
  padding: 10px 0 25px;
  box-sizing: border-box;
}

.w-1200 {
  width: 1200px;
  margin: 0 auto;
}

.border-none {
  border: none;
}

.position-r {
  position: relative;
}

.login-icon {
  height: 16px;
  position: absolute;
  top: calc(50% - 8px);
  left: 16px;
}

.login-code {
  color: $primary-color;
  line-height: 38px;
  min-width: 100px;
  text-align: center;
  position: absolute;
  top: calc(50% - 19px);
  right: 0;
  border-left: 1px solid #e8e8e8;
  cursor: pointer;
}

.enter-btn {
  color: #ffffff !important;
  width: 260px !important;
  height: 46px !important;
  background: $primary-color !important;
  border-radius: 3px !important;
  border: none !important;
}

.entrust {
  &-content {
    padding: 25px 20px;
    box-sizing: border-box;
    margin-top: 15px;
    background-color: #ffffff;
  }
  &-btn {
    padding: 30px 0 38px;
    box-sizing: border-box;
    text-align: center;
    &-left,
    &-right {
      width: 200px;
    }

    &-left {
      color: $primary-color !important;
      border: 1px solid $primary-color !important;
    }

    &-right {
      border: 1px solid $primary-color !important;
      background-color: $primary-color !important;
    }
  }
}

.label-content {
  height: 50px;
  line-height: 50px;
  background: #f1faff;
  padding: 0 18px;
  display: flex;
  justify-content: space-between;
  margin: 20px 0;

  &-left {
    font-size: 20px;
    color: #2d69ed;
    font-weight: bold;
  }

  &-right {
    font-size: 14px;
    color: #333333;

    span {
      font-weight: bold;

      &:not(:first-child) {
        margin-left: 30px;
      }
    }
  }
}

.require_star::after {
  content: '*';
  color: #ff4949;
  margin-left: 5px;
}

.btn-phone {
  height: 32px;
  min-width: 130px;
  font-size: 14px;
  color: #ffffff;
  line-height: 32px;
  text-align: center;
  border-radius: 5px;
  background-color: #0194f6;
}

.move-y-up {
  transition-duration: 0.4s;
  &:hover {
    transform: translateY(-10px);
    transition: all 600ms ease-in-out;
  }
}

.break-word {
  word-wrap: break-word;
}

.jump-link {
  font-size: 14px;
  color: $primary-color;
  text-decoration: underline;
  cursor: pointer;
}

// el-tabs
.el-tabs {
  font-size: 18px !important;
  padding: 10px 20px !important;
  box-sizing: border-box !important;
  background-color: #ffffff !important;
}

.el-tabs__header {
  margin: 0 !important;
}

.el-tabs__item {
  // color: #333333 !important;
  font-size: 18px !important;
  padding-bottom: 5px !important;
  box-sizing: border-box !important;
  height: auto !important;
}

.el-tabs__content {
  min-height: 450px !important;

  .el-empty {
    height: 450px !important;
  }
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
  background-color: #e8e8e8 !important;
}

// el-radio
.el-radio__label {
  padding-left: 5px !important;
}
.scroller-bar-style {
  &::-webkit-scrollbar-track-piece {
    background: #e8e8e8;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d1d1d1;
    border-radius: 20px;
  }
}

.el-descriptions__cell {
  border-color: #e3e3e3 !important;
}

.el-descriptions-item__label {
  width: 170px !important;
  min-width: 100px !important;
  font-size: 14px !important;
  color: #333333 !important;
  font-weight: 400 !important;
  background-color: #f3f6fd !important;
}

.el-tree-node__content {
  color: #333;
  height: 40px !important;
  overflow: hidden !important;
  padding-right: 18px;
  box-sizing: border-box;
}

.el-tree-node__content:hover {
  background-color: #e5eeff !important;
}

.el-tree-node:focus > .el-tree-node__content {
  background-color: #e5eeff !important;
}

.el-tree-node__expand-icon {
  color: #c8c8c8 !important;
  font-size: 15px !important;
}

.el-tree-node {
  font-size: 16px !important;
  font-weight: 600 !important;
}

.el-tree-node .el-tree-node__children .el-tree-node {
  font-size: 14px !important;
  font-weight: 400 !important;
}

.el-tree-node__content > .el-tree-node__expand-icon {
  padding-left: 0 !important;
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: $primary-color !important;
  background-color: #e5eeff !important;
}

.el-descriptions__label {
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #999999 !important;
  text-align: right !important;
}

.el-descriptions__content {
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #333333 !important;
  text-align: left !important;
}

.valid_link:hover {
  cursor: pointer !important;
  color: $primary-color !important;
  text-decoration: underline !important;
}

.el-range-editor.el-input__wrapper {
  padding: 0 17px !important;
}

.el-popup-parent--hidden {
  width: 100% !important;
}

.el-cascader__tags .el-tag {
  color: $primary-color !important;
  background-color: #dbe8ff !important;
}

.el-tag .el-tag__close:hover {
  background-color: transparent !important;
  color: var(--el-tag-text-color) !important;
}

.el-tag.el-tag--info {
  color: $primary-color !important;
  background-color: #dbe8ff !important;
}

.el-popover.el-popper.search-history-wrap {
  padding: 0px !important;
  margin: 0px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  z-index: 99 !important;
  .search-history-container {
    width: 100%;
    padding: 20px 0px 10px 0px;
    box-sizing: border-box;
    .title-wrap {
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #888888;
      .title {
        font-weight: bold;
      }
      .clear {
        display: flex;
        align-items: center;
        cursor: pointer;
        &:hover {
          color: $primary-color;
        }
      }
    }
    .history-list {
      margin-top: 5px;
      display: flex;
      flex-wrap: wrap;
      .history-item {
        padding: 0 20px;
        width: 50%;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #888888;
        &:nth-child(2n + 1) {
          border-right: 1px solid #e8e8e8;
        }
        &:hover {
          color: $primary-color;
          background: #f8f9fb;
          cursor: pointer;
          .icon {
            display: block;
          }
        }
        .title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .icon {
          flex-shrink: 0;
          margin-left: 10px;
          cursor: pointer;
          color: #888888;
          display: none;
          &:hover {
            color: $primary-color;
          }
        }
      }
    }
  }
}

.cascader-fold .el-cascader__tags .el-tag:first-child {
  max-width: 300px !important;
}
.no-header-dialog {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body  {
    padding: 0px !important;
    min-height: 788px !important;
    max-height: calc(100vh - 50px) !important;
  }
}
.el-overlay.is-message-box{
  z-index: 9999 !important;
}
.el-message{
  z-index: 99999 !important;
}