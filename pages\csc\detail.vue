<template>
  <div class="csc-wrap">
    <div class="csc-header">
      <div class="csc-header-container main-wrap">
        <div class="title">客户服务中心</div>
        <div class="desc">为您提供一站式的咨询和自动服务</div>
      </div>
    </div>
    <div class="csc-container main-wrap">
      <div class="flex flex-ai-center">
        <i class="iconfont icon-remenhuida c-primary" :style="{'font-size':'20px'}"></i>
        <div class="f-18 f-bold c-33 ml-10">{{data.title}}</div>
      </div>
      <div class="mt-15 pl-30">
        <div class="f-14 c-88" v-html="data.answer"></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getCscDetail } from '@/api/csc'
const { query } = useRoute()

interface IItem {
  id: string
  title: string
  answer: string
}
const data = ref(<IItem>{})

const getData = async () => {
  if( !query.id) return
  try {
    const res: any = await getCscDetail(query.id as string)
    data.value = res.data || []
  } catch (error) {}
}

await getData()

</script>
<style lang="scss" scoped>
.csc-header {
  height: 380px;
  background: url("@/assets/images/csc/csc-header-bg.png") no-repeat center;
  background-size: 100% 100%;
  .csc-header-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    font-size: 16px;
    color: #333333;
    .title {
      margin: 60px 0 25px 0;
      font-weight: bold;
      font-size: 32px;
    }
    .desc{
      width: 638px;
      font-size: 16px;
      color: #333333;
      line-height: 26px;
    }
  }
}
.csc-container{
  padding: 40px 0px;

}
</style>