<template>
  <div>
    <el-form-item prop="phonenumber">
      <el-input
        v-model="loginForm.phonenumber"
        type="text"
        size="large"
        autocomplete="new-password"
        placeholder="请输入手机号码"
        maxlength="11"
      >
        <template #prefix>
          <div class="p-num">
            <span>+86</span>
            <div class="line"></div>
          </div>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item class="mt-30" prop="smsCode">
      <el-input
        v-model="loginForm.smsCode"
        type="text"
        size="large"
        autocomplete="new-password"
        placeholder="请输入验证码"
        maxlength="6"
        @keyup.enter="handleLogin"
      >
        <template #append>
          <div v-if="!time ||time == 0" @click="handleVerify" class="verify">获取验证码</div>
          <div v-else class="down-timer">{{time}}s 后重新获取</div>
        </template>
      </el-input>
    </el-form-item>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps(['loginForm','loginRef'])
const emit = defineEmits(['update:loginForm','handleLogin','handleVerify'])


const { time, getMobileCode } = useMobileCode()

const handleLogin = () => {
  emit('handleLogin')
}
const handleVerify = () => {
  if(time.value == 0) {
    props.loginRef.validateField("phonenumber", (valid: boolean) => {
      if (valid) {
        let isSend = false; // 放重复点击
        if(isSend) return;
        
        getMobileCode(props.loginForm.phonenumber, 1).then(res => {
          isSend = true;
        }).finally(() => {
          isSend = false;
        })
      }
    });
  }
}

</script>