<template>
  <div class="descriptions">
    <div v-for="(item, index) in props.data" :key="index" class="descriptions-item">
      <div class="descriptions-item-label" :style="'min-width:' + props.minWidth + 'px'">{{ item.label }}</div>
      <div v-if="item.fieldName" class="descriptions-item-content">{{ props.form[item.fieldName] || '-' }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface DataItem {
    label: string;
    fieldName: string;
  }

  const props = defineProps({
    form: {
      required: true,
      type: Object,
      default: () => {
        return {};
      },
    },
    data: {
      required: true,
      type: Array as PropType<DataItem[]>,
    },
    minWidth: {
      type: Number,
      default: 100,
    },
  });
</script>

<style lang="scss" scoped>
  .descriptions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0 20px;

    &-item {
      font-size: 14px;
      display: flex;
      padding: 10px 0;
      box-sizing: border-box;
      overflow: hidden;

      &-label {
        width: auto;
        color: #999;
        text-align: right;
        word-wrap: break-word;
        overflow-wrap: break-word;
        overflow: hidden;
      }

      &-content {
        flex: 1;
        color: #333;
        text-align: left;
        word-wrap: break-word;
        overflow-wrap: break-word;
        overflow: hidden;
      }
    }
  }
</style>
