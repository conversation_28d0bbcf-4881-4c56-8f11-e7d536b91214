<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="form.keyword"
                maxlength="50"
                placeholder="请输入标准号、名称关键词检索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleSearchMoreCut" class="search-more">{{ searchMore ? '普通检索' : '高级检索' }}</div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
        <el-form ref="formRef" :model="form" inline class="form-inline mt-30" label-width="auto" size="large">
          <el-form-item prop="standardTypeList" label="标准类型" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.standardTypeList">
              <el-checkbox v-for="item in standardTypeOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item prop="standardStatusList" label="标准状态" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.standardStatusList">
              <el-checkbox v-for="item in standardStatusOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div v-if="searchMore" class="form-inline">
            <el-form-item label="发布日期">
              <el-date-picker
                @change="handlePublishDate"
                @clear="getData('pageNum')"
                v-model="publishDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="实施日期">
              <el-date-picker
                @change="handleExecuteDate"
                @clear="getData('pageNum')"
                v-model="executeDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <div class="flex flex-sb flex-ai-center mb-15">
          <div class="flex-1 flex flex-ai-center f-14 c-33 table-statistic">
            <div
              @click="handleClearSort"
              class="pointer"
              :class="publishDateSort == null && executeDateSort == null ? 'c-primary' : 'c-33'"
            >
              默认
            </div>
            <div @click="handlePublishDateSort" class="flex flex-ai-center pointer">
              <span :class="publishDateSort == null ? 'c-33' : 'c-primary'">发布日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="publishDateSort && publishDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!publishDateSort && publishDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
              </div>
            </div>
            <div @click="handleExecuteDateSort" class="flex flex-ai-center pointer">
              <span :class="executeDateSort == null ? 'c-33' : 'c-primary'">实施日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="executeDateSort && executeDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!executeDateSort && executeDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
              </div>
            </div>
            <div class="ml-30">
              为您找到
              <span class="c-primary">{{ tableTotal }}</span>
              条相关标准，更多内容请使用条件检索！
            </div>
          </div>
          <div class="flex flex-ai-center ml-100">
            <span class="f-14 c-33">展示方式</span>
            <span
              @click="handleShowFormCut('card')"
              class="iconfont f-20 ml-20 icon-liebiao pointer"
              :class="showForm == 'card' ? 'c-primary' : ''"
            ></span>
            <span
              @click="handleShowFormCut('table')"
              class="iconfont f-20 ml-20 icon-chart01 pointer"
              :class="showForm == 'table' ? 'c-primary' : ''"
            ></span>
          </div>
        </div>
        <RetrievalCard
          v-show="showForm == 'card'"
          :params="form"
          :tableData="tableData"
          url="/retrieval/internationDetail"
          @updateData="getData"
        />
        <RetrievalTable
          v-show="showForm == 'table'"
          :params="form"
          :tableData="tableData"
          url="/retrieval/internationDetail"
          @updateData="getData"
          class="mb-30"
        />
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '国际国外标准检索_国外标准查询_国际标准查询_标信查平台',
    meta: [
      { name: 'keywords', content: '标准检索，国际国外标准检索，国际标准查询，标准查询' },
      {
        name: 'description',
        content:
          '标信查平台及时收录各行业标准，国家标准，地方标准、团体标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务，并为用户提供一些标准增值服务。',
      },
    ],
  });

  import { getDicts } from '@/api/common';
  import { getInternationList } from '@/api/retrieval/internation';
  import { type IStandardInfo, type IDicts, type IResponseData } from '@/types';
  import type { FormInstance } from 'element-plus';

  interface IForm {
    keyword?: string;
    pageNum: number;
    pageSize: number;
    standardTypeList?: string[];
    standardStatusList?: string[];
    startPublishDate?: string;
    endPublishDate?: string;
    startExecuteDate?: string;
    endExecuteDate?: string;
    queryDateType?: number | string;
  }

  const formRef = ref<FormInstance>();
  const showForm = ref('card');
  const searchMore = ref(false);
  const standardTypeOptions = ref<IDicts[]>([]);
  const standardStatusOptions = ref<IDicts[]>([]);
  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
  });
  const publishDate = ref([]);
  const publishDateSort = ref<boolean | null>(null);
  const executeDate = ref([]);
  const executeDateSort = ref<boolean | null>(null);
  const tableData = ref<IStandardInfo[]>([]);
  const tableTotal = ref(0);

  let { rows, total } = <IResponseData>await getInternationList(form.value);
  tableData.value = rows || [];
  tableTotal.value = total || 0;

  let standardTypeDict = <IResponseData>await getDicts('bxc_standard_type');
  standardTypeOptions.value = standardTypeDict.data.filter((item: any) => item.dictValue == 5 || item.dictValue == 6);

  let standardStatusDict = <IResponseData>await getDicts('bxc_standard_status');
  standardStatusOptions.value = standardStatusDict.data;

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    form.value.startPublishDate = publishDate.value && publishDate.value.length > 0 ? publishDate.value[0] : '';
    form.value.endPublishDate = publishDate.value && publishDate.value.length > 0 ? publishDate.value[1] : '';
    form.value.startExecuteDate = executeDate.value && executeDate.value.length > 0 ? executeDate.value[0] : '';
    form.value.endExecuteDate = executeDate.value && executeDate.value.length > 0 ? executeDate.value[1] : '';
    let { rows, total } = <IResponseData>await useHttp.get('/search/sdc/stdStandardForeign/list', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleReset = () => {
    if (formRef.value) formRef.value.resetFields();
    publishDate.value = [];
    executeDate.value = [];
    form.value.keyword = '';
    handleClearSort()
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(101);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    form.value.keyword = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    form.value.keyword = form.value.keyword?.replace(/^\s+|\s+$/g, '');
    if (form.value.keyword) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: form.value.keyword,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };

  const handlePublishDate = () => {
    if (publishDate.value && publishDate.value.length > 0) getData('pageNum');
  };

  const handleExecuteDate = () => {
    if (executeDate.value && executeDate.value.length > 0) getData('pageNum');
  };

  const handleSearchMoreCut = () => {
    searchMore.value = !searchMore.value;
  };

  const handleClearSort = () => {
    publishDateSort.value = null;
    executeDateSort.value = null;
    form.value.queryDateType = '';
    getData('pageNum');
  };

  const handlePublishDateSort = () => {
    executeDateSort.value = null;
    publishDateSort.value = publishDateSort.value ? false : true;
    form.value.queryDateType = publishDateSort.value ? 1 : 0;
    getData('pageNum');
  };

  const handleExecuteDateSort = () => {
    publishDateSort.value = null;
    executeDateSort.value = executeDateSort.value ? false : true;
    form.value.queryDateType = executeDateSort.value ? 3 : 2;
    getData('pageNum');
  };

  const handleShowFormCut = (data: string) => {
    showForm.value = data;
    getData();
  };
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    :deep(.el-input) {
      width: 95% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 20px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-more {
      position: absolute;
      right: -70px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }

    &-reset {
      position: absolute;
      right: -113px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }

  .table-statistic {
    .pointer {
      margin-right: 30px;
    }
  }

  .c-CECECE {
    color: #cecece;
  }

  .icon-shang,
  .icon-xia {
    font-size: 10px !important;
  }

  :deep(.el-checkbox) {
    margin-right: 0;
    background-color: #fff !important;
  }

  :deep(.el-checkbox:hover) {
    color: #ffffff;
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox-group .el-checkbox__input) {
    display: none;
  }

  :deep(.el-checkbox.is-bordered.is-checked) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox.is-bordered.is-checked .el-checkbox__label) {
    color: #ffffff;
  }

  :deep(.el-checkbox.is-bordered + .el-checkbox.is-bordered) {
    margin-left: 15px;
  }

  :deep(.el-checkbox-group) {
    display: flex;
    justify-content: space-between;
  }
</style>
