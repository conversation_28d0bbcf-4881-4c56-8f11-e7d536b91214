<template>
  <el-popover
    v-model="visible"
    popper-class="nav-notice-wrap"
    placement="bottom"
    trigger="hover"
    :teleported="false"
    :offset="21"
    :show-arrow="false"
    ref="navNoticeRef"
  >
    <div class="nav-notice-container scroller-bar-style">
      <div class="title-wrap">
        <div class="left">
          <div class="title">消息通知</div>
          <div v-if="!isEmpty" class="msg">({{unReadCount}}条未读消息)</div>
        </div>
        <div v-if="!isEmpty" class="right">
          <i class="iconfont icon-yidu pointer" @click="handleRead()"></i>
        </div>
      </div>
      <div v-if="!isEmpty" class="msg-list">
        <div v-for="item in dataList" :key="item.messageId" @click="handleDetail(item)" class="msg-item">
          <img src="@/assets/images/layout/unread.png" alt="">
          <div class="item-content">
            <div class="title">{{item.messageTitle}}</div>
            <div class="date">{{parseTime(item.createTime,'{y}年{m}月{d}日')}}</div>
          </div>
        </div>
      </div>
      <div v-else>
        <BxcEmpty :img="defaultImg" imgWidth="220px" height="220px" content="暂无未读消息..." />
      </div>
      <div class="line"></div>
      <div class="msg-all">
        <span @click="handleDetail()" class="pointer">查看全部>></span>
      </div>
    </div>
    <template #reference>
      <div class="flex">
        <el-badge :value="unReadCount" :show-zero="false" :max="99">
          <i @mouseenter="refreshData" class="iconfont icon-xiaoxi pointer" style="font-size: 22px;"></i>
        </el-badge>
      </div>
    </template>
  </el-popover>
  
</template>
<script lang="ts" setup>
import { getMessageList, setMessageRead } from '@/api/home'
import defaultImg from '@/assets/images/layout/empty.png'

interface IMessageItem {
  messageId: string
  messageTitle: string
  createTime: string
}

const navNoticeRef = ref()
const visible = ref(false)
const unReadCount = ref(0)
const dataList = ref(<IMessageItem[]>[])
const timer = ref(0)

const isEmpty = computed(() => {
  return dataList.value.length == 0
})

const getList = async () => {
  try {
    const res: any = await getMessageList()
    dataList.value = res.rows || []
    unReadCount.value = res.otherInfo.unReadCount || 0
  } catch (error) {}
}

const handleDetail = (item?: any) => {
  navNoticeRef.value.hide()
  if(!item) {
    location.href = '/user-center/message/index'
  } else {
    location.href = `/user-center/message/index?id=${item.messageId}`
    setTimeout(() => {
      getList()
    }, 2000)
  }
}
const handleRead = () => {
  setMessageRead().then(res => {
    getList()
  })
}
const refreshData = () => {
  if(timer.value) {
    window.clearTimeout(timer.value)
  }
  timer.value = window.setTimeout(() => {
    getList()
    timer.value = 0
  }, 500)
}
await getList()
</script>
<style lang="scss">
.el-popover.el-popper.nav-notice-wrap {
  padding: 0px !important;
  margin: 0px !important;
  width: 420px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  // inset: 60px auto auto auto !important;  

  .nav-notice-container {
    margin: 20px;
    overflow-y: auto;
    
    .line{
      width: 100%;
      height: 1px;
      background: #E8E8E8;
    }
    .title-wrap{
      display: flex;
      justify-content: space-between;
      .left{
        flex: 1;
        display: flex;
        align-items: center;
        .title{
          font-weight: bold;
          font-size: 18px;
          color: #333333;
        }
        .msg{
          margin-left: 5px;
          font-size: 14px;
          color: #FF0000;
        }
      }
      .right{
        color: $primary-color;
      }
    }
    .msg-list{
      .msg-item{
        height: 70px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #E8E8E8;
        
        &:last-child{
          border-bottom: none;
        }
        i{
          width: 30px;
        }
        .item-content{
          flex: 1;
          margin-left: 15px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          font-size: 14px;
          overflow: hidden;
          cursor: pointer;
          .title{
            font-weight: bold;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .date{
            margin-top: 5px;
            color: #999999;
          }
        }
      }
    }
    .msg-all{
      margin-top: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $primary-color;
      font-size: 14px;
    }
  }
  
}
</style>