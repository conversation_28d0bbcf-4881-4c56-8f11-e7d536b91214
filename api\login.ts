export const login = (body: object) => {
  return request.post('/auth/cloudLogin',body)
}
export const mobileLogin = (body: object) => {
  return request.post('/auth/smsCloudLogin',body)
}
export const getUserInfo = () => {
  return request.get('/auth/cloudUser/getInfo',{})
}
export const logout = () => {
  return request.delete("/auth/cloudLogout")
}
export const getCode = (params = {}) => {
  return request.get('/resource/sms/code',params)
}
export const register = (body: object) => {
  return request.post('/auth/cloudRegister',body)
}
export const resetPassword = (body: object) => {
  return request.put('/auth/forgetPassword',body)
}