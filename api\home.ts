export const getNoticeList = () => {
  return request.get('/notice/cloud/friendlyLink/list')
}
// 查询友情链接列表
export const getFriendlyLink = () => {
  return request.get('/business/friendlyLink/enableList')
}
// 获取热搜词
export const getHotList = () => {
  return request.get('/business/hotSearch/getHot')
}
// 查询标准体系列表
export const getStandardSystemList = (params = {}) => {
  return request.get('/business/standardSystem/homepageSystemList',params)
}
// 查询未读消息消息top和未读消息数
export const getMessageList = (params = {}) => {
  return request.get('/business/messagePublish/unReadCountTopAndCount',params)
}
// 标记已读/全部标记
export const setMessageRead = (messageId?: string) => {
  let url = messageId ? '/business/messagePublish/readTag?messageId=' + messageId : '/business/messagePublish/readTag';
  return request.put(url)
}
// 标准动态
export const getStandardDynamicList = (params = {}) => {
  return request.get('/search/sdc/stdStandard/dynamic',params)
}
// 查询标准公告列表
export const getStandardNoticeList = (params = {}) => {
  return request.get('/search/process/stdNotice/list',params)
}
// 查询国外国际标准列表
export const getStandardForeignList = (params = {}) => {
  return request.get('/search/sdc/stdStandardForeign/list',params)
}