<template>
  <div class="comparison-wrap">
    <div class="comparison-header">
      <div class="comparison-header-container main-wrap">
        <div class="title">标准比对 </div>
        <div class="desc">
          依托于结构化标准文本数据和AI比对算法模型，提供基于文本的多维度一键比对、差异定位与标注、比对结果一键导出等功能。
        </div>
        <div class="desc mt-5">
          基于标准全文、目次、题条段落、表格、图片、公式、术语、指标等各维度，精准定位原标准与比对标准之间的差异，显著降低用户在修订标准与不同标准之间的差异化审核、阅读成本。
        </div>
        <div class="list">
          <div class="item">AI模型</div>
          <div class="item">多维度</div>
          <div class="item">比对报告</div>
          <div class="item">差异标注</div>
        </div>
        <div class="order-btn" @click="handleComparison">
          开始比对
        </div>
      </div>
    </div>
    <!-- 产品核心功能 -->
    <div class="func-wrap">
      <div class="main-wrap">
        <div class="v-title">产品核心功能</div>
        <div class="func-content">
          <div class="func-list">
            <div v-for="(item,index) in funcList" :key="index" @click="handleFuncClick(index)" class="func-item" :class="{'current-func-item': index == currentFuncIndex}">
              <div class="item-title">{{item.name}}</div>
              <div v-show="index == currentFuncIndex" class="item-desc">{{item.desc}}</div>
            </div>
          </div>
          <div class="func-img">
            <img :src="currentFuncImg" alt="">
          </div>
        </div>
      </div>
      
    </div>
    <!-- 数据比对维度 -->
    <div class="dimension-wrap">
      <div class="main-wrap">
        <div class="v-title">数据比对维度</div>
        <div class="dimension-list">
          <div v-for="(item,index) in dimensionList" :key="index" class="dimension-item">
            <img :src="item.icon" alt="">
            <div class="item-title">{{item.name}}</div>
            <div class="item-desc">{{item.desc}}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 标准比对流程 -->
    <div class="process-wrap main-wrap">
      <div class="v-title">标准比对流程</div>
      <ul class="process-list">
        <li v-for="(item,index) in processList" :key="index" class="process-item">
          <template v-if="item.name">
            <img class="icon-img" :src="item.icon" alt="">
            <div class="name">{{item.name}}</div>
            <img class="num-img" :src="item.numIcon" alt="">
          </template>
          <template v-else>
            <img class="gap-img" :src="item.icon" alt="">
          </template>
        </li>
      </ul>
    </div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleComparison"/>
  </div>
</template>
<script setup lang="ts">
import FUNC1 from '@/assets/images/tools/comparison/func-1.png'
import FUNC2 from '@/assets/images/tools/comparison/func-2.png'
import FUNC3 from '@/assets/images/tools/comparison/func-3.png'
import FUNC4 from '@/assets/images/tools/comparison/func-4.png'
import DIMENSION1 from '@/assets/images/tools/comparison/dimension-1.png'
import DIMENSION2 from '@/assets/images/tools/comparison/dimension-2.png'
import DIMENSION3 from '@/assets/images/tools/comparison/dimension-3.png'
import DIMENSION4 from '@/assets/images/tools/comparison/dimension-4.png'
import DIMENSION5 from '@/assets/images/tools/comparison/dimension-5.png'
import DIMENSION6 from '@/assets/images/tools/comparison/dimension-6.png'
import DIMENSION7 from '@/assets/images/tools/comparison/dimension-7.png'
import DIMENSION8 from '@/assets/images/tools/comparison/dimension-8.png'
import PROCESS1 from '@/assets/images/tools/comparison/process-1.png'
import PROCESS2 from '@/assets/images/tools/comparison/process-2.png'
import PROCESS3 from '@/assets/images/tools/comparison/process-3.png'
import PROCESS4 from '@/assets/images/tools/comparison/process-4.png'
import PROCESSNUM1 from '@/assets/images/tools/comparison/process-num-1.png'
import PROCESSNUM2 from '@/assets/images/tools/comparison/process-num-2.png'
import PROCESSNUM3 from '@/assets/images/tools/comparison/process-num-3.png'
import PROCESSNUM4 from '@/assets/images/tools/comparison/process-num-4.png'
import PROCESSGAP from '@/assets/images/tools/comparison/process-gap.png'
import { useUserStore } from '@/store/userStore'

const { setUseHead } = useSeo()
setUseHead('/tools/comparison')
const userStore = useUserStore()

const openLogin = ref(false)
const funcList = [
  {
    name: 'AI内容比对模型',
    desc: '依托于AI尝试语义分析技术与模型深度思考能力，构建针对内容分析的专业化差异比对模型',
    img: FUNC1,
  },
  {
    name: '差异比对标注',
    desc: '自动定位与高亮新增、编辑、删除的内容变化，并精准定位差异点，高效提升文本差异阅读效率',
    img: FUNC2,
  },
  {
    name: '多维度数据差异分析',
    desc: '基于标准全文、题条、目次、图片、表格、公式、指标等多维度，对标准进行全面、深度分析',
    img: FUNC3,
  },
  {
    name: '差异化比对报告',
    desc: '通过对比对模型对标准内容的多维度、差异化比对分析与标注，生成可视化标准比对分析报告',
    img: FUNC4,
  }
]
const dimensionList = [{
  name: '标准全文',
  desc: '标准全文数据的直观比对，全文差异一览无余',
  icon: DIMENSION1
},{
  name: '标准目次',
  desc: '标准目次数据结果变化标注，差异版块先知',
  icon: DIMENSION2
},{
  name: '题条段落',
  desc: '精准题条段落比对，范围精准，数据清晰',
  icon: DIMENSION3
},{
  name: '标准表格',
  desc: '标准全量表格数据提取，表格一对一精准比对',
  icon: DIMENSION4
},{
  name: '标准图片',
  desc: '标准全量图片数据提取，图片一对一精准比对',
  icon: DIMENSION5
},{
  name: '标准公式',
  desc: '标准全量公式数据提取，公式一对一精准比对',
  icon: DIMENSION6
},{
  name: '标准术语',
  desc: '标准术语标注，快速了解差异术语内容',
  icon: DIMENSION7
},{
  name: '标准指标',
  desc: '标准核心指标提炼，指标差异全掌握',
  icon: DIMENSION8
}]
const processList = [{
  icon: PROCESS1,
  name: '选择比对标准',
  numIcon: PROCESSNUM1
},{
  icon: PROCESSGAP,
},{
  icon: PROCESS2,
  name: '确定比对维度',
  numIcon: PROCESSNUM2
},{
  icon: PROCESSGAP,
},{
  icon: PROCESS3,
  name: '查看比对结果',
  numIcon: PROCESSNUM3
},{
  icon: PROCESSGAP,
},{
  icon: PROCESS4,
  name: '下载比对报告',
  numIcon: PROCESSNUM4
}]

const currentFuncImg = ref(FUNC1)
const currentFuncIndex = ref(0)

const handleFuncClick = (index: number) => {
  currentFuncIndex.value = index
  currentFuncImg.value = funcList[index].img 
}
const handleComparison = () => {
  if(!userStore.token){
    openLogin.value = true
  }else{
    openLogin.value = false
    location.href = '/tools/comparison/vs'
  }
}
</script>
<style lang="scss" scoped>
.comparison-header {
  height: 380px;
  background: url("@/assets/images/tools/comparison/comparison-header-bg.png") no-repeat center;
  background-size: 100% 100%;
  .comparison-header-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    font-size: 16px;
    color: #333333;
    .title {
      margin: 60px 0 20px 0;
      font-weight: bold;
      font-size: 32px;
    }
    .desc{
      width: 675px;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
    }
    .list{
      margin-top: 20px;
      display: flex;
      gap: 10px;
     .item{
        padding: 6px 12px;
        background: #DBE8FF;
        border-radius: 3px;
        font-size: 14px;
        color: $primary-color;
      }
    }
    .order-btn{
      margin-top: 20px;
      width: 240px;
      height: 40px;
      background: $primary-color;
      border-radius: 20px;
      font-size: 16px;
      color: #FFFFFF;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      &:hover{
        background: $primary-hover-color;
      }
    }
  }
}
.v-title{
  padding-top: 65px;
  font-weight: bold;
  font-size: 26px;
  color: #333333;
  text-align: center;
}
.func-wrap{
  background-color: #F8F9FB;
  padding-bottom: 60px;
  .func-content{
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    gap: 70px;
    .func-list{
      width: 400px;
      height: 382px;
      display: flex;
      flex-direction: column;
      background: #FFFFFF;
      border-radius: 5px;
      overflow: hidden;
      .func-item{
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        border-bottom: 1px solid #E8E8E8;
        font-size: 16px;
        color: #333333;
        padding: 0px 25px;
        overflow: hidden;
        .item-title{
          font-weight: bold;
        }
        &:hover{
          cursor: pointer;
        }
        &:last-child{
          border-bottom: none;
        }
      }
      .current-func-item{
        height: 140px;
        background: url("@/assets/images/tools/comparison/func-bg.png") no-repeat center;
        background-size: 100% 100%;
        .item-title{
          color: #FFFFFF;
        }
        .item-desc{
          margin-top: 10px;
          color: #FFFFFF;
          font-size: 14px; 
          line-height: 22px;
        }
      }
    }
    .func-img{
      flex: 1;

    }
  }
}
.dimension-wrap{
  box-sizing: border-box;
  .dimension-list{
    margin-top: 45px;
    display: flex;
    flex-wrap: wrap;
    gap: 30px 0px;
    .dimension-item{
      flex: 0 0 25%;
      background: #FFFFFF;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      img{
        width: 55px;
      }
      .item-title{
        margin-top: 15px;
        font-weight: bold;
        font-size: 16px;
        color: #333333;
      }
      .item-desc{
        margin-top: 12px;
        width: 185px;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        text-align: center;
      }
    }
  }
}
.process-wrap{
  .process-list{
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    margin-bottom: 90px;
    .process-item{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .icon-img{
        width: 62px;
        height: 77px;
      }
      .name{
        margin-top: 15px;
        font-size: 16px;
        color: #333333;
      }
     .num-img{
        height: 29px;
        margin-top: 20px;
      } 
      .gap-img{
        width: 73px;
        height: 25px; 
      }
    }
  }
}
</style>