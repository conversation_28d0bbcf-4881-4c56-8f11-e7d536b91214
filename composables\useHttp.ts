import { useUserStore } from '@/store/userStore';

export interface ResOptions<T> {
  data: T;
  code: number;
  message: string;
}

const handleError = <T>(response: ResOptions<T>) => {
  const { $modal } = useNuxtApp();
  const handleMap: { [key: number]: () => void } = {
    404: () => $modal.msgError(response.message),
    500: () => $modal.msgError(response.message),
    403: () => $modal.msgError(response.message),
    401: () => {
      $modal
        .confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', '重新登录')
        .then(() => {
          useUserStore()
            .logout()
            .then(() => {
              location.href = '/login';
            });
        })
        .catch(() => {});
    },
  };
  handleMap[response.code] ? handleMap[response.code]() : '';
};

const fetch = $fetch.create({
  onRequest({ options }) {
    const {
      public: { baseURL },
    } = useRuntimeConfig();
    options.baseURL = baseURL;
    const token = useUserStore().token;
    options.headers = new Headers(options.headers);
    options.headers.set('Authorization', `Bearer ${token}`);
    options.retry = false;
  },
  onResponse({ response }) {
    if (response.headers.get('content-disposition') && response.status === 200) return response;
    if (response._data.code !== 200) {
      handleError(response._data);
      return Promise.reject(response._data.message ?? null);
    }
    return response._data;
  },
  // 错误处理
  onResponseError({ response }) {
    handleError(response._data);
    return Promise.reject(response?._data.message ?? null);
  },
});

export const useHttp = {
  get: <T>(url: string, params?: any) => {
    return fetch<T>(url, { method: 'get', params });
  },

  post: <T>(url: string, body?: any) => {
    return fetch<T>(url, { method: 'post', body });
  },

  put: <T>(url: string, body?: any) => {
    return fetch<T>(url, { method: 'put', body });
  },

  delete: <T>(url: string, body?: any) => {
    return fetch<T>(url, { method: 'delete', body });
  },
};
