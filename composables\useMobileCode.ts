import { getCode } from '@/api/login';

export default () => {
  let timer: NodeJS.Timeout | number;
  const time = ref(0);
  const codeCountDown = () => {
    timer = setInterval(() => {
      if (time.value > 1) {
        time.value--;
      } else {
        clearInterval(timer);
        timer = 0;
        time.value = 0;
      }
    }, 1000);
  }

  const getMobileCode = (phonenumber: string,checkType = 0) => {
    return new Promise((resolve, reject) => {
      if (!timer || timer == 0) {
        getCode({ phonenumber, checkType }).then((res) => {
          time.value = 60
          codeCountDown();
          resolve(res)
        }).catch((error) => {
          reject(error)
        });
      }else{
        reject()
      }
    })
  }

  return {
    time,
    codeCountDown,
    getMobileCode
  }
}