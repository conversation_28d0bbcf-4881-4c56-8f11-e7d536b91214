<template>
  <div>
    <div class="flex flex-sb flex-ai-center">
      <div class="f-20 f-bold">对应标准</div>
      <div v-if="props.data && props.data.length > 10" class="flex flex-column flex-sa flex-ai-center pointer">
        <span @click="handClick" class="iconfont c-88" :class="portion ? 'icon-xiala' : 'icon-shangla'"></span>
      </div>
    </div>
    <div class="drafter mt-20">
      <div @click="handleStandardCode(item)" v-for="item in list" :key="item.id" class="drafter-label">
        <span class="iconfont icon-tixi f-14 c-primary"></span>
        <RetrievalToolTip :text="item.label" :className="item.id ? 'valid_link' : ''" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { handleJump } from '@/utils/common';

  type Item = {
    id: number | string;
    label: string;
  };

  const props = defineProps({
    data: {
      required: true,
      type: Array as () => Item[],
    },
  });

  const portion = ref(true);

  let list = computed(() => {
    return portion.value ? props.data.slice(0, 10) : props.data;
  });

  const handClick = () => {
    portion.value = !portion.value;
  };

  const handleStandardCode = (row: any) => {
    if (row.id) handleJump('/retrieval/domesticDetail', { id: row.id });
  };
</script>

<style lang="scss" scoped>
  .drafter {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0 10px;

    &-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333333;
      margin-bottom: 20px;
      min-width: 0;
      max-width: 100%;

      &-icon {
        display: block;
        width: 30px;
        height: 30px;
      }
    }
  }
</style>
