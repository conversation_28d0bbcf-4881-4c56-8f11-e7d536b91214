<template>
  <div class="footer-wrap">
    <div class="footer-container">
      <div class="footer-bussiness">
        <div class="company-info">
          <img src="@/assets/images/layout/footer-logo.png" alt="">
          <div class="desc">
            我们围绕支撑经济社会高质量发展，面向企业提供全产业链条的质量技术综合服务，面向政府提供政策研究、服务平台研发等服务，建设成为全国知名的标准化科研服务机构。
          </div>
          <div class="phone">
            <i class="iconfont icon-dianhuahover" style="font-size:24px;" />
            <span>************</span>
          </div>
        </div>
        <dl v-for="(item,index) in dataList" :key="index">
          <dt>{{item.name}}</dt>
          <dd v-for="(row, i) in item.children" :key="i">
            <NuxtLink class="row-title" :to="row.path" @click.native.prevent="handleLink(row)">{{row.name}}</NuxtLink>
          </dd>
        </dl>
        <div class="other">
          <img class="wx-official" src="@/assets/images/layout/wx-official.png" alt="">
          <div class="name">（公众号）</div>
        </div>
      </div>
      <div class="line">
      </div>
      <div class="footer-about">
        <NuxtLink to="http://biaoxincha.com/" target="_blank">关于我们</NuxtLink>
        <NuxtLink to="/clause/law">法律声明</NuxtLink>
        <NuxtLink to="/clause/privacy">隐私政策</NuxtLink>
        <NuxtLink to="/clause/copyright">版权声明</NuxtLink>
      </div>
      <div class="footer-links">
        <NuxtLink v-for="item in links" :key="item.id" :to="item.linkAddress" target="_blank">{{item.siteName}}</NuxtLink>
      </div>
      <div class="footer-copyright">
        Copyright © 2013-2025 BiaoXinCha . All Rights Reserved. 标信查 版权所有 | 备案号 <NuxtLink to="https://beian.mps.gov.cn/#/query/webSearch" target="_blank">皖ICP备2022010709号</NuxtLink>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getFriendlyLink } from '@/api/home'

const { $modal } = useNuxtApp()

const dataList = [
  {
    name: '数据检索',
    children: [{
      name: '标准检索',
      path: '/retrieval/domestic',
    },{
      name: '国际国外标准检索',
      path: '/retrieval/internation',
    },{
      name: '标准公告检索',
      path: '/retrieval/announcement',
    },{
      name: '标准法规检索',
      path: '/retrieval/law',
    },{
      name: '计划标准检索',
      path: '/retrieval/plan',
    },{
      name: 'TC委员会检索',
      path: '/retrieval/tc',
    }]
  },
  {
    name: '数据服务',
    children: [{
      name: '标准托管',
      path: '/data/trusteeship',
    },{
      name: '标准查新',
      path: '/data/search',
    },{
      name: '标准动态',
      path: '/data/dynamic',
    },{
      name: '行业标准体系',
      path: '/data/system',
    },{
      name: '标准专题',
      path: '/data/special',
    }]
  },
  {
    name: '场景&解决方案',
    children: [{
      name: '标准数字化管理系统',
      path: '/solution/esms',
    },{
      name: '院校标准经工程综合实训系统',
      path: '',
    },{
      name: '智能制造试验公共服务平台',
      path: '',
    },{
      name: '团标准协作平台',
      path: '',
    },{
      name: '行业标准体系智慧平台',
      path: '',
    }]
  },
]

interface ILink {
  id: string,
  siteName: string,
  linkAddress: string,
}
const links = ref<ILink[]>([])

const handleLink = (row: any) => {
  if(!row.path) {
    $modal.msgWarning('正在建设中，敬请期待')
  }
}
const getLinks = () => {
  getFriendlyLink().then((res: any) => {
    nextTick(() => {
      links.value = res.rows || []
    })
  })
}

getLinks()

</script>
<style lang="scss" scoped>
  .footer-container{
    padding: 40px 0px 25px 0px;
    font-size: 14px;
    color: #D7D7D7;
    a{
      cursor: pointer;
      color: #D7D7D7;
      &:hover{
        color: $primary-color;
      }
    }
    .footer-bussiness{
      display: flex;
      margin-bottom: 30px;
     .company-info{
        flex: 2;
       .desc{
          margin-top: 30px;
          margin-right: 80px;
          line-height: 25px;
        }
       .phone{
          margin-top: 30px;
          color: #FFFFFF;
          span{
            margin-left: 10px;
            font-size: 22px;
          }
       }
      }
      dl{
        flex: 1;
        dt{
          font-weight: bold;
          font-size: 16px;
          color: #FFFFFF;
        }
        dd{
          padding: 0;
          margin: 20px 0 0 0;
          font-size: 14px;
          color: #888888;
        }
      }
     .other{
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;
        gap: 10px 0;
        .wx-official{
          width: 115px;
          height: 115px;
        }
        .name{
          margin-right: 22px;
        }
      }

    }
    .line{
      width: 100%;
      height: 1px;
      background: #E8E8E8;
      opacity: 0.12;
    }
    .footer-about{
      margin-top: 30px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px 20px;
    }
    .footer-links{
      margin-top: 30px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px 20px;
    }
    .footer-copyright{
      margin-top: 30px;
    }
  }
</style>