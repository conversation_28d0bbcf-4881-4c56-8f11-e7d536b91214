<template>
  <div class="intro">
    <div class="f-22 c-33 f-bold">{{ form.sampleCode || '-' }} | {{ form.sampleName || '-' }}</div>
    <div v-if="form.sampleNameEn" class="f-16 c-99 mt-10">{{ form.sampleNameEn }}</div>
    <div class="flex mt-15">
      <div :class="getStatusColor(statusToString(form.sampleStatus))">
        {{ form.sampleStatusName }}
      </div>
    </div>
    <div class="flex flex-center pb-30 mt-50">
      <div class="intro-progress">
        <div class="intro-progress-time c-primary">
          {{ form.valuingDate }}
        </div>
        <span class="iconfont icon-xuanzhong f-16 c-primary"></span>
        <div class="intro-progress-title c-primary f-bold">定值日期</div>
      </div>
      <div class="intro-line bgc-primary"></div>
      <div class="intro-progress">
        <div class="intro-progress-time c-primary">
          {{ form.approvalDate }}
        </div>
        <span class="iconfont icon-xuanzhong f16 c-primary"></span>
        <div class="intro-progress-title c-primary f-bold">批准日期</div>
      </div>
      <div class="intro-line bgc-primary"></div>
      <div class="intro-progress">
        <div class="intro-progress-time c-primary">
          {{ form.validityEndDate }}
        </div>
        <span class="iconfont icon-xuanzhong f16 c-primary"></span>
        <div class="intro-progress-title c-primary f-bold">有效截止日期</div>
      </div>
    </div>
    <el-divider />
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'green';
        break;
      case 1:
        return 'red';
        break;
      default:
        return 'green';
        break;
    }
  };
</script>

<style lang="scss" scoped>
  .intro {
    &-progress {
      position: relative;

      &-time {
        position: absolute;
        top: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        white-space: nowrap;
      }

      &-title {
        position: absolute;
        bottom: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 15px;
        white-space: nowrap;
      }
    }

    &-line {
      flex: 1;
      max-width: 300px;
      height: 4px;
    }
  }
</style>
