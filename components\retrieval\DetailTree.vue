<template>
  <div>
    <template v-if="userStore.token">
      <div v-if="treeData && treeData.length > 0" class="tree-content mb-20">
        <div class="flex flex-ai-center mb-10">
          <span class="iconfont icon-wenzhang f-20 c-primary"></span>
          <span class="f-18 c-33 f-bold ml-10 overflow-ellipsis">{{ props.standardCode }}</span>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          empty-text="暂无数据"
          :data="treeData"
          :highlight-current="true"
          :expand-on-click-node="false"
          :default-expanded-keys="treeHighLight"
          :props="{ children: 'children', label: 'name' }"
          @node-click="handleNodeClick"
          class="tree-content-item"
        >
          <template #default="{ node, data }">
            <div class="flex flex-ai-center" style="width: 100%">
              <span v-if="node.level == 1" class="iconfont icon-levels f-16 c-primary"></span>
              <RetrievalToolTip :text="node.label" :className="isHighlight(data) ? 'c-ff0000' : ''" />
            </div>
          </template>
        </el-tree>
        <RetrievalTreePreview
          v-if="dialogVisible"
          v-model:visible="dialogVisible"
          :standardName="props.standardName"
          :standardCode="props.standardCode"
          :standardId="standardId"
          :treeId="treeId"
          :searchValue="searchValue"
        />
      </div>
    </template>
    <div v-else class="contents m-20">
      <img class="contents-icon" src="@/assets/images/retrieval/contents.png" alt="" />
      <div @click="handleLogin" class="contents-title">
        <span class="c-primary text-underline">登录</span>
        后即可查看标准目次
      </div>
    </div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="updateData" />
  </div>
</template>

<script lang="ts" setup>
  import { getDomesticTree, getDomesticTreeHighLight } from '@/api/retrieval/domestic';
  import { type IResponseData } from '@/types';
  import { ElTree } from 'element-plus';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();
  const route = useRoute();
  const emit = defineEmits(['updateData']);

  const props = defineProps({
    standardName: {
      type: String,
      default: '',
    },
    standardCode: {
      type: [String, Number],
      default: '',
    },
  });

  const openLogin = ref<boolean>(false);
  const treeRef = ref<InstanceType<typeof ElTree>>();
  const dialogVisible = ref<boolean>(false);
  const standardId = ref<string | number>('');
  const treeData = ref([]);
  const treeHighLight = ref([]);
  const treeId = ref('');
  const searchValue = ref('');

  standardId.value = route.query.id as string | number;
  searchValue.value = route.query?.content ? decodeURI(route.query.content as string | '') : '';

  const getContents = async () => {
    if (userStore.token) {
      let treeRes = <IResponseData>await getDomesticTree(route.query.id as string | number);
      treeData.value = treeRes.data.tree || [];

      if (searchValue.value) {
        let treeHighLightRes = <IResponseData>await getDomesticTreeHighLight({
          standardId: route.query.id,
          title: searchValue.value,
        });
        treeHighLight.value = treeHighLightRes.data || [];
      }
    }
  };

  const isHighlight = (data: any) => {
    return treeHighLight.value.some(n => n === data.id);
  };

  const handleNodeClick = (node: any) => {
    if (!node.children || node.children.length == 0) {
      treeId.value = node.id;
      dialogVisible.value = true;
    }
  };

  const handleLogin = () => {
    openLogin.value = true;
  };

  const updateData = () => {
    getContents();
    emit('updateData');
  };

  getContents();
</script>

<style lang="scss" scoped>
  .contents {
    position: relative;
    width: 100%;
    margin-bottom: 20px;

    &-icon {
      display: block;
      width: 100%;
    }

    &-title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 16px;
      color: #fff;
      cursor: pointer;
    }
  }

  .tree {
    &-content {
      padding: 25px 15px 15px;
      box-sizing: border-box;
      border: 1px solid #e8e8e8;
      overflow: hidden;

      &-item {
        overflow-y: scroll;
        max-height: 600px;

        &::-webkit-scrollbar {
          width: 0;
        }
      }
    }
  }
</style>
