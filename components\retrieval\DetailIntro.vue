<template>
  <div class="intro">
    <div class="f-18 c-33 f-bold">{{ form.standardCode || '-' }} | {{ form.standardName || '-' }}</div>
    <div v-if="form.standardNameEn" class="f-14 c-99 mt-10">{{ form.standardNameEn }}</div>
    <div class="flex mt-15">
      <div v-if="form.standardTypeName" :class="getStatusColor('blue')" class="mr-10">{{ form.standardTypeName }}</div>
      <div v-if="form.standardStatusName" :class="getStatusColor(statusToString(form.standardStatus))" class="mr-10">
        {{ form.standardStatusName }}
      </div>
      <div v-if="form.standardAttrName" :class="getStatusColor('purple')" class="mr-10">{{ form.standardAttrName }}</div>
      <div v-if="form.standardTextPages" :class="getStatusColor('orange')">{{ form.standardTextPages }}页</div>
    </div>
    <div class="flex flex-center pb-30" :class="form.publishDate ? 'mt-50' : 'mt-30'">
      <div class="intro-progress">
        <div
          v-if="form.publishDate"
          class="intro-progress-time"
          :class="[0, 1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-99'"
        >
          {{ form.publishDate }}
        </div>
        <span
          class="iconfont icon-xuanzhong f-16"
          :class="[0, 1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <div
          class="intro-progress-title"
          :class="[0, 1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary f-bold' : 'c-99'"
        >
          发布
        </div>
      </div>
      <div class="intro-line" :class="[1, 3, 4].includes(Number(form.standardStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="intro-progress">
        <div
          v-if="form.executeDate"
          class="intro-progress-time"
          :class="[1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-99'"
        >
          {{ form.executeDate }}
        </div>
        <span
          class="iconfont icon-xuanzhong f16"
          :class="[1, 3, , 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <div
          class="intro-progress-title"
          :class="[1, 3, , 4].includes(Number(form.standardStatus)) ? 'c-primary f-bold' : 'c-99'"
        >
          实施
        </div>
      </div>
      <div class="intro-line" :class="[3, 4].includes(Number(form.standardStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="intro-progress">
        <div
          v-if="form.repealDate && (form.standardStatus == 3 || form.standardStatus == 4)"
          class="intro-progress-time"
          :class="[3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-99'"
        >
          {{ form.repealDate }}
        </div>
        <span
          class="iconfont icon-xuanzhong f16"
          :class="[3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <div class="intro-progress-title" :class="[3, 4].includes(Number(form.standardStatus)) ? 'c-primary f-bold' : 'c-99'">
          废止
        </div>
      </div>
    </div>
    <el-divider />
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };
</script>

<style lang="scss" scoped>
  .intro {
    &-progress {
      position: relative;

      &-time {
        position: absolute;
        top: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        white-space: nowrap;
      }

      &-title {
        position: absolute;
        bottom: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 15px;
        white-space: nowrap;
      }
    }

    &-line {
      flex: 1;
      max-width: 300px;
      height: 4px;
    }
  }

  .c-gray {
    color: #c8c8c8;
  }

  .bgc-gray {
    background-color: #c8c8c8;
  }
</style>
