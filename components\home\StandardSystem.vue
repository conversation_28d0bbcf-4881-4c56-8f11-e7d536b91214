<template>
  <div class="standard-wrap">
    <div class="h-title-wrap">
        <h3 class="title">行业标准体系</h3>
        <div class="more-btn" @click="handleDetail()">
          <span>查看更多</span><el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    <div class="standard-container">
      <div class="standard-list">
        <div v-for="item in standardList" :key="item.id" class="standard-item">
          <div class="name overflow-ellipsis">{{item.systemName}}</div>
          <div class="industry overflow-ellipsis">所属行业：{{item.industryTypeName}}</div>
          <div class="node">产业节点：{{item.nodeCount || 0}}</div>
          <div class="desc">{{item.systemDescription}}</div>
          <div class="more" @click="handleDetail(item)"><span>了解更多</span><el-icon><Right /></el-icon></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getStandardSystemList } from '@/api/home'
import type { IStandardInfo } from '@/types'

const standardList = ref(<IStandardInfo[]>[])

const getData = async () => {
  try {
    let params = {
      pageNum: 1,
      pageSize: 4,
      systemType: '0'
    }
    const res: any = await getStandardSystemList(params)
    standardList.value = res.rows || []
  } catch (error) {}
}

const handleDetail = (item?: any) => {
  if(!item) {
    navigateTo('/data/system')
  } else {
    navigateTo(`/data/systemDetail?id=${item.id}`)
  }
}

await getData()
</script>
<style lang="scss" scoped>
.standard-wrap {
  margin-top: 70px;
  background: url('@/assets/images/home/<USER>') no-repeat center;
  background-size: 100% 100%;
  height: 625px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .h-title-wrap{
    margin: 0 auto;
    margin-top: -40px;
    width: 1200px;
  }
  .standard-container{
    margin: 0 auto;
    width: 1200px;
    background: url('@/assets/images/home/<USER>') no-repeat center;
    background-size: 100% 100%;
    height: 408px;
    .standard-list{
      display: flex;
      color: #FFFFFF;
      font-size: 14px;
      height: 408px;
      .standard-item{
        flex: 0 0 25%;
        padding: 60px 25px 40px 25px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
        &:hover{
          background: url('@/assets/images/home/<USER>') no-repeat center;
          background-size: 100% 100%;
        }

        .name{
          font-weight: bold;
          font-size: 18px;
        }
        .industry{
          margin-top: 20px;
        }
        .node{
          margin-top: 10px;
        }
        .desc{
          margin-top: 45px;
          line-height: 22px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          line-clamp: 4;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
        }
        .more{
          position: absolute;
          left: 25px;
          bottom: 40px;
          width: 120px;
          height: 34px;
          background: $primary-color;
          color: #FFFFFF;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          span{
            margin-right: 5px;
          }
          &:hover{
            background: $primary-hover-color !important;
          }
        }
      }
    }
  }
}

</style>