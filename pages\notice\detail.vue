<template>
  <div class="main-wrap">
    <div class="notice-detail-wrap">
      <div class="title">{{data.title}}</div>
      <div class="date">{{parseTime(data.publishDate,'{y}-{m}-{d}')}}</div>
      <div class="line"></div>
      <div class="content" v-html="data.content"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getNoticeDetail } from '@/api/notice'
import type { INoticeInfo } from '@/types'

const { query } = useRoute()
const data = ref(<INoticeInfo>{})

const getData = () => {
  if(!query.id) return

  getNoticeDetail(<string>query.id).then((res: any) => {
    nextTick(() => {
      data.value = res.data || {}
    })
  })
}
getData()
</script>
<style lang="scss" scoped>
.notice-detail-wrap{
  margin-top: 60px;
  padding: 40px 0px;
  .title{
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    color: #333333;
  }
  .date{
    margin-top: 10px;
    text-align: center;
    font-size: 14px;
    color: #888888;
  }
  .line{
    margin-top: 10px;
    width: 100%;
    height: 1px;
    background: #E8E8E8;
  }
  .content{
    margin-top: 20px;
    font-size: 14px;
    color: #333333;
    line-height: 24px;
  }
}
</style>