<template>
  <el-tooltip ref="toolTipRef" :content="props.text" effect="dark" :disabled="!tooltipFlag" :placement="props.placement">
    <div class="over-flow" :class="props.className" @mouseenter="getElementWidth">{{ props.text }}</div>
  </el-tooltip>
</template>

<script lang="ts" setup>
  const props = defineProps({
    text: {
      required: true,
      type: String,
      default: '',
    },
    placement: { type: String, default: 'top-start' },
    className: { type: String },
  });

  const toolTipRef = ref<any>(null);
  const tooltipFlag = ref(false);

  const getElementWidth = (event: any) => {
    const pElement = event.currentTarget;
    if (pElement.offsetParent !== null) {
      const width = pElement.clientWidth;
      const imgWidth = toolTipRef.value.$el.parentNode.querySelector('img')
        ? toolTipRef.value.$el.parentNode.querySelector('img').clientWidth
        : 0;
      const parentWidth = toolTipRef.value.$el.parentNode.clientWidth;
      if (width + imgWidth >= parentWidth) {
        tooltipFlag.value = true;
      } else {
        tooltipFlag.value = false;
      }
    } else {
      console.log('Element is not visible');
    }
  };
</script>

<style scoped>
  .over-flow {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    padding-left: 10px;
  }
</style>
