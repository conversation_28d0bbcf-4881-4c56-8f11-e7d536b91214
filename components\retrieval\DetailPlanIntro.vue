<template>
  <div class="intro">
    <div class="f-18 c-33 f-bold">{{ form.planNumber || '-' }} | {{ form.entryName || '-' }}</div>
    <div v-if="form.entryNameEn" class="f-14 c-99 mt-10">{{ form.entryNameEn }}</div>
    <div class="flex mt-15">
      <div v-if="form.typeName" :class="getStatusColor('blue')" class="mr-10">{{ form.typeName }}</div>
      <div v-if="form.projectStatusName" :class="getStatusColor(statusToString(form.projectStatus))" class="mr-10">
        {{ form.status }}
      </div>
      <div v-if="form.amendName" :class="getStatusColor(form.amend ? 'green' : 'blue')" class="mr-10">
        {{ form.amendName }}
      </div>
      <div v-if="form.standardAttrName" :class="getStatusColor('purple')">{{ form.standardAttrName }}</div>
    </div>
    <div class="flex flex-center pb-30" :class="form.projectStatus ? 'mt-50' : 'mt-30'">
      <div class="intro-progress">
        <div class="intro-progress-time" :class="[0, 1, 2, 3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-99'">
          起草
        </div>
        <span v-if="form.terminationStatus == 999 && form.projectStatus == 0" class="iconfont icon-xuanzhong f16 c-ff0000"></span>
        <span
          v-else
          class="iconfont icon-xuanzhong f-16"
          :class="[0, 1, 2, 3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <template v-if="form.terminationStatus == 999 && form.projectStatus == 0">
          <div class="intro-progress-title c-ff0000">终止</div>
        </template>
      </div>
      <div class="intro-line" :class="[1, 2, 3, 4].includes(Number(form.projectStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="intro-progress">
        <div class="intro-progress-time" :class="[1, 2, 3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-99'">
          征求意见
        </div>
        <span v-if="form.terminationStatus == 999 && form.projectStatus == 1" class="iconfont icon-xuanzhong f16 c-ff0000"></span>
        <span
          v-else
          class="iconfont icon-xuanzhong f16"
          :class="[1, 2, 3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <template v-if="form.terminationStatus == 999 && form.projectStatus == 1">
          <div class="intro-progress-title c-ff0000">终止</div>
        </template>
      </div>
      <div class="intro-line" :class="[2, 3, 4].includes(Number(form.projectStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="intro-progress">
        <div class="intro-progress-time" :class="[2, 3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-99'">审查</div>
        <span v-if="form.terminationStatus == 999 && form.projectStatus == 2" class="iconfont icon-xuanzhong f16 c-ff0000"></span>
        <span
          v-else
          class="iconfont icon-xuanzhong f16"
          :class="[2, 3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <template v-if="form.terminationStatus == 999 && form.projectStatus == 2">
          <div class="intro-progress-title c-ff0000">终止</div>
        </template>
      </div>
      <div class="intro-line" :class="[3, 4].includes(Number(form.projectStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="intro-progress">
        <div class="intro-progress-time" :class="[3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-99'">批准</div>
        <span v-if="form.terminationStatus == 999 && form.projectStatus == 3" class="iconfont icon-xuanzhong f16 c-ff0000"></span>
        <span
          v-else
          class="iconfont icon-xuanzhong f16"
          :class="[3, 4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <template v-if="form.terminationStatus == 999 && form.projectStatus == 3">
          <div class="intro-progress-title c-ff0000">终止</div>
        </template>
      </div>
      <div class="intro-line" :class="[4].includes(Number(form.projectStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="intro-progress">
        <div class="intro-progress-time" :class="[4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-99'">发布</div>
        <span v-if="form.terminationStatus == 999 && form.projectStatus == 4" class="iconfont icon-xuanzhong f16 c-ff0000"></span>
        <span
          v-else
          class="iconfont icon-xuanzhong f16"
          :class="[4].includes(Number(form.projectStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <template v-if="form.terminationStatus == 999 && form.projectStatus == 4">
          <div class="intro-progress-title c-ff0000">终止</div>
        </template>
      </div>
    </div>
    <el-divider />
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'blue';
        break;
      case 2:
        return 'orange';
        break;
      case 3:
        return 'green';
        break;
      case 4:
        return 'purple';
        break;
      case 999:
        return 'red';
        break;
      case -1:
        return 'yellow';
        break;
      default:
        return 'green';
        break;
    }
  };
</script>

<style lang="scss" scoped>
  .intro {
    &-progress {
      position: relative;

      &-time {
        position: absolute;
        top: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        white-space: nowrap;
      }

      &-title {
        position: absolute;
        bottom: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        white-space: nowrap;
      }
    }

    &-line {
      flex: 1;
      max-width: 160px;
      height: 4px;
    }
  }

  .c-gray {
    color: #c8c8c8;
  }

  .bgc-gray {
    background-color: #c8c8c8;
  }
</style>
