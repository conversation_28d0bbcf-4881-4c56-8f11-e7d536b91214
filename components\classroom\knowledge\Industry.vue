<template>
  <div>
    <div v-if="props.data && props.data.length > 0" class="industry">
      <div v-for="(item, index) in props.data" :key="index" class="industry-item">
        <img src="@/assets/images/classroom/category.png" class="industry-item-img" alt="" />
        <el-popover placement="bottom-start" :width="600" trigger="hover" popper-class="popover">
          <template #reference>
            <div @click="handleSearch(item)" class="industry-item-title flex-1">
              {{ item.name }}
            </div>
          </template>
          <template #default>
            <div v-if="item.children && item.children.length > 0" class="industry">
              <div
                @click="handleSearch(childrenItem)"
                v-for="(childrenItem, childrenIndex) in item.children"
                :key="childrenIndex"
                class="industry-item-title"
              >
                {{ childrenItem.name }}
              </div>
            </div>
            <BxcEmpty v-else />
          </template>
        </el-popover>
      </div>
    </div>
    <BxcEmpty v-else />
  </div>
</template>

<script setup lang="ts">
  import { handleJump } from '@/utils/common';

  interface dataType {
    id: string | number;
    name: string;
    code: string;
    children?: dataType[];
  }

  const props = defineProps({
    data: {
      type: Array as () => dataType[],
      default: () => [],
    },
  });

  const handleSearch = (row: dataType) => {
    handleJump('/classroom/knowledge/search', { economicTypeCodes: encodeURI(row.code) });
  };
</script>

<style scoped lang="scss">
  .industry {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20px 30px;
    border-radius: 0px 0px 3px 3px;
    overflow: hidden;

    &-item {
      display: flex;
      align-items: center;
      width: 100%;

      &-img {
        width: 20px;
        display: block;
        margin-right: 15px;
      }

      &-title {
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        line-clamp: 1;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: normal;
        word-wrap: break-word;
        word-break: break-word;
        cursor: pointer;

        &:hover {
          color: $primary-color !important;
          font-weight: 600 !important;
        }
      }
    }
  }
</style>

<style lang="scss">
  .popover.el-popover.el-popper {
    padding: 0 !important;
    border-radius: 8px;
    overflow: hidden;
    padding: 20px !important;
  }
</style>
