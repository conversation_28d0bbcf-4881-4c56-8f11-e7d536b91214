// 查询解析标准列表
export const getStandardList = (params = {}) => {
  return request.get('/search/compare/analysisList',params)
}
// 标准对比
export const getStandardCompare = (params = {}) => {
  return request.get('/search/compare/standardCompare',params,{timeout: 60*1000})
}
// 解析详情-树结构
export const getStandardTree = (params = {}) => {
  return request.get('/search/sdc/stdStandard/getAnalysisIndexDetail',params)
}
// 解析详情-根据索引ID解析当前及以下（包括目次、正文、pdf地址）
export const getStandardTreeContentById = (params = {}) => {
  return request.get('/search/sdc/stdStandard/getAnalysisDetailByIndex',params)
}
// 解析详情-全部（包括目次、正文、pdf地址）
export const getStandardTreeContent = (standardId: string) => {
  return request.get('/search/sdc/stdStandard/getAnalysisDetail/'+ standardId)
}