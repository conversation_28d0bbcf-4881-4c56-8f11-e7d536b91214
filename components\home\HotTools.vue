<template>
  <div class="hot-tools main-wrap">
    <div class="tools-list">
      <div
        class="tools-item"
        v-for="(item, index) in dataList"
        @click="handleLink(item)"
        :key="index"
      >
        <div class="item-icon">
          <img :src="item.icon" alt="" />
        </div>
        <div class="item-content">
          <div class="item-title">{{ item.title }}</div>
          <div class="item-desc">{{ item.subTitle }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import HOT1 from "@/assets/images/home/<USER>";
import HOT2 from "@/assets/images/home/<USER>";
import HOT3 from "@/assets/images/home/<USER>";
import HOT4 from "@/assets/images/home/<USER>";
import HOT5 from "@/assets/images/home/<USER>";
import HOT6 from "@/assets/images/home/<USER>";
import HOT7 from "@/assets/images/home/<USER>";
import HOT8 from "@/assets/images/home/<USER>";
import HOT9 from "@/assets/images/home/<USER>";
import HOT10 from "@/assets/images/home/<USER>";

const { $modal } = useNuxtApp();

const dataList = [
  {
    title: "标准查询",
    subTitle: "全量标准数据快捷查询",
    icon: HOT1,
    path: "/retrieval/domestic",
  },
  {
    title: "国外标准查询",
    subTitle: "国际、国外标准数据查询",
    icon: HOT2,
    path: "/retrieval/internation",
  },
  {
    title: "标准查新",
    subTitle: "快速与批量检索标准状态与替代关系数据",
    icon: HOT3,
    path: "/data/search",
  },
  {
    title: "标准托管",
    subTitle: "标准在线托管与动态监测预警服务",
    icon: HOT4,
    path: "/data/trusteeship",
  },
  {
    title: "行业标准体系",
    subTitle: "针对各行业建立科学、规范、有效的标准体系",
    icon: HOT5,
    path: "/data/system",
  },
  {
    title: "公告查询",
    subTitle: "标准发布/废止公告及关联标准信息查询",
    icon: HOT6,
    path: "/retrieval/announcement",
  },
  {
    title: "法规查询",
    subTitle: "标准相关的法律法规在线检索",
    icon: HOT7,
    path: "/retrieval/law",
  },
  {
    title: "计划标准查询",
    subTitle: "国行地团计划标准项目信息公示查询",
    icon: HOT8,
    path: "/retrieval/plan",
  },
  {
    title: "标准动态",
    subTitle: "实时跟踪标准发布、废止、替代动态",
    icon: HOT9,
    path: "/data/dynamic",
  },
  {
    title: "TC委员会查询",
    subTitle: "全国TC技术委员会目录及相关标准查询",
    icon: HOT10,
    path: "/retrieval/tc",
  },
];
const handleLink = (row: any) => {
  if (!row.path) {
    $modal.msgWarning("正在建设中，敬请期待");
    return;
  }
  navigateTo(row.path);
};
</script>
<style lang="scss" scoped>
.tools-list {
  margin-top: 50px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px 10px;
  .tools-item {
    width: 225px;
    height: 103px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.17);
    border-radius: 0px;
    padding: 20px 10px;
    display: flex;
    &:hover {
      background: url("@/assets/images/home/<USER>") no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      .item-content {
        .item-title {
          color: #ffffff;
        }
        .item-desc {
          color: #ffffff;
        }
      }
    }
    .item-icon {
      width: 31px;
      img {
        width: 31px;
      }
    }
    .item-content {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      .item-title {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
      }
      .item-desc {
        margin-top: 10px;
        font-size: 14px;
        color: #888888;
        line-height: 20px;
      }
    }
  }
}
</style>
