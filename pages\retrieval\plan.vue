<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="form.keyword"
                maxlength="50"
                placeholder="输入标准计划号或名称关键词检索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleSearchMoreCut" class="search-more">{{ searchMore ? '普通检索' : '高级检索' }}</div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
        <el-form ref="formRef" :model="form" inline class="form-inline mt-30" label-width="auto" size="large">
          <el-form-item prop="projectStatusList" label="状态" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.projectStatusList">
              <el-checkbox v-for="item in projectStatusOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item prop="typeList" label="计划类型" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.typeList">
              <el-checkbox v-for="item in typeOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div v-if="searchMore" class="form-inline">
            <el-form-item prop="amend" label="制修订">
              <el-select
                @change="handleSelect('amend')"
                @clear="getData('pageNum')"
                v-model="form.amend"
                clearable
                placeholder="请选择制修订"
              >
                <el-option
                  v-for="item in amendOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="下达日期">
              <el-date-picker
                @change="handleReleaseDate"
                @clear="getData('pageNum')"
                v-model="releaseDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item prop="standardTypeCodeIso" label="ICS分类">
              <el-cascader
                @visible-change="e => handleCascader(e, 'standardTypeCodeIso')"
                @clear="handleClearCascader"
                @remove-tag="handleClearCascader"
                v-model="form.standardTypeCodeIso"
                clearable
                filterable
                collapse-tags
                :show-all-levels="false"
                :options="ICSOptions"
                :props="defaultICSProps"
                placeholder="请选择国际标准分类号"
                class="cascader-fold"
              ></el-cascader>
            </el-form-item>
            <el-form-item prop="standardTypeCodeGbs" label="CCS分类">
              <el-cascader
                @visible-change="e => handleCascader(e, 'standardTypeCodeGbs')"
                @clear="getData('pageNum')"
                @remove-tag="getData('pageNum')"
                v-model="form.standardTypeCodeGbs"
                clearable
                filterable
                collapse-tags
                :show-all-levels="false"
                :options="CCSOptions"
                :props="defaultCCSProps"
                placeholder="请选择中国标准分类号"
                class="cascader-fold"
              ></el-cascader>
            </el-form-item>
            <el-form-item prop="standardAttr" label="标准性质">
              <el-select
                @change="handleComparison('standardAttr')"
                @clear="getData('pageNum')"
                v-model="form.standardAttr"
                clearable
                placeholder="请选择标准性质"
              >
                <el-option
                  v-for="item in standardAttrOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="registryUnit" label="归口单位">
              <el-input
                @blur="handleComparison('registryUnit')"
                @clear="getData('pageNum')"
                v-model="form.registryUnit"
                clearable
                placeholder="输入归口单位名称"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <div class="flex flex-sb flex-ai-center mb-15">
          <div class="flex-1 flex flex-ai-center f-14 c-33 table-statistic">
            <div @click="handleClearSort" class="pointer" :class="releaseDateSort == null ? 'c-primary' : 'c-33'">默认</div>
            <div @click="handlePublishDateSort" class="flex flex-ai-center pointer">
              <span :class="releaseDateSort == null ? 'c-33' : 'c-primary'">下达日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="releaseDateSort && releaseDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!releaseDateSort && releaseDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
              </div>
            </div>
            <div class="ml-30">
              为您找到
              <span class="c-primary">{{ tableTotal }}</span>
              条相关标准计划，更多内容请使用条件检索！
            </div>
          </div>
        </div>
        <el-table :data="tableData">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column type="index" label="序号" width="80">
            <template #default="{ $index }">
              {{ (form.pageNum - 1) * form.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="计划号" min-width="180">
            <template #default="{ row }">
              <span @click="handleJump('/retrieval/planDetail', { id: row.id })" class="c-primary pointer">
                {{ row.planNumber }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="entryName" show-overflow-tooltip label="项目名称" min-width="180" />
          <el-table-column prop="typeName" show-overflow-tooltip label="计划类型" min-width="120" />
          <el-table-column show-overflow-tooltip label="制修订" min-width="100">
            <template #default="{ row }">
              <span :class="getStatusColor(row.amend == 0 ? 'green' : 'blue', 'text')">{{ row.amendName }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="状态" min-width="100">
            <template #default="{ row }">
              <span v-if="row.terminationStatus == 999" :class="getStatusColor(statusToString(row.terminationStatus), 'text')">
                {{ row.terminationStatusName }}
              </span>
              <span v-else :class="getStatusColor(statusToString(row.projectStatus), 'text')">{{ row.projectStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="planReleaseDate" show-overflow-tooltip label="下达日期" min-width="150" />
          <el-table-column prop="registryUnit" show-overflow-tooltip label="归口单位" min-width="180" />
        </el-table>
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
          class="mt-30"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '计划标准检索_计划标准查询_国家标准计划_标信查平台',
    meta: [
      { name: 'keywords', content: '标准查询，计划标准检索，计划标准查询，国家标准计划' },
      {
        name: 'description',
        content:
          '标信查一站式标准服务平台，及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务，并为用户提供一些标准的增值服务。',
      },
    ],
  });

  import { getPlanList } from '@/api/retrieval/plan';
  import { getIcsList, getCcsList } from '@/api/retrieval/domestic';
  import { getDicts } from '@/api/common';
  import { type IPlanInfo, type IDicts, type IResponseData } from '@/types';
  import type { FormInstance } from 'element-plus';

  const route = useRoute();

  interface IForm {
    keyword?: string;
    pageNum: number;
    pageSize: number;
    typeList?: string[];
    projectStatusList?: string[];
    startPlanReleaseDate?: string;
    endPlanReleaseDate?: string;
    queryDateType?: number | string;
    [key: string]: any;
  }

  const formRef = ref<FormInstance>();
  const searchMore = ref(false);
  const projectStatusOptions = ref<IDicts[]>([]);
  const typeOptions = ref<IDicts[]>([]);
  const amendOptions = ref<IDicts[]>([]);
  const standardAttrOptions = ref<IDicts[]>([]);
  const ICSOptions = ref<IDicts[]>([]);
  const CCSOptions = ref<IDicts[]>([]);
  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
  });
  const releaseDate = ref([]);
  const releaseDateSort = ref<boolean | null>(null);
  const defaultICSProps = ref({
    checkStrictly: true,
    multiple: true,
    emitPath: false,
    label: 'name',
    value: 'icsTypeCode',
  });
  const defaultCCSProps = ref({
    checkStrictly: true,
    multiple: true,
    emitPath: false,
    label: 'name',
    value: 'ccsTypeCode',
  });
  const tableData = ref<IPlanInfo[]>([]);
  const tableTotal = ref(0);

  if (route.query.keyword) {
    form.value.keyword = decodeURI(route.query?.keyword as string) || '';
  }

  let { rows, total } = <IResponseData>await getPlanList(form.value);
  tableData.value = rows || [];
  tableTotal.value = total || 0;

  let projectStatusDict = <IResponseData>await getDicts('bxc_project_status');
  projectStatusOptions.value = projectStatusDict.data;

  let typeDict = <IResponseData>await getDicts('bxc_std_plan_type');
  typeOptions.value = typeDict.data;

  let amendDict = <IResponseData>await getDicts('bxc_standard_amend');
  amendOptions.value = amendDict.data;

  let standardAttrDict = <IResponseData>await getDicts('bxc_standard_attr');
  standardAttrOptions.value = standardAttrDict.data;

  let ICSRes = <IResponseData>await getIcsList();
  ICSOptions.value = ICSRes.data;

  let CCSRes = <IResponseData>await getCcsList();
  CCSOptions.value = CCSRes.data;

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'blue';
        break;
      case 2:
        return 'orange';
        break;
      case 3:
        return 'green';
        break;
      case 4:
        return 'purple';
        break;
      case 999:
        return 'red';
        break;
      case -1:
        return 'yellow';
        break;
      default:
        return 'green';
        break;
    }
  };

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    form.value.startPlanReleaseDate = releaseDate.value && releaseDate.value.length > 0 ? releaseDate.value[0] : '';
    form.value.endPlanReleaseDate = releaseDate.value && releaseDate.value.length > 0 ? releaseDate.value[1] : '';
    let { rows, total } = <IResponseData>await useHttp.get('/search/sdc/nationalStandardPlanQuery/list', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleReset = () => {
    if (formRef.value) formRef.value.resetFields();
    releaseDate.value = [];
    form.value.keyword = '';
    handleClearSort();
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(3);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    form.value.keyword = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    form.value.keyword = form.value.keyword?.replace(/^\s+|\s+$/g, '');
    if (form.value.keyword) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: form.value.keyword,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };

  let orginalForm = JSON.parse(JSON.stringify(form.value));
  const handleComparison = (fieldName: keyof typeof form.value) => {
    if (fieldName) {
      if (form.value[fieldName] != orginalForm[fieldName]) {
        getData('pageNum');
        orginalForm = JSON.parse(JSON.stringify(form.value));
      }
    }
  };

  const handleCascader = (event: any, fieldName: keyof typeof form.value) => {
    if (!event) handleComparison(fieldName);
  };

  const handleClearCascader = () => {
    nextTick(() => {
      getData('pageNum');
    });
  };

  const handleReleaseDate = () => {
    if (releaseDate.value && releaseDate.value.length > 0) getData('pageNum');
  };

  const handleSelect = (fieldName: keyof typeof form.value) => {
    if (form.value[fieldName]) getData('pageNum');
  };

  const handleSearchMoreCut = () => {
    searchMore.value = !searchMore.value;
  };

  const handleClearSort = () => {
    releaseDateSort.value = null;
    form.value.queryDateType = '';
    getData('pageNum');
  };

  const handlePublishDateSort = () => {
    releaseDateSort.value = releaseDateSort.value ? false : true;
    form.value.queryDateType = releaseDateSort.value ? 1 : 0;
    getData('pageNum');
  };
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    :deep(.el-input) {
      width: 95% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 20px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-more {
      position: absolute;
      right: -70px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }

    &-reset {
      position: absolute;
      right: -113px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }

  .table-statistic {
    .pointer {
      margin-right: 30px;
    }
  }

  .c-CECECE {
    color: #cecece;
  }

  .icon-shang,
  .icon-xia {
    font-size: 10px !important;
  }

  :deep(.el-checkbox) {
    margin-right: 0;
    background-color: #fff !important;
  }

  :deep(.el-checkbox:hover) {
    color: #ffffff;
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox-group .el-checkbox__input) {
    display: none;
  }

  :deep(.el-checkbox.is-bordered.is-checked) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox.is-bordered.is-checked .el-checkbox__label) {
    color: #ffffff;
  }

  :deep(.el-checkbox.is-bordered + .el-checkbox.is-bordered) {
    margin-left: 15px;
  }

  :deep(.el-checkbox-group) {
    display: flex;
    justify-content: space-between;
  }
</style>
