export default () => {
  type ScrollOffsets = {
    div1: number;
    div2: number;
  };
  const div1 = ref();
  const div2 = ref();
  const scrollOffsets = ref<ScrollOffsets>({ div1: 0, div2: 0 }); // 记录两个 div 的滚动偏移量
  let isScrolling = false; // 是否正在滚动，用于避免重复触发
  const syncScroll = ref(false);

  // 防抖函数
  const debounce = (fn:any, delay:any) => {
    let timer: any = null;
    return (...args: any) => {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => fn(...args), delay);
    };
  };
  // 处理滚动事件
  const handleScroll = (divNumber: number) => {
    if (!syncScroll.value || isScrolling) return;

    isScrolling = true; // 标记正在滚动

    const currentDiv = divNumber === 1 ? div1.value : div2.value;
    const otherDiv = divNumber === 1 ? div2.value : div1.value;

    // 计算滚动偏移量
    const scrollTop = currentDiv.scrollTop;
    const offset = scrollTop - scrollOffsets.value[`div${divNumber}` as keyof ScrollOffsets];

    // 同步滚动
    otherDiv.scrollTop += offset;

    // 更新滚动偏移量
    scrollOffsets.value[`div${divNumber}` as keyof ScrollOffsets] = scrollTop;

    // 重置滚动状态
    setTimeout(() => (isScrolling = false), 100); // 100ms 后重置状态
  };
  const handlescrollOffsets = () => {
    // 开启同步滚动时，记录当前滚动位置
    if(!div1.value || !div2.value) return;
    scrollOffsets.value.div1 = div1.value.scrollTop;
    scrollOffsets.value.div2 = div2.value.scrollTop;
  }
  debounce(handleScroll, 10);
  const setSyncScroll = (val: boolean) => {
    syncScroll.value = val; 
  }
  return {
    div1,
    div2,
    setSyncScroll,
    handleScroll,
    handlescrollOffsets
  }
}