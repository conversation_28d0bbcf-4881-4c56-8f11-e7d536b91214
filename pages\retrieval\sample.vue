<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="form.keyword"
                maxlength="50"
                placeholder="输入标准样品编号或名称关键词检索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleSearchMoreCut" class="search-more">{{ searchMore ? '普通检索' : '高级检索' }}</div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
        <el-form ref="formRef" :model="form" inline class="form-inline mt-30" label-width="100px" size="large">
          <el-form-item prop="sampleStatusList" label="状态" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.sampleStatusList">
              <el-checkbox v-for="item in sampleStatusOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div v-if="searchMore" class="form-inline">
            <el-form-item label="定值日期">
              <el-date-picker
                @change="handleValuingDate"
                @clear="getData('pageNum')"
                v-model="valuingDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="批准日期">
              <el-date-picker
                @change="handleApprovalDate"
                @clear="getData('pageNum')"
                v-model="approvalDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="有效截止日期">
              <el-date-picker
                @change="handleValidityEndDate"
                @clear="getData('pageNum')"
                v-model="validityEndDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item prop="registryUnit" label="归口单位">
              <el-input
                @blur="handleComparison('registryUnit')"
                @clear="getData('pageNum')"
                v-model="form.registryUnit"
                clearable
                placeholder="输入归口单位名称"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <div class="flex flex-sb flex-ai-center mb-15">
          <div class="flex-1 flex flex-ai-center f-14 c-33 table-statistic">
            <div
              @click="handleClearSort"
              class="pointer"
              :class="valuingDateSort == null && approvalDateSort == null && validityEndDateSort == null ? 'c-primary' : 'c-33'"
            >
              默认
            </div>
            <div @click="handleValuingDateSort" class="flex flex-ai-center pointer">
              <span :class="valuingDateSort == null ? 'c-33' : 'c-primary'">定值日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="valuingDateSort && valuingDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!valuingDateSort && valuingDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
              </div>
            </div>
            <div @click="handleValidityEndDateSort" class="flex flex-ai-center pointer">
              <span :class="validityEndDateSort == null ? 'c-33' : 'c-primary'">有效截止日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="validityEndDateSort && validityEndDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!validityEndDateSort && validityEndDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
              </div>
            </div>
            <div @click="handleApprovalDateSort" class="flex flex-ai-center pointer">
              <span :class="approvalDateSort == null ? 'c-33' : 'c-primary'">批准日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="approvalDateSort && approvalDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!approvalDateSort && approvalDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
              </div>
            </div>
            <div class="ml-30">
              为您找到
              <span class="c-primary">{{ tableTotal }}</span>
              条相关标准，更多内容请使用条件检索！
            </div>
          </div>
        </div>
        <el-table :data="tableData">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column type="index" label="序号" width="80">
            <template #default="{ $index }">
              {{ (form.pageNum - 1) * form.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="样品编号" min-width="150">
            <template #default="{ row }">
              <span @click="handleJump('/retrieval/sampleDetail', { id: row.id })" class="c-primary pointer">
                {{ row.sampleCode }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="sampleName" show-overflow-tooltip label="样品名称" />
          <el-table-column show-overflow-tooltip label="状态">
            <template #default="{ row }">
              <span :class="getStatusColor(statusToString(row.sampleStatus), 'text')">{{ row.sampleStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="valuingDate" show-overflow-tooltip label="定值日期" />
          <el-table-column prop="validityEndDate" show-overflow-tooltip label="有效截止日期" />
          <el-table-column prop="approvalDate" show-overflow-tooltip label="批准日期" />
          <el-table-column prop="registryUnit" show-overflow-tooltip label="归口单位" />
        </el-table>
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
          class="mt-30"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准样品检索_标准样品查询_国家标准样品查询_标信查平台',
    meta: [
      { name: 'keywords', content: '标准样品，标准样品检索，标准样品查询，国家标准样品查询' },
      {
        name: 'description',
        content:
          '标信查平台及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务，并为用户提供一些标准的增值服务。',
      },
    ],
  });

  import { getDicts } from '@/api/common';
  import { getSampleList } from '@/api/retrieval/sample';
  import { type ISampleInfo, type IDicts, type IResponseData } from '@/types';
  import type { FormInstance } from 'element-plus';

  interface IForm {
    keyword?: string;
    pageNum: number;
    pageSize: number;
    sampleStatusList?: string[];
    registryUnit?: string;
    startValuingDate?: string;
    endValuingDate?: string;
    startApprovalDate?: string;
    endApprovalDate?: string;
    startValidityEndDate?: string;
    endValidityEndDate?: string;
    queryDateType?: number | string;
  }

  const formRef = ref<FormInstance>();
  const searchMore = ref(false);
  const sampleStatusOptions = ref<IDicts[]>([]);
  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
  });
  const valuingDate = ref([]);
  const valuingDateSort = ref<boolean | null>(null);
  const approvalDate = ref([]);
  const approvalDateSort = ref<boolean | null>(null);
  const validityEndDate = ref([]);
  const validityEndDateSort = ref<boolean | null>(null);
  const tableData = ref<ISampleInfo[]>([]);
  const tableTotal = ref(0);

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'green';
        break;
      case 1:
        return 'red';
        break;
      default:
        return 'green';
        break;
    }
  };

  let { rows, total } = <IResponseData>await getSampleList(form.value);
  tableData.value = rows || [];
  tableTotal.value = total || 0;

  let sampleStatusDict = <IResponseData>await getDicts('bxc_std_sample_status');
  sampleStatusOptions.value = sampleStatusDict.data.filter((item: any) => item.dictValue != 5 && item.dictValue != 6);

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    form.value.startValuingDate = valuingDate.value && valuingDate.value.length > 0 ? valuingDate.value[0] : '';
    form.value.endValuingDate = valuingDate.value && valuingDate.value.length > 0 ? valuingDate.value[1] : '';
    form.value.startApprovalDate = approvalDate.value && approvalDate.value.length > 0 ? approvalDate.value[0] : '';
    form.value.endApprovalDate = approvalDate.value && approvalDate.value.length > 0 ? approvalDate.value[1] : '';
    form.value.startValidityEndDate = validityEndDate.value && validityEndDate.value.length > 0 ? validityEndDate.value[0] : '';
    form.value.endValidityEndDate = validityEndDate.value && validityEndDate.value.length > 0 ? validityEndDate.value[1] : '';
    let { rows, total } = <IResponseData>await useHttp.get('/search/sdc/stdSample/list', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleReset = () => {
    if (formRef.value) formRef.value.resetFields();
    valuingDate.value = [];
    approvalDate.value = [];
    validityEndDate.value = [];
    form.value.keyword = '';
    handleClearSort();
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(103);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    form.value.keyword = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    form.value.keyword = form.value.keyword?.replace(/^\s+|\s+$/g, '');
    if (form.value.keyword) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: form.value.keyword,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };

  let orginalForm = JSON.parse(JSON.stringify(form.value));
  const handleComparison = (fieldName: keyof typeof form.value) => {
    if (fieldName) {
      if (form.value[fieldName] != orginalForm[fieldName]) {
        getData('pageNum');
        orginalForm = JSON.parse(JSON.stringify(form.value));
      }
    }
  };

  const handleValuingDate = () => {
    if (valuingDate.value && valuingDate.value.length > 0) getData('pageNum');
  };

  const handleApprovalDate = () => {
    if (approvalDate.value && approvalDate.value.length > 0) getData('pageNum');
  };

  const handleValidityEndDate = () => {
    if (validityEndDate.value && validityEndDate.value.length > 0) getData('pageNum');
  };

  const handleSearchMoreCut = () => {
    searchMore.value = !searchMore.value;
  };

  const handleClearSort = () => {
    valuingDateSort.value = null;
    approvalDateSort.value = null;
    validityEndDateSort.value = null;
    form.value.queryDateType = '';
    getData('pageNum');
  };

  const handleValuingDateSort = () => {
    approvalDateSort.value = null;
    validityEndDateSort.value = null;
    valuingDateSort.value = valuingDateSort.value ? false : true;
    form.value.queryDateType = valuingDateSort.value ? 1 : 0;
    getData('pageNum');
  };

  const handleValidityEndDateSort = () => {
    valuingDateSort.value = null;
    approvalDateSort.value = null;
    validityEndDateSort.value = validityEndDateSort.value ? false : true;
    form.value.queryDateType = validityEndDateSort.value ? 3 : 2;
    getData('pageNum');
  };

  const handleApprovalDateSort = () => {
    valuingDateSort.value = null;
    validityEndDateSort.value = null;
    approvalDateSort.value = approvalDateSort.value ? false : true;
    form.value.queryDateType = approvalDateSort.value ? 5 : 4;
    getData('pageNum');
  };
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    :deep(.el-input) {
      width: 95% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 20px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-more {
      position: absolute;
      right: -70px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }

    &-reset {
      position: absolute;
      right: -113px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }

  .table-statistic {
    .pointer {
      margin-right: 30px;
    }
  }

  .c-CECECE {
    color: #cecece;
  }

  .icon-shang,
  .icon-xia {
    font-size: 10px !important;
  }

  :deep(.el-checkbox) {
    margin-right: 0;
    background-color: #fff !important;
  }

  :deep(.el-checkbox:hover) {
    color: #ffffff;
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox-group .el-checkbox__input) {
    display: none;
  }

  :deep(.el-checkbox.is-bordered.is-checked) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox.is-bordered.is-checked .el-checkbox__label) {
    color: #ffffff;
  }

  :deep(.el-checkbox.is-bordered + .el-checkbox.is-bordered) {
    margin-left: 15px;
  }

  :deep(.el-checkbox-group) {
    display: flex;
    justify-content: space-between;
  }
</style>
