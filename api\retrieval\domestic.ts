export const getIcsList = () => {
  return request.get('/search/process/icsType/tree');
};

export const getCcsList = () => {
  return request.get('/search/process/ccsType/tree');
};

export const getDomesticList = (params: object) => {
  return request.get('/search/sdc/stdStandard/list', params);
};

export const getDomesticDetail = (data: number | string) => {
  return request.get('/search/sdc/stdStandard/' + data);
};

export const getRecommendedList = (data: number | string) => {
  return request.get('/search/sdc/stdStandard/getRecommendedList/' + data);
};

export const getGraphDetail = (data: number | string) => {
  return request.get('/search/sdc/stdStandard/getStandardAtlas/' + data);
};

export const getDomesticTree = (data: number | string) => {
  return request.get('/search/sdc/stdStandard/getAnalysisDetail/' + data);
};

export const getDomesticTreeHighLight = (params: object) => {
  return request.get('/search/sdc/stdStandard/getHighLightAnalysisIndex', params);
};
