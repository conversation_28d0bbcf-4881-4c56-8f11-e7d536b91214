<template>
  <div class="container mt-60">
    <div class="main-wrap flex flex-sb">
      <div class="container-left">
        <RetrievalDetailTcIntro :form="form" />
        <div class="f-20 f-bold">基础信息</div>
        <RetrievalDetailDescriptions :form="form" :data="basicInfo" class="mt-15" />
        <div class="f-20 f-bold mt-30">秘书处信息</div>
        <RetrievalDetailDescriptions :form="form" :data="secretaryInfo" :minWidth="120" class="mt-15 mb-20" />
        <RetrievalDetailDrafter
          v-if="form.committeeList && form.committeeList.length > 0"
          :data="form.committeeList"
          title="委员"
        />
        <RetrievalDetailTcTable class="mt-30" :tableData="form.sdcNationalStandardPlanQueryVoList" />
      </div>
      <div class="container-right">
        <div class="container-right-title">
          <div>归口标准({{ tableData.length }})</div>
          <span
            @click="handleJump('/retrieval/domestic', { registryUnit: encodeURI(form.cnCommitteeName) })"
            class="iconfont icon-levels f-20 pointer"
          ></span>
        </div>
        <div class="container-right-card">
          <template v-if="tableData.length > 0">
            <div
              @click="handleJump('/retrieval/domesticDetail', { id: item.id })"
              v-for="item in tableData"
              :key="item.id"
              class="container-right-card-item"
            >
              <div class="flex flex-ai-center" style="width: 100%">
                <span :class="getStatusColor(statusToString(item.standardStatus), 'text')">
                  【{{ item.standardStatusName }}】
                </span>
                <span class="flex-1 overflow-ellipsis container-right-card-item-title">{{ item.standardCode }}</span>
              </div>
              <div class="mt-10 c-88 overflow-ellipsis" style="width: 100%">&nbsp;{{ item.standardName }}</div>
            </div>
          </template>
          <BxcEmpty v-else />
        </div>
      </div>
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :type="2" @updateData="getDetail" />
  </div>
</template>

<script setup lang="ts">
  import { getTcDetail, getRecommendedList } from '@/api/retrieval/tc';
  import { splitStrToArray, getStatusColor, handleJump } from '@/utils/common';
  import { type ITcInfo, type IResponseData } from '@/types';

  const route = useRoute();

  const form = ref<ITcInfo>({
    committeeNumber: '',
    cnCommitteeName: '',
    currentSecretaryGeneral: '',
    sessionNumber: 0,
    secretariatUnit: '',
    commissionerCnt: 0,
    responsibleProfessionalScope: '',
    draftersList: [],
    sdcNationalStandardPlanQueryVoList: [],
  });
  const basicInfo = reactive([
    { label: '委员会编号：', fieldName: 'committeeNumber' },
    { label: '委员会全称：', fieldName: 'cnCommitteeName' },
    { label: '委员会简称：', fieldName: 'committeeAbbreviation' },
    { label: '英文全称：', fieldName: 'enCommitteeName' },
    { label: '本届届号：', fieldName: 'sessionNumber' },
    { label: '筹建单位：', fieldName: 'preparationUnit' },
    { label: '业务指导单位：', fieldName: 'businessGuidanceUnit' },
    { label: '', fieldName: '' },
    { label: '负责专业范围：', fieldName: 'responsibleProfessionalScope' },
  ]);
  const secretaryInfo = reactive([
    { label: '秘书长：', fieldName: 'currentSecretaryGeneral' },
    { label: '秘书处所在单位：', fieldName: 'secretariatUnit' },
    { label: '所在区划：', fieldName: 'region' },
    { label: '通讯地址：', fieldName: 'mailingAddress' },
    { label: '邮编：', fieldName: 'postalCode' },
    { label: '联系人：', fieldName: 'contacts' },
    { label: '电话：', fieldName: 'phone' },
    { label: '传真：', fieldName: 'fax' },
    { label: '邮箱：', fieldName: 'email' },
  ]);
  const tableData = ref<ITcInfo[]>([]);

  let { data } = <IResponseData>await getTcDetail(route.query.id as string | number);
  if (data.committeeCommissionerList && data.committeeCommissionerList.length > 0) {
    data.committeeList = data.committeeCommissionerList.map((item: any) => item.commissionerName);
  }
  form.value = data || {};

  let recommendedData = <IResponseData>await getRecommendedList(route.query.id as string | number);
  tableData.value = recommendedData.data || [];

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/search/sdc/tcCommittee/' + route.query.id);
    if (data.committeeCommissionerList && data.committeeCommissionerList.length > 0) {
      data.committeeList = data.committeeCommissionerList.map((item: any) => item.commissionerName);
    }
    form.value = data || {};
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;

    &-left {
      width: 800px;
      overflow: hidden;
    }

    &-right {
      width: 360px;
      overflow: hidden;

      &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 20px;
        color: #fff;
        width: 100%;
        height: 55px;
        background-color: #f2a511;
        padding: 0 20px;
        box-sizing: border-box;
      }

      &-card {
        padding: 10px 18px;
        box-sizing: border-box;
        border: 1px solid #e8e8e8;

        &-item {
          font-size: 14px;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: self-start;
          overflow: hidden;
          cursor: pointer;

          &:hover &-title {
            color: $primary-color;
          }

          &:not(:first-child) {
            border-top: 1px solid #e5e8ef;
          }
        }
      }
    }
  }
</style>
