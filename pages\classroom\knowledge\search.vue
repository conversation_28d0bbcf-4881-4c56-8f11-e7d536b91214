<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="form.keyword"
                maxlength="50"
                placeholder="输入标准号、知识名称检索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
        <el-form ref="formRef" :model="form" inline class="form-inline mt-30" label-width="auto" size="large">
          <el-form-item label="按产业分类" prop="economicTypeCodes">
            <el-cascader
              @visible-change="e => handleCascader(e, 'economicTypeCodes')"
              @clear="handleClearCascader"
              v-model="form.economicTypeCodes"
              clearable
              filterable
              collapse-tags
              :options="industryOptions"
              :props="{ label: 'name', value: 'code', emitPath: false, checkStrictly: true }"
              placeholder="请选择"
              class="cascader-fold"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="按主题分类" prop="themeList" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.themeList">
              <el-checkbox v-for="item in themeOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <div class="flex flex-sb flex-ai-center mb-15">
          <div class="flex-1 flex flex-ai-center f-14 c-33 table-statistic">
            <div @click="handleClearSort" class="pointer" :class="releaseDateSort == null ? 'c-primary' : 'c-33'">默认</div>
            <div @click="handlePublishDateSort" class="pointer" :class="releaseDateSort == null ? 'c-33' : 'c-primary'">
              按点击量
            </div>
            <div class="ml-30">
              为您找到
              <span class="c-primary">{{ tableTotal }}</span>
              条相关标准知识
            </div>
          </div>
        </div>
        <template v-if="tableData && tableData.length > 0">
          <div
            @click="handleJump('/classroom/knowledge/detail', { id: item.id })"
            v-for="item in tableData"
            :key="item.id"
            class="card"
          >
            <img
              v-if="item.mainImageList && item.mainImageList.length > 0"
              :src="item.mainImageList[0].url"
              class="card-img"
              alt=""
            />
            <div class="card-right">
              <div class="card-right-title">{{ item.knowledgeName }}</div>
              <div
                v-if="item.themeName || (item.economicTypeNameList && item.economicTypeNameList.length > 0)"
                class="card-right-label"
              >
                <div v-if="item.themeName" :class="getStatusColor('green')" class="mr-10">{{ item.themeName }}</div>
                <template v-if="item.economicTypeNameList && item.economicTypeNameList.length > 0">
                  <div
                    v-for="(childItem, childIndex) in item.economicTypeNameList"
                    :key="childIndex"
                    class="card-right-label-item"
                    :class="getStatusColor('purple')"
                  >
                    {{ childItem }}
                  </div>
                </template>
              </div>
              <div class="f-14 c-33 overflow-ellipsis">关联标准：{{ item.standardCodeName }}</div>
              <div class="f-14 c-33">发布日期：{{ item.publishDate.split(' ')[0] }}</div>
            </div>
          </div>
        </template>
        <BxcEmpty v-else />
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
          class="mt-30"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准知识_标准课堂_标准知识课堂_标准学院_标信查平台',
    meta: [
      { name: 'keywords', content: '标准学院，标准解读，标准知识，标准课堂' },
      {
        name: 'description',
        content:
          '标信查平台标准学院频道，名师在线讲解标准知识、深入解读标准内容，及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务。',
      },
    ],
  });

  import { getNationalEconomicTypeTree, getKnowledgeList } from '@/api/classroom/knowledge';
  import { getDicts } from '@/api/common';
  import { type IKnowledgeInfo, type IDicts, type IResponseData } from '@/types';
  import { handleJump } from '@/utils/common';
  import type { FormInstance } from 'element-plus';

  const route = useRoute();
  const router = useRouter();

  interface IForm {
    keyword?: string;
    pageNum: number;
    pageSize: number;
    sort: number;
    economicTypeCodes?: string;
    themeList?: string[];
    [key: string]: any;
  }

  const formRef = ref<FormInstance>();
  const industryOptions = ref([]);
  const themeOptions = ref<IDicts[]>([]);
  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
    sort: 0,
  });
  const releaseDateSort = ref<boolean | null>(null);
  const tableData = ref<IKnowledgeInfo[]>([]);
  const tableTotal = ref(0);

  if (route.query.economicTypeCodes) {
    form.value.economicTypeCodes = decodeURI(route.query?.economicTypeCodes as string) || '';
  }

  if (route.query.theme) {
    form.value.themeList = route.query?.theme ? [decodeURI(route.query.theme as string)] : [];
  }

  let industryRes = <IResponseData>await getNationalEconomicTypeTree({ deep: 2 });
  industryOptions.value = industryRes.data || [];

  let themeRes = <IResponseData>await getDicts('bxc_knowledge_theme');
  themeOptions.value = themeRes.data || [];

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    let { rows, total } = <IResponseData>await getKnowledgeList(form.value);
    if (rows && rows.length > 0) {
      rows.forEach((item: any) => {
        item.economicTypeNameList = item.economicTypeName ? splitStrToArray(item.economicTypeName, ' | ') : [];
      });
    }
    tableData.value = rows || [];
    tableTotal.value = total || 0;
    nextTick(() => {
      router.replace({ path: route.path, query: {} });
    });
  };

  const handleReset = () => {
    if (formRef.value) formRef.value.resetFields();
    form.value.keyword = '';
    form.value.economicTypeCodes = '';
    form.value.themeList = [];
    getData('pageNum');
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(200);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    form.value.keyword = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    form.value.keyword = form.value.keyword?.replace(/^\s+|\s+$/g, '');
    if (form.value.keyword) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: form.value.keyword,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };

  let orginalForm = JSON.parse(JSON.stringify(form.value));
  const handleComparison = (fieldName: keyof typeof form.value) => {
    if (fieldName) {
      if (form.value[fieldName] != orginalForm[fieldName]) {
        getData('pageNum');
        orginalForm = JSON.parse(JSON.stringify(form.value));
      }
    }
  };

  const handleCascader = (event: any, fieldName: keyof typeof form.value) => {
    if (!event) handleComparison(fieldName);
  };

  const handleClearCascader = () => {
    nextTick(() => {
      getData('pageNum');
    });
  };

  const handleClearSort = () => {
    releaseDateSort.value = null;
    form.value.sort = 0;
    getData('pageNum');
  };

  const handlePublishDateSort = () => {
    releaseDateSort.value = true;
    form.value.sort = 1;
    getData('pageNum');
  };

  await getData();
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    :deep(.el-input) {
      width: 95% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 20px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-reset {
      position: absolute;
      right: -40px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }

  .table-statistic {
    .pointer {
      margin-right: 30px;
    }
  }

  .card {
    display: flex;
    padding: 25px;
    box-sizing: border-box;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    overflow: hidden;
    cursor: pointer;

    &:not(:last-child) {
      margin-bottom: 25px;
    }

    &:hover {
      box-shadow: 0px 0px 8px 1px rgba(0, 0, 0, 0.09);
    }

    &:hover &-right-title {
      color: $primary-color !important;
    }

    &-img {
      width: 254px;
      height: 141px;
    }

    &-right {
      flex: 1;
      margin-left: 20px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      overflow: hidden;

      &-title {
        font-size: 16px;
        color: #333;
        font-weight: 600;
      }

      &-label {
        display: flex;

        &-item {
          margin-right: 10px;
          max-width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          line-clamp: 1;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          white-space: normal;
          word-wrap: break-word;
          word-break: break-word;
        }
      }
    }
  }

  :deep(.el-checkbox) {
    margin-right: 0;
    background-color: #fff !important;
  }

  :deep(.el-checkbox:hover) {
    color: #ffffff;
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox-group .el-checkbox__input) {
    display: none;
  }

  :deep(.el-checkbox.is-bordered.is-checked) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox.is-bordered.is-checked .el-checkbox__label) {
    color: #ffffff;
  }

  :deep(.el-checkbox.is-bordered + .el-checkbox.is-bordered) {
    margin-left: 15px;
  }

  :deep(.el-checkbox-group) {
    display: flex;
    justify-content: space-between;
  }
</style>
