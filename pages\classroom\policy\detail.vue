<template>
  <div class="container mt-60">
    <div class="main-wrap">
      <div class="f-18 c-33 f-bold text-center">{{ form.policyTitle }}</div>
      <div class="flex flex-center f-14 c-88 mt-10">
        <div class="mr-60">来源：{{ form.source || '-' }}</div>
        <div class="mr-60">政策地域：{{ form.fullRegionName || '-' }}</div>
        <div class="mr-60">政策发布日期：{{ form.policyPublishDate || '-' }}</div>
      </div>
      <el-divider />
      <div v-html="form.content" class="app-richtext"></div>
      <template v-if="form.fileList && form.fileList.length > 0">
        <div class="f-20 f-bold mt-40">相关文件</div>
        <div
          @click="handlePreview(item.url)"
          v-for="(item, index) in form.fileList"
          :key="index"
          class="f-14 mt-20 mr-10 c-33 flex flex-ai-center"
        >
          <i class="iconfont icon-wenben f-18 c-primary"></i>
          <span class="text-underline ml-5">文件{{ index + 1 }}</span>
        </div>
      </template>
    </div>
    <BxcPreviewFile v-if="open" v-model:open="open" :url="url" />
  </div>
</template>

<script setup lang="ts">
  import { getPolicyDetail } from '@/api/classroom/policy';
  import { type IPolicyInfo, type IResponseData } from '@/types';

  const route = useRoute();

  const open = ref(false);
  const url = ref('');
  const form = ref<IPolicyInfo>({
    policyTitle: '',
    fullRegionName: '',
    source: '',
    policyPublishDate: '',
  });

  let { data } = <IResponseData>await getPolicyDetail(route.query.id as string | number);
  form.value = data || {};

  useHead({
    title: form.value.policyTitle,
    meta: [
      { name: 'keywords', content: '国家标准政策，地方标准政策，标准课堂，标准知识' },
      {
        name: 'description',
        content:
          '标信查一站式标准服务平台标准政策频道，涵盖全国和个地方标准化政策与扶持信息，及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务。',
      },
    ],
  });

  const handlePreview = (row: string) => {
    url.value = row;
    open.value = true;
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;
  }

  .text-underline {
    cursor: pointer;

    &:hover {
      color: $primary-color !important;
    }
  }

  :deep(.el-divider--horizontal) {
    margin: 12px 0 30px !important;
  }
</style>
