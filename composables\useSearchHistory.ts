import { useCookie } from 'nuxt/app'
import type { IHistoryInfo } from '@/types'
const kHistory = 'BXCSEARCHHISTORY'
const kHistoryMax = 10

export default () => {
  const historyAllList = useCookie(
    kHistory,
    {
      default: () => <IHistoryInfo[]>[]
    }
  )

  const addHistoryItem = (item: IHistoryInfo) => {
    if(!historyAllList.value || historyAllList.value.length == 0) {
      useCookie(kHistory, {
        default: () => <IHistoryInfo[]>[]
      });
    }
    const index = historyAllList.value.findIndex(row => row.t == item.t && row.q == item.q);

    if (index!== -1) {
      historyAllList.value.splice(index, 1);
    }
    // 限制数量 每种类型 最多10条
    let count = countType(item.t)
    if(count == kHistoryMax) {
      historyAllList.value.pop();
    }else if(count > kHistoryMax) {
      let i = 0
      for (let index = 0; index < historyAllList.value.length; index++) {
        if(historyAllList.value[index].t == item.t) {
          i++;
        }
        if(i >= kHistoryMax) {
          historyAllList.value.splice(index, 1)
          index--;
        }
      }
    }

    historyAllList.value.unshift(item);
  }
  const getHistory = (t: number = 0): IHistoryInfo[] => {
    const list = historyAllList.value.filter(item => item.t == t);

    return list;
  }
  const removeHistoryItem = (item: IHistoryInfo) => {
    const index = historyAllList.value.findIndex(row => row.t == item.t && row.d == item.d);

    if (index!== -1) {
      historyAllList.value.splice(index, 1);
    }
  }
  const removeHistoryAll = (t: number) => {
    historyAllList.value = historyAllList.value.filter(row => row.t != t);
  }

  const countType = (t: number = 0) => {
    return historyAllList.value.filter(row => row.t == t).length;
  }

  return {
    historyAllList,
    getHistory,
    addHistoryItem,
    removeHistoryItem,
    removeHistoryAll
  }
}