<template>
  <div>
    <div class="banner">
      <div class="main-wrap">
        <div class="f-32 c-33 f-bold">行业标准体系</div>
        <div class="banner-title mt-20 lh-26">
          各行业专家对行业垂直领域的精细划分，为不同行业内的生产、经营、服务构建一张精细的蓝图。
          <br />
          通过行业标准体系的建立和完善，有助于提高行业整体的产品质量和服务水平，促进企业之间的公平竞争，保障消费者的合法权益，推动技术创新和行业的可持续发展，提高了行业的效率和效益。
        </div>
      </div>
    </div>
    <div class="content main-wrap">
      <ClientOnly>
        <div class="content-type">
          <div class="flex flex-center">
            <div @click="handleCut('-1')" class="content-type-item" :class="activeType == '-1' ? 'content-type-active' : ''">
              全部
            </div>
            <div
              @click="handleCut(item.dictValue)"
              v-for="(item, index) in typeList"
              :key="index"
              class="content-type-item"
              :class="item.dictValue == activeType ? 'content-type-active' : ''"
            >
              {{ item.dictLabel }}
            </div>
          </div>
        </div>
      </ClientOnly>
      <template v-if="tableData && tableData.length > 0">
        <div v-loading="loading" class="content-card">
          <div
            @click="handleJump('/data/systemDetail', { id: item.id })"
            v-for="item in tableData"
            :key="item.id"
            class="content-card-item"
          >
            <img
              @click.stop="handleSubscription(item)"
              v-if="item.subscription"
              src="@/assets/images/system/ydy.png"
              alt=""
              class="content-card-item-mark"
            />
            <img
              @click.stop="handleSubscription(item)"
              v-else
              src="@/assets/images/system/dy.png"
              alt=""
              class="content-card-item-mark"
            />
            <img
              :src="
                item.coverUrlFileList && item.coverUrlFileList.length > 0
                  ? item.coverUrlFileList[0].url
                  : '@/assets/images/system/card-cover.png'
              "
              alt=""
              class="content-card-item-cover"
            />
            <div class="content-card-item-mask"></div>
            <div class="content-card-item-down">
              <div>
                <span v-if="item.hotTag == 1" class="iconfont icon-remen f-20 c-ff0000"></span>
                <span class="f-16 c-33 f-bold ml-5 content-card-item-down-title">{{ item.systemName }}</span>
              </div>
              <div class="f-14 c-88 lh-22 overflow-three-ellipsis mt-10 mb-20">
                {{ item.systemDescription }}
              </div>
              <div class="f-14 c-primary content-card-item-down-more">
                了解更多
                <span class="iconfont icon-arrow-right f-14 ml-5"></span>
              </div>
            </div>
          </div>
        </div>
        <BxcPagination v-model:page="form.pageNum" v-model:limit="form.pageSize" :total="tableTotal" @pagination="getData" />
      </template>
      <BxcEmpty v-else />
    </div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" />
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '行业标准体系_标准体系构建_专业标准体系_岗位标准体系_标信查平台',
    meta: [
      { name: 'keywords', content: '标准体系，专业标准体系，岗位标准体系，行业标准体系构建' },
      {
        name: 'description',
        content:
          '标信查平台数据服务频道，可以为企业提供标准托管、标准查新、标准情报、标准灯塔、标准战略分析等多种标准信息化服务，助力广大企业更好发展。',
      },
    ],
  });

  import { ElMessageBox } from 'element-plus';
  import { getSystemList } from '@/api/data/system';
  import { getDicts } from '@/api/common';
  import { handleJump } from '@/utils/common';
  import { type IDicts, type ISystemInfo, type IResponseData } from '@/types';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();
  const { $modal } = useNuxtApp();

  const loading = ref(false);
  const activeType = ref<string | number>('-1');
  const typeList = ref<IDicts[]>([]);
  const form = ref({
    pageNum: 1,
    pageSize: 10,
    industryType: '',
    systemType: 0,
  });
  const tableData = ref<ISystemInfo[]>([]);
  const tableTotal = ref(0);
  const openLogin = ref(false);

  let dictRes = <IResponseData>await getDicts('bxc_industry_type');
  typeList.value = dictRes.data;

  let { rows, total } = <IResponseData>await getSystemList(form.value);
  tableData.value = rows || [];
  tableTotal.value = total || 0;

  const getData = async (pageNum?: string) => {
    loading.value = true;
    if (pageNum) form.value.pageNum = 1;
    let { rows, total } = <IResponseData>await useHttp.get('/business/standardSystem/list', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
    nextTick(() => {
      loading.value = false;
    });
  };

  const handleCut = (data: any) => {
    activeType.value = data;
    form.value.industryType = activeType.value == '-1' ? '' : data;
    getData('pageNum');
  };

  const handleSubscription = async (row: any) => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      if (row.subscription) {
        ElMessageBox.confirm('确认取消订阅体系【' + row.systemName + '】？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          lockScroll: false,
        })
          .then(() => {
            useHttp.delete('/business/standardSystemSubscription/delete/' + row.id).then(res => {
              let data = res as IResponseData;
              if (data.code == 200) {
                $modal.msgSuccess('取消订阅！');
                getData('pageNum');
              }
            });
          })
          .catch(() => {});
      } else {
        let data = <IResponseData>await useHttp.post('/business/standardSystemSubscription', { systemId: row.id });
        if (data.code == 200) {
          $modal.msgSuccess('订阅成功！');
          getData('pageNum');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .banner {
    width: 100%;
    min-height: 380px;
    background: url('@/assets/images/system/cover.png') no-repeat center;
    background-size: 100% 100%;

    .main-wrap {
      padding-top: 150px;
    }

    &-title {
      font-size: 16px;
      color: #333;
      width: 750px;
      line-height: 24px;
    }
  }

  .content {
    padding: 40px 0 65px;
    box-sizing: border-box;

    &-type {
      border-bottom: 1px solid #e8e8e8;

      &-item {
        padding: 10px 0;
        font-size: 16px;
        color: #333;
        cursor: pointer;
        border-bottom: 3px solid #fff;

        &:not(:first-child) {
          margin-left: 40px;
        }
      }

      &-active {
        font-weight: bold;
        color: $primary-color;
        border-bottom-color: $primary-color;
      }
    }

    &-card {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-top: 25px;

      &-item {
        position: relative;
        width: 100%;
        border-radius: 10px;
        cursor: pointer;

        &:hover &-down {
          background-color: #fff;
          box-shadow: 0px 0px 8px 1px rgba(0, 31, 89, 0.32);
        }

        &:hover &-mask {
          color: $primary-color;
          background-color: rgba(0, 0, 0, 0.6);
        }

        &:hover &-down-title {
          color: $primary-color !important;
        }

        &-mark {
          position: absolute;
          left: 0;
          top: 0;
          cursor: pointer;
          z-index: 99;
          height: 32px;
        }

        &-cover {
          width: 100%;
          height: 158px;
          border-radius: 10px;
          display: block;
          overflow: hidden;
          object-fit: cover;
        }

        &-mask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 158px;
          z-index: 9;
          border-radius: 10px;
          overflow: hidden;
        }

        &-down {
          position: relative;
          top: -22px;
          padding: 16px 20px;
          border-radius: 10px;
          overflow: hidden;
          background-color: #f8f9fb;
          z-index: 99;
          height: calc(100% - 158px - 22px);

          &-more {
            position: absolute;
            bottom: 16px;
            right: 20px;
          }
        }
      }
    }
  }
</style>
