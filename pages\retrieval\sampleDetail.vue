<template>
  <div class="container mt-60">
    <div class="main-wrap flex flex-sb">
      <div class="container-left">
        <RetrievalDetailSampleIntro :form="form" />
        <div class="f-20 f-bold">基础信息</div>
        <RetrievalDetailDescriptions :form="form" :data="basicInfo" class="mt-15" />
        <RetrievalDetailSampleUnit
          v-if="form.developUnitsList && form.developUnitsList.length > 0"
          :data="form.developUnitsList"
          class="mt-20"
        />
        <RetrievalDetailSampleStandard
          v-if="form.standardCodeList && form.standardCodeList.length > 0"
          :data="form.standardCodeList"
          class="mt-20"
        />
      </div>
      <div class="container-right">
        <div class="container-right-title">推荐</div>
        <div class="container-right-card">
          <template v-if="tableData.length > 0">
            <div
              @click="handleJump('/retrieval/domesticDetail', { id: item.id })"
              v-for="item in tableData"
              :key="item.id"
              class="container-right-card-item"
            >
              <div class="flex flex-ai-center" style="width: 100%">
                <span :class="getStatusColor(statusToString(item.standardStatus), 'text')">
                  【{{ item.standardStatusName }}】
                </span>
                <span class="flex-1 overflow-ellipsis container-right-card-item-title">{{ item.standardCode }}</span>
              </div>
              <div class="mt-10 c-88 overflow-ellipsis" style="width: 100%">&nbsp;{{ item.standardName }}</div>
            </div>
          </template>
          <BxcEmpty v-else />
        </div>
      </div>
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :type="3" @updateData="getDetail" />
  </div>
</template>

<script setup lang="ts">
  import { getSampleDetail, getRecommendedList } from '@/api/retrieval/sample';
  import { splitStrToArray, getStatusColor, handleJump } from '@/utils/common';
  import { type IStandardInfo, type ISampleInfo, type IResponseData } from '@/types';

  const route = useRoute();

  const form = ref<ISampleInfo>({
    sampleCode: '',
    sampleName: '',
    sampleStatus: 0,
    sampleStatusName: '',
    valuingDate: '',
    validityEndDate: '',
    approvalDate: '',
    registryUnit: '',
    developUnitsList: [],
    standardCodeList: [],
  });
  const basicInfo = reactive([
    { label: '标准样品编号：', fieldName: 'sampleCode' },
    { label: '标准样品名称：', fieldName: 'sampleName' },
    { label: '计划项目编号：', fieldName: 'projectCode' },
    { label: '状态：', fieldName: 'sampleStatusName' },
    { label: '定值日期：', fieldName: 'valuingDate' },
    { label: '批准日期：', fieldName: 'approvalDate' },
    { label: '有效截止日期：', fieldName: 'validityEndDate' },
    { label: '归口单位：', fieldName: 'registryUnit' },
    { label: '执行单位：', fieldName: 'applyUnit' },
    { label: '说明：', fieldName: 'remark' },
  ]);
  const tableData = ref<IStandardInfo[]>([]);
  const tableTotal = ref(0);

  let { data } = <IResponseData>await getSampleDetail(route.query.id as string | number);
  data.developUnitsList = data.developUnits ? splitStrToArray(data.developUnits, '、') : [];
  data.standardCodeList = [];
  if (data.standardList && data.standardList.length > 0) {
    data.standardList.forEach((item: any) => {
      let obj = {
        id: item.id,
        label: item.standardCode,
      };
      data.standardCodeList.push(obj);
    });
  }
  form.value = data || {};

  let recommendedData = <IResponseData>await getRecommendedList(route.query.id as string | number);
  tableData.value = recommendedData.data || [];
  tableTotal.value = recommendedData.total || 0;

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/search/sdc/stdSample/' + route.query.id);
    data.developUnitsList = data.developUnits ? splitStrToArray(data.developUnits, ',') : [];
    data.standardCodeList = [];
    if (data.standardList && data.standardList.length > 0) {
      data.standardList.forEach((item: any) => {
        let obj = {
          id: item.id,
          label: item.standardCode,
        };
        data.standardCodeList.push(obj);
      });
    }
    form.value = data || {};
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;

    &-left {
      width: 800px;
      overflow: hidden;
    }

    &-right {
      width: 360px;
      overflow: hidden;

      &-icon {
        display: block;
        width: 100%;
        margin-bottom: 20px;
      }

      &-title {
        font-size: 20px;
        color: #fff;
        width: 100%;
        height: 55px;
        line-height: 55px;
        background-color: #f2a511;
        padding: 0 20px;
        box-sizing: border-box;
      }

      &-card {
        padding: 10px 18px;
        box-sizing: border-box;
        border: 1px solid #e8e8e8;

        &-item {
          font-size: 14px;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: self-start;
          overflow: hidden;
          cursor: pointer;

          &:hover &-title {
            color: $primary-color;
          }

          &:not(:first-child) {
            border-top: 1px solid #e5e8ef;
          }
        }
      }
    }
  }
</style>
