<template>
  <el-dialog
    width="930"
    title="标准图谱"
    v-model="props.visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
    :z-index="2000"
  >
    <div v-if="graphData && graphData.length > 0" ref="echartRef" class="_echart"></div>
    <BxcEmpty v-else />
  </el-dialog>
</template>

<script lang="ts" setup>
  import { getGraphDetail } from '@/api/retrieval/domestic';
  import { handleJump } from '@/utils/common';
  import * as echarts from 'echarts';
  import { type IResponseData } from '@/types';

  const route = useRoute();
  const { $modal } = useNuxtApp();

  const props = defineProps({
    visible: {
      type: Boolean as PropType<boolean>,
    },
  });

  const echartRef = ref<HTMLElement | null>();
  let chartInstance: echarts.ECharts | null = null;
  const graphData = ref([]);
  const graphLinks = ref([]);

  let { data } = <IResponseData>await getGraphDetail(route.query.id as string | number);
  if (data.data && data.data.length > 0) {
    graphLinks.value = data.links || [];
    graphData.value = Array.from(
      data.data
        .reduce((map: any, obj: any) => {
          map.set(obj.name, obj);
          return map;
        }, new Map())
        .values()
    );
  }

  onMounted(() => {
    nextTick(() => {
      initChart();
    });
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  });

  const initChart = () => {
    if (echartRef.value) {
      chartInstance = echarts.init(echartRef.value);
      const options = {
        tooltip: {
          show: false,
        },
        series: [
          {
            top: 20,
            left: 20,
            right: 20,
            bottom: 20,
            symbolSize: 88,
            type: 'graph',
            roam: 'none',
            layout: 'force',
            animationDuration: 1500,
            animationEasingUpdate: 'quinticInOut',
            emphasis: { disabled: true },
            itemStyle: { color: '#2F5AFF' },
            label: { show: true, fontSize: 18 },
            force: { repulsion: 500, edgeLength: [150, 170] },
            edgeSymbolSize: [10, 1],
            edgeSymbol: ['arrow', 'circle'],
            edgeLabel: { show: true, color: '#2F5AFF', fontSize: 14, formatter: '{c}' },
            data: graphData.value,
            links: graphLinks.value,
            lineStyle: { color: '#FFC600', opacity: 0.9, width: 1, curveness: 0 },
          },
        ],
      };
      chartInstance.setOption(options);
      chartInstance.on('click', params => {
        if (params.seriesType === 'graph' && params.dataType === 'node') {
          if (params.data) {
            const standardId = (params.data as { standardId: string }).standardId;
            if (!standardId) {
              $modal.msgWarning('该标准暂未收录！');
              return;
            }
            if (standardId != route.query.id) {
              handleJump('/retrieval/domesticDetail', { id: standardId });
            }
          }
        }
      });
      chartInstance.resize();
    }
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible']);
</script>

<style lang="scss" scoped>
  ._echart {
    width: 100%;
    height: 500px;
  }
</style>
