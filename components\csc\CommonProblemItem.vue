<template>
  <div v-if="props.list.length > 0" class="problem-list">
    <div v-for="item in props.list" :key="item.id" class="problem-item">
      <span @click="handleDetail(item)">{{item.title}}</span>
    </div>
  </div>
  <div v-else>
    <span class="c-33 f-14">暂无数据</span>
  </div>
</template>
<script lang="ts" setup>
interface IListItem {
  id: string
  title: string
}
const props = defineProps({
  tabType: {
    type: String,
    default: '0'
  },
  list: {
    type: Array<IListItem>,
    default: () => []
  }
})

const handleDetail = (item: any) => {
  navigateTo(`/csc/detail?id=${item.id}`,{
    open: {
      target: '_blank'
    }
  })
}

</script>
<style lang="scss" scoped>
.problem-list {
  display: flex;
  flex-wrap: wrap;
  align-items: 20px;
  gap: 20px;
  .problem-item {
    width: calc((100% - 60px)/4);
    font-size: 14px;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
    cursor: pointer;
    &:hover{
      color: $primary-color;
    }
    span{
      position: relative;
      margin-left: 15px;
      &::before{
        content: '';
        display: inline-block;
        width: 7px;
        height: 7px;
        position: absolute;
        left: -15px;
        top: 6px;
        background: #888888;
        border-radius: 50%;
      }
    }
    
  }
}
</style>