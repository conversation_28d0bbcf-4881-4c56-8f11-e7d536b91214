<template>
  <div v-loading="loading" class="search">
    <div class="f-26 f-bold c-33 text-center">查新结果</div>
    <el-form ref="formRef" :model="form" inline class="mt-35" label-width="auto" size="large">
      <el-form-item label="检索结果">
        <el-checkbox-group @change="handleChange" v-model="form.searchTypeList">
          <el-checkbox :value="'1'" :border="true">检索到</el-checkbox>
          <el-checkbox :value="'0'" :border="true">未检索到</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="标准状态" class="ml-80">
        <el-checkbox-group @change="handleChange" v-model="form.standardStatusList">
          <el-checkbox v-for="item in standardStatusOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
            {{ item.dictLabel }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <div class="flex flex-ai-center flex-sb">
      <div class="flex flex-ai-center f-14 c-33">
        <div class="circle"></div>
        查新标准：
        <span class="c-primary">{{ props.info.count || 0 }}</span>
        &nbsp;丨&nbsp;检索到标准：
        <span class="c-primary">{{ props.info.onCount || 0 }}</span>
        &nbsp;丨&nbsp;未检索到标准：
        <span class="c-primary">{{ props.info.nonCount || 0 }}</span>
        &nbsp;丨&nbsp;将实施：
        <span class="c-primary">{{ props.info.tobeCount || 0 }}</span>
        &nbsp;丨&nbsp;现行有效：
        <span class="c-primary">{{ props.info.activeCount || 0 }}</span>
        &nbsp;丨&nbsp;被替代：
        <span class="c-primary">{{ props.info.beReplacedCount || 0 }}</span>
        &nbsp;丨&nbsp;废止：
        <span class="c-primary">{{ props.info.abolishCount || 0 }}</span>
      </div>
      <div class="flex flex-ai-center">
        <span class="iconfont icon-xiazaimoban c-primary f-16"></span>
        <span @click="handleDownLoad" class="f-14 text-underline c-primary ml-5 pointer">下载查新结果</span>
      </div>
    </div>
    <el-table :data="currentPageData" class="mt-20 mb-30">
      <template #empty>
        <BxcEmpty />
      </template>
      <el-table-column type="index" label="序号" width="80">
        <template #default="{ $index }">
          {{ (form.pageNum - 1) * form.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="标准号" min-width="120">
        <template #default="{ row }">
          <span @click="handleClick('/retrieval/domesticDetail', { id: row.id })" :class="row.id ? 'c-primary pointer' : ''">
            {{ row.standardCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="standardName" show-overflow-tooltip label="标准名称" />
      <el-table-column prop="searchTypeName" show-overflow-tooltip label="检索结果" />
      <el-table-column prop="standardTypeName" show-overflow-tooltip label="标准类型" />
      <el-table-column show-overflow-tooltip label="标准状态">
        <template #default="{ row }">
          <span :class="getStatusColor(statusToString(row.standardStatus), 'text')">{{ row.standardStatusName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="repealDate" show-overflow-tooltip label="废止日期" />
      <el-table-column show-overflow-tooltip label="被替代标准号">
        <template #default="{ row }">
          <span
            @click="handleClick('/retrieval/domesticDetail', { id: item.id })"
            v-for="item in row.beReplacedStdList"
            :key="item.id"
            class="mr-10"
            :class="item.id ? 'c-primary pointer' : ''"
          >
            {{ item.standardCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="被替代标准名称">
        <template #default="{ row }">
          <span v-for="item in row.beReplacedStdList" :key="item.id" class="mr-10">
            {{ item.standardName }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <BxcPagination
      v-show="filteredDataLength > 0"
      v-model:page="form.pageNum"
      v-model:limit="form.pageSize"
      :total="filteredDataLength"
      @pagination="updateCurrentPageData"
      class="mb-40"
    />
  </div>
</template>

<script setup lang="ts">
  import { getDicts } from '@/api/common';
  import { type IStandardInfo, type IDicts } from '@/types';
  import { handleJump } from '@/utils/common';
  import { json2excel } from '@/utils/formatExcel';

  interface FormType {
    pageNum: number;
    pageSize: number;
    searchTypeList: number[];
    standardStatusList: number[];
  }

  const props = defineProps({
    info: {
      required: true,
      type: Object,
    },
    tableData: {
      type: Array as () => IStandardInfo[],
      default: () => [],
    },
  });

  const loading = ref(false);
  const standardStatusOptions = ref<IDicts[]>([]);
  const form = ref<FormType>({
    pageNum: 1,
    pageSize: 10,
    searchTypeList: [],
    standardStatusList: [],
  });
  const currentPageData = ref<any>([]);
  const filteredDataLength = ref(0);

  onMounted(() => {
    updateCurrentPageData();
  });

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const getSelect = () => {
    getDicts('bxc_standard_status').then((res: any) => {
      standardStatusOptions.value = res.data;
    });
  };

  const updateCurrentPageData = () => {
    loading.value = true;
    let allData = [];
    allData = props.tableData.filter((item: any) => {
      const hasTypeFilter = form.value.searchTypeList.length > 0;
      const hasStatusFilter = form.value.standardStatusList.length > 0;
      const matchesType = hasTypeFilter ? form.value.searchTypeList.includes(item.searchType) : true;
      const matchesStatus = hasStatusFilter ? form.value.standardStatusList.includes(item.standardStatus) : true;
      return matchesType && matchesStatus;
    });
    filteredDataLength.value = allData.length;
    currentPageData.value = allData.slice(
      (form.value.pageNum - 1) * form.value.pageSize,
      form.value.pageNum * form.value.pageSize
    );
    nextTick(() => {
      loading.value = false;
    });
  };

  const handleChange = () => {
    form.value.pageNum = 1;
    updateCurrentPageData();
  };

  const handleDownLoad = () => {
    let excelDatas: {
      tHeader: string[];
      filterVal: string[];
      tableDatas: IStandardInfo[];
      sheetName: string;
    }[];
    excelDatas = [
      {
        tHeader: ['标准号', '标准名称', '检索结果', '标准类型', '标准状态', '废止日期', '被替代标准号', '被替代标准名称'],
        filterVal: [
          'standardCode',
          'standardName',
          'searchTypeName',
          'standardTypeName',
          'standardStatusName',
          'repealDate',
          'beReplacedStandardCode',
          'beReplacedStandardName',
        ],
        tableDatas: props.tableData,
        sheetName: 'Sheet1',
      },
    ];
    let multiHeader = [['标准查新结果']];
    json2excel(excelDatas, multiHeader, '标准查新结果', true, 'xlsx');
  };

  const handleClick = (url: string, query?: Record<string, string>) => {
    if (query?.id) handleJump(url, query);
  };

  getSelect();
</script>

<style lang="scss" scoped>
  .circle {
    width: 7px;
    height: 7px;
    background-color: #045cff;
    border-radius: 50%;
    margin-right: 10px;
  }

  :deep(.el-checkbox) {
    margin-right: 0;
  }

  :deep(.el-checkbox:hover) {
    color: #ffffff;
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox-group .el-checkbox__input) {
    display: none;
  }

  :deep(.el-checkbox.is-bordered.is-checked) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox.is-bordered.is-checked .el-checkbox__label) {
    color: #ffffff;
  }

  :deep(.el-checkbox.is-bordered + .el-checkbox.is-bordered) {
    margin-left: 15px;
  }

  :deep(.el-checkbox-group) {
    display: flex;
    justify-content: space-between;
  }
</style>
