<template>
  <el-dialog :append-to-body="true" :lock-scroll="false" v-model="open" width="350px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
    <div class="cs-list">
      <div v-for="item in list" :key="item.userId" class="cs-item" @click="handleSelect(item)">
        <img src="@/assets/images/csc/cs.png" alt="">
        <div class="title">{{item.userName}}</div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { type ICustomer } from "@/types"
const props = defineProps({
  open: Boolean,
  list: {
    type: Array<ICustomer>,
    default: () => {
      return <ICustomer[]>[]
    }
  },
});
const { open, list } = toRefs(props)
const title = ref('选择客服')

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}

const handleSelect = (item: any) => {
  emit('success',item)
}

</script>
<style lang="scss" scoped>
.cs-list {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 25px;
  justify-content: space-between;
  gap: 30px;
  .cs-item{
    width: 110px;
    height: 110px;
    background: #FFFFFF;
    border-radius: 5px;
    border: 1px solid #E8E8E8;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title{
      margin-top: 10px;
      color: #333333;
    }
    &:hover{
      background: $primary-color;
      border-radius: 5px;
      border: 1px solid #E8E8E8;
      cursor: pointer;
      .title{
        color: #fff;
      }
    }
  }
}
</style>