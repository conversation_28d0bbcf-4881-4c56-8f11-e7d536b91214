<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="form.keyword"
                maxlength="50"
                placeholder="输入标准法规标题关键词检索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleSearchMoreCut" class="search-more">{{ searchMore ? '普通检索' : '高级检索' }}</div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
        <el-form v-show="searchMore" :model="form" inline class="form-inline mt-30" label-width="auto" size="large">
          <div class="form-inline">
            <el-form-item label="发布日期">
              <el-date-picker
                @change="handlePublishDate"
                @clear="getData('pageNum')"
                v-model="publishDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <div class="flex flex-sb flex-ai-center mb-15">
          <div class="flex-1 flex flex-ai-center f-14 c-33 table-statistic">
            <div @click="handleClearSort" class="pointer" :class="publishDateSort == null ? 'c-primary' : 'c-33'">默认</div>
            <div @click="handlePublishDateSort" class="flex flex-ai-center pointer">
              <span :class="publishDateSort == null ? 'c-33' : 'c-primary'">发布日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="publishDateSort && publishDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!publishDateSort && publishDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
              </div>
            </div>
            <div class="ml-30">
              为您找到
              <span class="c-primary">{{ tableTotal }}</span>
              条相关标准法规，更多内容请使用条件检索！
            </div>
          </div>
        </div>
        <el-table :data="tableData">
          <template #empty>
            <BxcEmpty />
          </template>
          <el-table-column type="index" label="序号" width="80">
            <template #default="{ $index }">
              {{ (form.pageNum - 1) * form.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="标题" min-width="150">
            <template #default="{ row }">
              <span @click="handleJump('/retrieval/lawDetail', { id: row.id })" class="c-primary pointer">
                {{ row.title }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="publishDate" show-overflow-tooltip label="发布日期" />
        </el-table>
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
          class="mt-30"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '法律法规检索_标准法律法规查询_标准法律法规_标信查平台',
    meta: [
      { name: 'keywords', content: '标准法律法规，标准法律法规检索，标准法律法规查询' },
      {
        name: 'description',
        content:
          '标信查平台及时收录各行业标准，国家标准，地方标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务，并为用户提供一些标准的增值服务。',
      },
    ],
  });

  import { getLawList } from '@/api/retrieval/law';
  import { type ILawInfo, type IResponseData } from '@/types';

  const route = useRoute();

  interface IForm {
    keyword?: string;
    pageNum: number;
    pageSize: number;
    publishStartDate?: string;
    publishEndDate?: string;
    queryDateType?: number | string;
  }

  const searchMore = ref(false);
  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
  });
  const publishDate = ref([]);
  const publishDateSort = ref<boolean | null>(null);
  const tableData = ref<ILawInfo[]>([]);
  const tableTotal = ref(0);

  if (route.query.keyword) {
    form.value.keyword = decodeURI(route.query?.keyword as string) || '';
  }

  let { rows, total } = <IResponseData>await getLawList(form.value);
  tableData.value = rows || [];
  tableTotal.value = total || 0;

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    form.value.publishStartDate = publishDate.value && publishDate.value.length > 0 ? publishDate.value[0] : '';
    form.value.publishEndDate = publishDate.value && publishDate.value.length > 0 ? publishDate.value[1] : '';
    let { rows, total } = <IResponseData>await useHttp.get('/search/process/lawsRegulations/list', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleReset = () => {
    publishDate.value = [];
    form.value.keyword = '';
    handleClearSort();
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(2);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    form.value.keyword = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    form.value.keyword = form.value.keyword?.replace(/^\s+|\s+$/g, '');
    if (form.value.keyword) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: form.value.keyword,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };

  const handlePublishDate = () => {
    if (publishDate.value && publishDate.value.length > 0) getData('pageNum');
  };

  const handleSearchMoreCut = () => {
    searchMore.value = !searchMore.value;
  };

  const handleClearSort = () => {
    publishDateSort.value = null;
    form.value.queryDateType = '';
    getData('pageNum');
  };

  const handlePublishDateSort = () => {
    publishDateSort.value = publishDateSort.value ? false : true;
    form.value.queryDateType = publishDateSort.value ? 1 : 0;
    getData('pageNum');
  };
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    :deep(.el-input) {
      width: 95% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 20px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-more {
      position: absolute;
      right: -70px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }

    &-reset {
      position: absolute;
      right: -113px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }

  .table-statistic {
    .pointer {
      margin-right: 30px;
    }
  }

  .c-CECECE {
    color: #cecece;
  }

  .icon-shang,
  .icon-xia {
    font-size: 10px !important;
  }
</style>
