<template>
  <div>
    <template v-if="props.tableData.length > 0">
      <div @click="toDetail(item)" v-for="item in props.tableData" :key="item.id" class="card pointer">
        <div class="f-16">
          <div class="flex flex-ai-center flex-sb overflow-ellipsis">
            <div class="flex flex-ai-center overflow-ellipsis">
              <div v-html="item.intro" class="c-33 f-bold overflow-ellipsis mr-10 card-title"></div>
              <div :class="getStatusColor('blue')" class="mr-10">{{ item.standardTypeName }}</div>
              <div :class="getStatusColor(statusToString(item.standardStatus))" class="mr-10">{{ item.standardStatusName }}</div>
            </div>
            <span
              v-if="item.standardType != 5 && item.standardType != 6"
              @click.stop="handleTrusteeship(item)"
              class="iconfont f-25"
              :class="item.trusteeshipType == 1 ? 'icon-kuangjituoguan c-primary' : 'icon-tuoguan c-88'"
            ></span>
          </div>
          <div v-if="item.standardNameEn" class="c-99 mt-10 overflow-ellipsis">
            {{ item.standardNameEn }}
          </div>
        </div>
        <div class="flex flex-ai-center mt-10">
          <div class="flex">
            <div v-if="item.standardAttrName" :class="getStatusColor('purple')" class="mr-10">{{ item.standardAttrName }}</div>
            <div v-if="item.standardTextPages" :class="getStatusColor('orange')" class="mr-10">
              {{ item.standardTextPages }}页
            </div>
          </div>
          <div v-if="item.beReplacedStandardCode" :class="getStatusColor('sky-blue')">{{ item.beReplacedStandardCode }}</div>
        </div>
        <div class="f-14 c-33 mt-15 flex flex-1">
          <div class="w-20vw overflow-ellipsis mr-10">发布日期：{{ item.publishDate || '-' }}</div>
          <div class="w-20vw overflow-ellipsis mr-10">实施日期：{{ item.executeDate || '-' }}</div>
          <div v-if="item.repealDate" class="w-20vw overflow-ellipsis mr-10">废止日期：{{ item.repealDate }}</div>
          <div v-if="item.standardTypeCodeGbName" class="w-20vw overflow-ellipsis mr-10">
            CCS分类：{{ item.standardTypeCodeGbName }}
          </div>
          <div v-if="item.standardTypeCodeIsoName" class="w-20vw overflow-ellipsis">
            ICS分类：{{ item.standardTypeCodeIsoName }}
          </div>
        </div>
        <div @click.stop="handleTreePreview(item)" v-if="item.searchContent" class="flex f-14 mt-15 overflow-two-ellipsis">
          <span class="c-ff0000">【正文检索】</span>
          <span v-html="item.searchContent" class="lh-24"></span>
        </div>
      </div>
    </template>
    <BxcEmpty v-else />
    <RetrievalTreePreview
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :standardId="standardId"
      :standardName="standardName"
      :standardCode="standardCode"
      :treeId="treeId"
      :searchValue="props.params?.content"
    />
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleLoginSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ElMessageBox } from 'element-plus';
  import { getStatusColor, handleJump } from '@/utils/common';
  import { type IStandardInfo, type IResponseData } from '@/types';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();
  const { $modal } = useNuxtApp();

  const props = defineProps({
    params: {
      type: Object,
    },
    tableData: {
      required: true,
      type: Array<IStandardInfo>,
    },
    title: {
      type: String,
    },
    url: {
      required: true,
      type: String,
    },
  });

  const isValidRegExp = () => {
    try {
      new RegExp(props.params?.title, 'gi');
      return true;
    } catch (e) {
      return false;
    }
  };

  watch(
    () => props.tableData,
    (newValue, oldValue) => {
      if (newValue) {
        let text = '';
        newValue.forEach(item => {
          text =
            (item?.standardCode ? item.standardCode : '-') + '&nbsp; | &nbsp;' + (item?.standardName ? item.standardName : '-');
          if (props.params?.title && isValidRegExp()) {
            item.intro = text.replace(new RegExp(props.params.title, 'gi'), `<span class="c-ff0000">$&</span>`);
          } else {
            item.intro = text;
          }
          if (props.params?.content && isValidRegExp()) {
            item.searchContent = item.textRetrieval
              ? item.textRetrieval
                  .replace(/<\/?em>/g, '')
                  .replace(new RegExp(props.params.content, 'ig'), `<span class="c-ff0000">$&</span>`)
              : '';
          } else {
            item.searchContent = item.textRetrieval;
          }
        });
      }
    },
    { immediate: true, deep: true }
  );

  const openLogin = ref(false);
  const dialogVisible = ref(false);
  const standardId = ref('');
  const standardName = ref('');
  const standardCode = ref('');
  const treeId = ref('');

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const toDetail = (row: any) => {
    handleJump(props.url, { id: row.id, content: encodeURI(props.params?.content || '') });
  };

  const handleTreePreview = (row: any) => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      standardId.value = row.id;
      treeId.value = row.indexId;
      standardName.value = row.standardName;
      standardCode.value = row.standardCode;
      dialogVisible.value = true;
    }
  };

  const handleTrusteeship = async (row: any) => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      if (row.trusteeshipType == 1) {
        ElMessageBox.confirm('确认取消托管标准【' + row.standardCode + '】？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          lockScroll: false,
        })
          .then(() => {
            useHttp.delete('/search/trusteeshipManage/remove', { ids: [row.id] }).then(res => {
              let data = res as IResponseData;
              if (data.code == 200) {
                $modal.msgSuccess('取消标准托管成功！');
                emit('updateData');
              }
            });
          })
          .catch(() => {});
      } else {
        let data = <IResponseData>await useHttp.post('/search/trusteeshipManage', {
          standardId: row.id,
          standardCode: row.standardCode,
          standardType: row.standardType,
        });
        if (data.code == 200) {
          $modal.msgSuccess('标准托管成功！');
          emit('updateData');
        }
      }
    }
  };

  const handleLoginSuccess = () => {
    emit('updateData');
  }

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped>
  .card {
    border-radius: 3px;
    margin-bottom: 30px;
    padding: 20px;
    box-sizing: border-box;
    border: 1px solid #e8e8e8;
    position: relative;
    border-radius: 3px;

    &:hover {
      box-shadow: 0px 0px 8px 1px rgba(0, 0, 0, 0.09);

      .card-title {
        color: $primary-color !important;
      }
    }
  }

  .c-FFAE00 {
    color: #ffae00;
  }

  .w-20vw {
    width: 19.5%;
  }
</style>
