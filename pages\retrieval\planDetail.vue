<template>
  <div class="container mt-60">
    <div class="main-wrap flex flex-sb">
      <div class="container-left">
        <RetrievalDetailPlanIntro :form="form" />
        <div class="f-20 f-bold">基础信息</div>
        <RetrievalDetailDescriptions :form="form" :data="basicInfo" :minWidth="70" class="mt-15" />
        <template v-if="form.standardList && form.standardList.length > 0">
          <div class="f-20 f-bold mt-30">修订标准</div>
          <div class="flex flex-wrap">
            <div
              @click="handleClick('/retrieval/domesticDetail', { id: item.id })"
              v-for="(item, index) in form.standardList"
              :key="index"
              class="f-14 c-33 mt-20 mr-10"
              :class="{ valid_link: item.id }"
            >
              {{ item.standardCode }} | {{ item.standardName }}
            </div>
          </div>
        </template>
        <template v-if="form.draftForSolicitingOpinionsList && form.draftForSolicitingOpinionsList.length > 0">
          <div class="f-20 f-bold mt-30">征求意见稿</div>
          <div class="flex flex-wrap">
            <div
              @click="handlePreview(item)"
              v-for="(item, index) in form.draftForSolicitingOpinionsList"
              :key="index"
              class="f-14 mt-20 mr-10 jump-link c-primary"
            >
              征求意见稿{{ index + 1 }}
            </div>
          </div>
        </template>
        <template v-if="form.compilationInstructionsList && form.compilationInstructionsList.length > 0">
          <div class="f-20 f-bold mt-40">编制说明</div>
          <div class="flex flex-wrap">
            <div
              @click="handlePreview(item)"
              v-for="(item, index) in form.compilationInstructionsList"
              :key="index"
              class="f-14 mt-20 mr-10 jump-link c-primary"
            >
              编制说明{{ index + 1 }}
            </div>
          </div>
        </template>
        <RetrievalDetailDrafter
          v-if="form.draftersList && form.draftersList.length > 0"
          :data="form.draftersList"
          class="mt-40"
        />
        <RetrievalDetailUnit
          v-if="form.draftUnitsList && form.draftUnitsList.length > 0"
          :data="form.draftUnitsList"
          class="mt-40"
        />
      </div>
      <div class="container-right">
        <div class="container-right-title">推荐</div>
        <div class="container-right-card">
          <template v-if="tableData.length > 0">
            <div
              @click="handleJump('/retrieval/domesticDetail', { id: item.id })"
              v-for="item in tableData"
              :key="item.id"
              class="container-right-card-item"
            >
              <div class="flex flex-ai-center" style="width: 100%">
                <span :class="getStatusColor(statusToString(item.standardStatus), 'text')">
                  【{{ item.standardStatusName }}】
                </span>
                <span class="flex-1 overflow-ellipsis container-right-card-item-title">{{ item.standardCode }}</span>
              </div>
              <div class="mt-10 c-88 overflow-ellipsis" style="width: 100%">&nbsp;{{ item.standardName }}</div>
            </div>
          </template>
          <BxcEmpty v-else />
        </div>
      </div>
    </div>
    <RetrievalDetailTool :form="form" :isShowTrusteeship="false" :type="7" @updateData="getDetail" />
    <BxcPreviewFile v-if="open" v-model:open="open" :url="url" />
  </div>
</template>

<script setup lang="ts">
  import { getPlanDetail, getRecommendedList } from '@/api/retrieval/plan';
  import { splitStrToArray, getStatusColor, handleJump } from '@/utils/common';
  import { type IStandardInfo, type IPlanInfo, type IResponseData } from '@/types';

  const route = useRoute();

  const open = ref(false);
  const url = ref('');
  const form = ref<IPlanInfo>({
    planNumber: '',
    entryName: '',
    type: '',
    typeName: '',
    amend: '',
    amendName: '',
    projectStatus: '',
    projectStatusName: '',
    planReleaseDate: '',
    registryUnit: '',
  });
  const basicInfo = reactive([
    { label: '计划号：', fieldName: 'planNumber' },
    { label: '制修订：', fieldName: 'amendName' },
    { label: '计划类型：', fieldName: 'typeName' },
    { label: '状态：', fieldName: 'status' },
    { label: '下达日期：', fieldName: 'planReleaseDate' },
    { label: '项目周期：', fieldName: 'projectCycle' },
    { label: 'CCS号：', fieldName: 'standardTypeCodeGbName' },
    { label: 'ICS号：', fieldName: 'standardTypeCodeIsoName' },
    { label: '标准性质：', fieldName: 'standardAttrName' },
    { label: '标准类别：', fieldName: 'standardCategory' },
    { label: '归口单位：', fieldName: 'registryUnit' },
    { label: '执行单位：', fieldName: 'applyUnit' },
    { label: '主管部门：', fieldName: 'manageDept' },
  ]);
  const tableData = ref<IStandardInfo[]>([]);
  const tableTotal = ref(0);

  let { data } = <IResponseData>await getPlanDetail(route.query.id as string | number);
  data.draftForSolicitingOpinionsList = data.draftForSolicitingOpinions
    ? splitStrToArray(data.draftForSolicitingOpinions, ',')
    : [];
  data.compilationInstructionsList = data.compilationInstructions ? splitStrToArray(data.compilationInstructions, ',') : [];
  data.draftersList = data.drafters ? splitStrToArray(data.drafters) : [];
  data.draftUnitsList = data.draftUnits ? splitStrToArray(data.draftUnits) : [];
  data.status = data.terminationStatus == 999 ? data.terminationStatusName : data.projectStatusName;
  form.value = data || {};

  let recommendedData = <IResponseData>await getRecommendedList(route.query.id as string | number);
  tableData.value = recommendedData.data || [];
  tableTotal.value = recommendedData.total || 0;

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const getDetail = async () => {
    let { data } = <IResponseData>await useHttp.get('/search/sdc/nationalStandardPlanQuery/' + route.query.id);
    data.draftForSolicitingOpinionsList = data.draftForSolicitingOpinions
      ? splitStrToArray(data.draftForSolicitingOpinions, ',')
      : [];
    data.compilationInstructionsList = data.compilationInstructions ? splitStrToArray(data.compilationInstructions, ',') : [];
    data.draftersList = data.drafters ? splitStrToArray(data.drafters) : [];
    data.draftUnitsList = data.draftUnits ? splitStrToArray(data.draftUnits) : [];
    form.value = data || {};
  };

  const handleClick = (url: string, query?: Record<string, string>) => {
    if (query?.id) handleJump(url, query);
  };

  const handlePreview = (row: string) => {
    url.value = row;
    open.value = true;
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 45px 0;
    box-sizing: border-box;

    &-left {
      width: 800px;
      overflow: hidden;
    }

    &-right {
      width: 360px;
      overflow: hidden;

      &-icon {
        display: block;
        width: 100%;
        margin-bottom: 20px;
      }

      &-title {
        font-size: 20px;
        color: #fff;
        width: 100%;
        height: 55px;
        line-height: 55px;
        background-color: #f2a511;
        padding: 0 20px;
        box-sizing: border-box;
      }

      &-card {
        padding: 10px 18px;
        box-sizing: border-box;
        border: 1px solid #e8e8e8;

        &-item {
          font-size: 14px;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: self-start;
          overflow: hidden;
          cursor: pointer;

          &:hover &-title {
            color: $primary-color;
          }

          &:not(:first-child) {
            border-top: 1px solid #e5e8ef;
          }
        }
      }
    }
  }
</style>
