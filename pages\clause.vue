<template>
  <div class="clause-wrap">
    <div class="main-wrap clause-container">
      <div class="clause-menu-list">
        <NuxtLink v-for="(item,index) in clauseList" :key="index" :to="item.path" :class="{actived: route.path == item.path}">{{item.name}}</NuxtLink>
      </div>
      <div class="clause-content">
        <NuxtPage />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const route = useRoute()
const clauseList = [{
  name: '服务条款',
  path: '/clause/service'
},{
  name: '隐私政策',
  path: '/clause/privacy'
},{
  name: '法律声明',
  path: '/clause/law'
},{
  name: '版权声明',
  path: '/clause/copyright'
}]
</script>
<style lang="scss" scoped>
.clause-wrap{
  margin-top: 60px;
  background: #F8F9FB;
  
  .clause-container{
    padding: 30px 0px;
    display: flex;
    justify-content: space-between;
    gap: 25px;
    .clause-menu-list{
      display: flex;
      flex-direction: column;
      width: 275px;
      padding: 15px 10px;
      box-sizing: border-box;
      background: #FFFFFF;
      height: 222px;
      a{
        height: 48px;
        line-height: 48px;
        text-align: center;
        font-size: 16px;
        color: #333333;
        &.actived{
          background: $primary-color;
          font-weight: bold;
          color: #FFFFFF;
        }
      }
    }
    .clause-content{
      width: 900px;;
      padding: 30px 30px;
      background: #FFFFFF;
      box-sizing: border-box;
    }
  }
  
}
</style>