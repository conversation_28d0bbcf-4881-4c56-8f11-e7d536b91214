<template>
  <div class="mt-60">
    <div class="info-title">{{ info.fillingTheme }}</div>
    <div class="bg-F8F9FB">
      <div class="main-wrap">
        <el-descriptions>
          <el-descriptions-item label="发起部门：">{{ info.initiatingDepartment }}</el-descriptions-item>
          <el-descriptions-item label="填报对象：">{{ info.fillingObject }}</el-descriptions-item>
          <el-descriptions-item label="填报区域：">{{ info.fullRegionName }}</el-descriptions-item>
          <el-descriptions-item :span="3" label="填报时间段：">
            {{ info.fillingStartDate + ' 至 ' + info.fillingEndDate }}
          </el-descriptions-item>
          <el-descriptions-item :span="3" label="填报要求：">{{ info.fillingRequirement }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="main-wrap">
      <QuestionnaireBasicInformation v-show="stepsIndex == 1" :info="info" @handleNext="handleNext" class="mt-40" />
      <QuestionnaireStandardizationInformation
        v-show="stepsIndex == 2"
        @handleBack="handleBack"
        @handleNext="handleNext"
        class="mt-40"
      />
      <QuestionnairePersonInformation v-show="stepsIndex == 3" @handleBack="handleBack" @submitData="submitData" class="mt-40" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { getQuestionnaireDetail, addQuestionnaire, checkValidity } from '@/api/questionnaire';
  import { type IFillInInfo, type IResponseData } from '@/types';
  import { handleJump } from '@/utils/common';

  const { $modal } = useNuxtApp();
  const route = useRoute();
  const router = useRouter();

  const stepsIndex = ref(1);
  const info = ref<IFillInInfo>({
    fillingTheme: '',
    fillingStatus: '',
    fillingStatusName: '',
    fillingStartDate: '',
    fillingEndDate: '',
    initiatingDepartment: '',
    fullRegionName: '',
    fillingObject: '',
  });
  const form = ref({
    fillingTaskId: '',
  });

  let { data } = <IResponseData>await getQuestionnaireDetail(route.query.id as string | number);
  info.value = data || {};

  const handleBack = (stepIndex: number) => {
    stepsIndex.value = stepIndex - 1;
  };

  const handleNext = (data: object, stepIndex: number) => {
    stepsIndex.value = stepIndex + 1;
    form.value = { ...form.value, ...data };
  };

  const submitData = (data: object) => {
    checkValidity(route.query.id as string | number).then((res: any) => {
      if (res.data) {
        form.value = { ...form.value, ...data };
        form.value.fillingTaskId = info.value.id;
        addQuestionnaire(form.value).then((res: any) => {
          res.data ? $modal.msgSuccess('填报信息已提交') : $modal.msgError(res.message);
          router.replace('/questionnaire');
        });
      } else {
        $modal.msgError(res.message);
        router.replace('/questionnaire');
      }
    });
  };
</script>

<style lang="scss" scoped>
  .info {
    &-title {
      font-weight: 600;
      font-size: 22px;
      color: #333333;
      padding: 50px 0 30px;
      box-sizing: border-box;
      text-align: center;
    }
  }

  .bg-F8F9FB {
    background-color: #f8f9fb !important;
    padding: 30px 0 20px;
    box-sizing: border-box;
  }

  :deep(.el-descriptions__body) {
    background-color: #f8f9fb !important;
  }

  :deep(.el-descriptions__cell) {
    padding-bottom: 10px !important;
    box-sizing: border-box;
  }

  :deep(.el-descriptions__label) {
    color: #333 !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }

  :deep(.el-descriptions__cell) {
    width: calc(100% / 3) !important;
  }
</style>
