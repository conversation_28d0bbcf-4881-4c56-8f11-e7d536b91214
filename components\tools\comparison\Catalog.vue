<template>
  <div class="comparison-full-text-wrap">
    <div class="left">
      <div class="left-content scroll-style" style="background-color: #ECF5FC; padding:0px;" ref="div1" @scroll="handleScroll(1)">
        <el-tree
          :data="originTreeList"
          ref="treeRef"
          node-key="id"
          empty-text="暂无数据"
          :highlight-current="true"
          :props="defaultProps" 
          default-expand-all
          :expand-on-click-node="false"
          class="custom-origin-tree"
          style="background-color: #ECF5FC;"
          >
            <template #default="{ node, data }">
            
              <span class="custom-tree-node">
                <span v-if="node.level == 1" class="custom-label">
                  <i class="iconfont icon-wendang2 c-0096FF ml-5 mr-8"></i>
                  {{ node.label }}
                </span>
                <span v-else class="custom-label">
                  {{ node.label }}
                </span>
              </span>
            </template>
        </el-tree>
      </div>
    </div>
    <div class="right">
      <div class="right-content scroll-style" style="background-color: #EBF1FC; padding:0px;" ref="div2" @scroll="handleScroll(2)">
        <el-tree
          :data="compareTreeList"
          ref="tree2Ref"
          node-key="id"
          empty-text="暂无数据"
          :highlight-current="true"
          :props="defaultProps" 
          default-expand-all
          :expand-on-click-node="false"
          class="custom-compare-tree"
          style="background-color: #EBF1FC;"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span v-if="node.level == 1" class="custom-label">
                  <i class="iconfont icon-wendang2 c-primary ml-5 mr-8"></i>
                  <span v-showToolTip="['tree']">
                    <el-tooltip placement="bottom-start" :content="node.label">
                      <span class="level">{{ node.label }}</span>
                    </el-tooltip>
                  </span>
                </span>
                <span v-else class="custom-label">
                  <span v-showToolTip="['tree']">
                    <el-tooltip placement="bottom-start" :content="node.label">
                      <span class="level">{{ node.label }}</span>
                    </el-tooltip>
                  </span>
                </span>
              </span>
            </template>
        </el-tree>
      </div>
    </div>
    <!-- <div class="vs-btn" @click="handleVs">
      <img src="@/assets/images/tools/comparison/vs-btn.png" alt="">
    </div> -->
  </div>
</template>
<script setup lang="ts">
import { useComparisonStore } from '@/store/comparisonStore'

const props = defineProps({
  syncScroll: {
    type: Boolean,
    default: false,
  }
})

const comparisonStore = useComparisonStore()
const {div1,div2,setSyncScroll,handleScroll,handlescrollOffsets} = useSyncScroll()

const defaultProps = {
  children: 'children',
  label: 'name',
}
const { originTreeList, compareTreeList } = storeToRefs(comparisonStore);

const handleVs = () => {
  comparisonStore.handleVs()
}

watchEffect(()=>{
  setSyncScroll(props.syncScroll)
  if(props.syncScroll){
    handlescrollOffsets()
  }
})
</script>
<style lang="scss">
@import '@/assets/styles/comparison.scss';
</style>