<template>
  <div class="container mt-60">
    <div class="container-search">
      <div class="main-wrap">
        <el-popover
          popper-class="search-history-wrap"
          :visible="isShowPopover"
          ref="historyRef"
          placement="bottom-start"
          :show-arrow="false"
          :teleported="false"
          trigger="click"
          width="752"
          :offset="5"
        >
          <div class="search-history-container">
            <div class="title-wrap">
              <div class="title">最近检索</div>
              <div class="clear" @click="handleClear()">
                <span>清空历史</span>
                <i class="iconfont icon-qingkong1 ml-5"></i>
              </div>
            </div>
            <ul class="history-list">
              <li v-for="item in historyList" :key="item.d" @click.stop="handleClick(item)" class="history-item">
                <div class="title">{{ item.q }}</div>
                <div class="icon" @click.stop="handleClear(item)">
                  <i class="iconfont icon-guanbi" :style="{ 'font-size': '12px' }"></i>
                </div>
              </li>
            </ul>
          </div>
          <template #reference>
            <div class="search">
              <div class="flex">
                <div class="search-trapezoid search-trapezoid-left">
                  {{ searchType ? '正文' : '标题' }}
                </div>
                <div @click="handleSearchTypeCut" class="search-trapezoid ml-10">
                  {{ !searchType ? '正文' : '标题' }}
                </div>
              </div>
              <el-input
                @focus="setIsShow"
                @input="isShowPopover = false"
                @blur="handleBlur"
                @keyup.enter="handleSearch"
                v-model="keyword"
                maxlength="50"
                placeholder="请输入标准号、名称、内容关键词检索"
              />
              <div @click="handleSearch" class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
              <div @click="handleSearchMoreCut" class="search-more">{{ searchMore ? '普通检索' : '高级检索' }}</div>
              <div @click="handleReset" class="search-reset">重置</div>
            </div>
          </template>
        </el-popover>
        <el-form ref="formRef" :model="form" inline class="form-inline mt-30" label-width="auto" size="large">
          <el-form-item prop="standardTypeList" label="标准类型" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.standardTypeList">
              <el-checkbox v-for="item in standardTypeOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item prop="standardStatusList" label="标准状态" class="one-column">
            <el-checkbox-group @change="getData('pageNum')" v-model="form.standardStatusList">
              <el-checkbox v-for="item in standardStatusOptions" :key="item.dictValue" :value="item.dictValue" :border="true">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div v-if="searchMore" class="form-inline">
            <el-form-item prop="standardTypeCodeIso" label="ICS分类">
              <el-cascader
                @visible-change="e => handleCascader(e, 'standardTypeCodeIso')"
                @clear="handleClearCascader"
                @remove-tag="handleClearCascader"
                v-model="form.standardTypeCodeIso"
                clearable
                filterable
                :collapse-tags="true"
                :show-all-levels="false"
                :options="ICSOptions"
                :props="defaultICSProps"
                placeholder="请选择国际标准分类号"
                class="cascader-fold"
              ></el-cascader>
            </el-form-item>
            <el-form-item prop="standardTypeCodeGbs" label="CCS分类">
              <el-cascader
                @visible-change="e => handleCascader(e, 'standardTypeCodeGbs')"
                @clear="getData('pageNum')"
                @remove-tag="getData('pageNum')"
                v-model="form.standardTypeCodeGbs"
                clearable
                filterable
                collapse-tags
                :show-all-levels="false"
                :options="CCSOptions"
                :props="defaultCCSProps"
                placeholder="请选择中国标准分类号"
                class="cascader-fold"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="发布日期">
              <el-date-picker
                @change="handlePublishDate"
                @clear="getData('pageNum')"
                v-model="publishDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="实施日期">
              <el-date-picker
                @change="handleExecuteDate"
                @clear="getData('pageNum')"
                v-model="executeDate"
                clearable
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item prop="draftUnits" label="起草单位">
              <el-input
                @blur="handleComparison('draftUnits')"
                @clear="getData('pageNum')"
                v-model="form.draftUnits"
                maxlength="50"
                clearable
                placeholder="请输入标准起草单位名称"
              />
            </el-form-item>
            <el-form-item prop="drafters" label="起草人">
              <el-input
                @blur="handleComparison('drafters')"
                @clear="getData('pageNum')"
                v-model="form.drafters"
                maxlength="50"
                clearable
                placeholder="请输入标准起草人姓名"
              />
            </el-form-item>
            <el-form-item prop="standardAttr" label="标准性质">
              <el-select
                @change="handleComparison('standardAttr')"
                @clear="getData('pageNum')"
                v-model="form.standardAttr"
                clearable
                placeholder="请选择标准性质"
              >
                <el-option
                  v-for="item in standardAttrOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="registryUnit" label="归口单位">
              <el-input
                @blur="handleComparison('registryUnit')"
                @clear="getData('pageNum')"
                v-model="form.registryUnit"
                maxlength="50"
                clearable
                placeholder="输入归口单位名称"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div class="container-content">
      <div class="main-wrap">
        <div class="flex flex-sb flex-ai-center mb-15">
          <div class="flex-1 flex flex-ai-center f-14 c-33 table-statistic">
            <div
              @click="handleClearSort"
              class="pointer"
              :class="publishDateSort == null && executeDateSort == null ? 'c-primary' : 'c-33'"
            >
              默认
            </div>
            <div @click="handlePublishDateSort" class="flex flex-ai-center pointer">
              <span :class="publishDateSort == null ? 'c-33' : 'c-primary'">发布日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="publishDateSort && publishDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!publishDateSort && publishDateSort != null ? 'c-primary' : 'c-CECECE'"
                ></span>
              </div>
            </div>
            <div @click="handleExecuteDateSort" class="flex flex-ai-center pointer">
              <span :class="executeDateSort == null ? 'c-33' : 'c-primary'">实施日期</span>
              <div class="flex flex-column flex-center ml-5">
                <span
                  class="iconfont icon-shang"
                  :class="executeDateSort && executeDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
                <span
                  class="iconfont icon-xia"
                  :class="!executeDateSort && executeDateSort != null ? 'c-primary' : ' c-CECECE'"
                ></span>
              </div>
            </div>
            <div class="ml-30">
              为您找到
              <span class="c-primary">{{ tableTotal }}</span>
              条相关标准，更多内容请使用条件检索！
            </div>
          </div>
          <div class="flex flex-ai-center ml-100">
            <span class="f-14 c-33">展示方式</span>
            <span
              @click="handleShowFormCut('card')"
              class="iconfont f-20 ml-20 icon-liebiao pointer"
              :class="showForm == 'card' ? 'c-primary' : ''"
            ></span>
            <span
              @click="handleShowFormCut('table')"
              class="iconfont f-20 ml-20 icon-chart01 pointer"
              :class="showForm == 'table' ? 'c-primary' : ''"
            ></span>
          </div>
        </div>
        <RetrievalCard
          v-show="showForm == 'card'"
          :params="form"
          :tableData="tableData"
          url="/retrieval/domesticDetail"
          @updateData="getData"
        />
        <RetrievalTable
          v-show="showForm == 'table'"
          :params="form"
          :tableData="tableData"
          url="/retrieval/domesticDetail"
          @updateData="getData"
          class="mb-30"
        />
        <BxcPagination
          v-show="tableTotal"
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准检索_标准查询_国家标准_行业标准_团体标准_地方标准_标信查平台',
    meta: [
      { name: 'keywords', content: '标准检索，国家标准，行业标准，团体标准，地方标准，标准查询' },
      {
        name: 'description',
        content:
          '标信查平台及时收录各行业标准，国家标准，地方标准、团体标准、国外标准等标准文本、资讯、公告、及标准更替信息，与搜索完美结合，及时为企业提供各种标准化信息服务，并为用户提供一些标准增值服务。',
      },
    ],
  });

  import { getIcsList, getCcsList, getDomesticList } from '@/api/retrieval/domestic';
  import { getDicts } from '@/api/common';
  import { type IStandardInfo, type IDicts, type IResponseData } from '@/types';
  import type { FormInstance } from 'element-plus';

  const route = useRoute();
  const router = useRouter();

  interface IForm {
    pageNum: number;
    pageSize: number;
    title?: string;
    content?: string;
    standardTypeList?: string[];
    standardStatusList?: string[];
    standardTypeCodeIso?: string[];
    standardTypeCodeGbs?: string[];
    draftUnits?: string;
    drafters?: string;
    standardAttr?: string;
    registryUnit?: string;
    startPublishDate?: string;
    endPublishDate?: string;
    startExecuteDate?: string;
    endExecuteDate?: string;
    queryDateType?: number | string;
  }

  const formRef = ref<FormInstance>();
  const showForm = ref('card');
  const searchType = ref(false);
  const searchMore = ref(false);
  const standardTypeOptions = ref<IDicts[]>([]);
  const standardStatusOptions = ref<IDicts[]>([]);
  const standardAttrOptions = ref<IDicts[]>([]);
  const ICSOptions = ref([]);
  const CCSOptions = ref([]);
  const form = ref<IForm>({
    pageNum: 1,
    pageSize: 10,
  });
  const keyword = ref<string>('');
  const publishDate = ref([]);
  const publishDateSort = ref<boolean | null>(null);
  const executeDate = ref([]);
  const executeDateSort = ref<boolean | null>(null);
  const defaultICSProps = ref({
    checkStrictly: true,
    multiple: true,
    emitPath: false,
    label: 'name',
    value: 'icsTypeCode',
  });
  const defaultCCSProps = ref({
    checkStrictly: true,
    multiple: true,
    emitPath: false,
    label: 'name',
    value: 'ccsTypeCode',
  });
  const tableData = ref<IStandardInfo[]>([]);
  const tableTotal = ref(0);

  if (route.query.registryUnit) {
    form.value.registryUnit = decodeURI(route.query?.registryUnit as string) || '';
    searchMore.value = true;
  }
  if (route.query.title) {
    keyword.value = decodeURI(route.query?.title as string) || '';
    form.value.title = decodeURI(route.query?.title as string) || '';
  }

  try {
    let { rows, total } = <IResponseData>await getDomesticList(form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
    nextTick(() => {
      router.replace({ path: route.path, query: {} });
    });
  } catch (error) {
    nextTick(() => {
      router.replace({ path: route.path, query: {} });
    });
  }

  let standardTypeDict = <IResponseData>await getDicts('bxc_standard_type');
  standardTypeOptions.value = standardTypeDict.data.filter((item: any) => item.dictValue != 5 && item.dictValue != 6);

  let standardStatusDict = <IResponseData>await getDicts('bxc_standard_status');
  standardStatusOptions.value = standardStatusDict.data;

  let standardAttrDict = <IResponseData>await getDicts('bxc_standard_attr');
  standardAttrOptions.value = standardAttrDict.data;

  let ICSRes = <IResponseData>await getIcsList();
  ICSOptions.value = ICSRes.data;

  let CCSRes = <IResponseData>await getCcsList();
  CCSOptions.value = CCSRes.data;

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    form.value.startPublishDate = publishDate.value && publishDate.value.length > 0 ? publishDate.value[0] : '';
    form.value.endPublishDate = publishDate.value && publishDate.value.length > 0 ? publishDate.value[1] : '';
    form.value.startExecuteDate = executeDate.value && executeDate.value.length > 0 ? executeDate.value[0] : '';
    form.value.endExecuteDate = executeDate.value && executeDate.value.length > 0 ? executeDate.value[1] : '';
    form.value.content = '';
    form.value.title = '';
    if (searchType.value) {
      form.value.title = keyword.value;
      form.value.content = keyword.value;
    } else {
      form.value.title = keyword.value;
    }
    let { rows, total } = <IResponseData>await useHttp.get('/search/sdc/stdStandard/list', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
  };

  const handleReset = () => {
    if (formRef.value) formRef.value.resetFields();
    publishDate.value = [];
    executeDate.value = [];
    keyword.value = '';
    handleClearSort();
  };

  const { getHistory, addHistoryItem, removeHistoryItem, removeHistoryAll } = useSearchHistory();
  const historyRef = ref();
  const currentType = ref(0);
  const isShowPopover = ref(false);

  const historyList = computed(() => {
    return getHistory(currentType.value);
  });

  const handleClick = (item: any) => {
    keyword.value = item.q;
    handleSearch();
  };

  const handleSearch = () => {
    keyword.value = keyword.value?.replace(/^\s+|\s+$/g, '');
    if (keyword.value) {
      isShowPopover.value = false;
      historyRef?.value.hide();
      let obj = {
        d: Date.now(),
        t: currentType.value,
        q: keyword.value,
      };
      addHistoryItem(obj);
    }
    getData('pageNum');
  };

  const handleBlur = () => {
    isShowPopover.value = false;
  };

  const setIsShow = () => {
    if (historyList.value && historyList.value.length > 0) {
      isShowPopover.value = true;
    } else {
      isShowPopover.value = false;
    }
  };

  const handleClear = (item: any = null) => {
    if (!item) {
      removeHistoryAll(currentType.value);
    } else {
      removeHistoryItem(item);
    }
    setIsShow();
  };

  let orginalForm = JSON.parse(JSON.stringify(form.value));
  const handleComparison = (fieldName: keyof typeof form.value) => {
    if (fieldName) {
      if (form.value[fieldName] != orginalForm[fieldName]) {
        getData('pageNum');
        orginalForm = JSON.parse(JSON.stringify(form.value));
      }
    }
  };

  const handleCascader = (event: any, fieldName: keyof typeof form.value) => {
    if (!event) handleComparison(fieldName);
  };

  const handleClearCascader = () => {
    nextTick(() => {
      getData('pageNum');
    });
  };

  const handlePublishDate = () => {
    if (publishDate.value && publishDate.value.length > 0) getData('pageNum');
  };

  const handleExecuteDate = () => {
    if (executeDate.value && executeDate.value.length > 0) getData('pageNum');
  };

  const handleSearchTypeCut = () => {
    searchType.value = !searchType.value;
    getData('pageNum');
  };

  const handleSearchMoreCut = () => {
    searchMore.value = !searchMore.value;
  };

  const handleClearSort = () => {
    publishDateSort.value = null;
    executeDateSort.value = null;
    form.value.queryDateType = '';
    getData('pageNum');
  };

  const handlePublishDateSort = () => {
    executeDateSort.value = null;
    publishDateSort.value = publishDateSort.value ? false : true;
    form.value.queryDateType = publishDateSort.value ? 1 : 0;
    getData('pageNum');
  };

  const handleExecuteDateSort = () => {
    publishDateSort.value = null;
    executeDateSort.value = executeDateSort.value ? false : true;
    form.value.queryDateType = executeDateSort.value ? 3 : 2;
    getData('pageNum');
  };

  const handleShowFormCut = (data: string) => {
    if (data == showForm.value) return;
    showForm.value = data;
    getData('pageNum');
  };
</script>

<style lang="scss" scoped>
  .container {
    &-search {
      background-color: #f8f9fb;
      padding: 40px 0 20px;
      box-sizing: border-box;
    }

    &-content {
      background-color: #fff;
      padding: 15px 0 40px;
      box-sizing: border-box;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 48px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    margin: 0 auto;
    position: relative;
    background-color: #fff;

    &-trapezoid {
      color: #333;
      font-size: 14px;
      line-height: 48px;
      cursor: pointer;

      &-left {
        width: 70px;
        color: #fff;
        text-align: left;
        padding-left: 15px;
        box-sizing: border-box;
        background-color: $primary-color;
        clip-path: polygon(0 100%, 0 0, 80% 0, 100% 100%);
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
      }
    }

    :deep(.el-input) {
      width: 75% !important;
    }

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      padding: 10px !important;
    }

    :deep(.el-input__inner) {
      width: 100% !important;
      text-align: center !important;
    }

    &-icon {
      width: 60px;
      height: 48px;
      background: $primary-color;
      border-radius: 0px 3px 3px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      :deep(.el-icon) {
        font-size: 24px;
        color: #fff;
      }
    }

    &-more {
      position: absolute;
      right: -70px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }

    &-reset {
      position: absolute;
      right: -113px;
      bottom: 0;
      font-size: 14px;
      color: $primary-color;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }

  .table-statistic {
    .pointer {
      margin-right: 30px;
    }
  }

  .c-CECECE {
    color: #cecece;
  }

  .icon-shang,
  .icon-xia {
    font-size: 10px !important;
  }

  :deep(.el-checkbox) {
    margin-right: 0;
    background-color: #fff !important;
  }

  :deep(.el-checkbox:hover) {
    color: #ffffff;
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox-group .el-checkbox__input) {
    display: none;
  }

  :deep(.el-checkbox.is-bordered.is-checked) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }

  :deep(.el-checkbox.is-bordered.is-checked .el-checkbox__label) {
    color: #ffffff;
  }

  :deep(.el-checkbox.is-bordered + .el-checkbox.is-bordered) {
    margin-left: 15px;
  }

  :deep(.el-checkbox-group) {
    display: flex;
    justify-content: space-between;
  }
</style>
