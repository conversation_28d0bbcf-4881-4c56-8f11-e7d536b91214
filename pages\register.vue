<template>
  <div class="register-wrap">
    <LoginHeader />
    <div class="register-container main-wrap">
      <div class="register-login">
        <span>已有账号？</span>
        <NuxtLink to="/login">立即登录</NuxtLink>
      </div>
      <div class="register-form-wrap">
        <div class="register-title">账号注册</div>
        <el-form ref="registerRef" :model="registerForm" :rules="registerRules" class="register-form">
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              type="text"
              size="large"
              autocomplete="new-password"
              placeholder="设置登录账户名称"
              maxlength="20"
            >
            </el-input>
          </el-form-item>
          <el-form-item class="mt-30" prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              size="large"
              autocomplete="new-password"
              placeholder="设置登录密码"
              show-password
              maxlength="20"
            >
            </el-input>
          </el-form-item>
          <el-form-item class="mt-30" prop="affirmUpwd">
            <el-input
              v-model="registerForm.affirmUpwd"
              type="password"
              size="large"
              autocomplete="new-password"
              placeholder="确认登录密码"
              show-password
              maxlength="20"
            >
            </el-input>
          </el-form-item>
          <el-form-item prop="phonenumber">
            <el-input
              v-model="registerForm.phonenumber"
              type="text"
              size="large"
              autocomplete="new-password"
              placeholder="请输入手机号码"
              maxlength="11"
            >
              <template #prefix>
                <div class="p-num">
                  <span>+86</span>
                  <div class="line"></div>
                </div>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item class="mt-30" prop="smsCode">
            <el-input
              v-model="registerForm.smsCode"
              type="text"
              size="large"
              autocomplete="new-password"
              placeholder="请输入验证码"
              maxlength="6"
              @keyup.enter="handleRegister"
            >
              <template #append>
                <div v-if="!time ||time == 0" @click="handleVerify" class="verify">获取验证码</div>
                <div v-else class="down-timer">{{time}}s 后重新获取</div>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="" prop="checked">
            <el-checkbox v-model="registerForm.checked" class="check-item">我已阅读及同意</el-checkbox>
            <span class="check-service">
              <NuxtLink to="/clause/service" target="_blank">服务条款</NuxtLink>
              <span>、</span>
              <NuxtLink to="/clause/privacy" target="_blank">隐私政策</NuxtLink>
            </span>
          </el-form-item>
          <el-form-item class="register-btn" style="width:100%;">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              style="width:100%;"
              @click.prevent="handleRegister"
            >
              <span v-if="!loading">注 册</span>
              <span v-else>注 册 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <LoginFooter class="register-footer" />
  </div>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { register } from '@/api/login';

definePageMeta({
  layout: 'login',
})
const registerRef = ref<FormInstance>();
const loading = ref(false);

const registerForm = ref({
  username: "",
  password: "",
  affirmUpwd: "",
  phonenumber: "",
  smsCode: "",
  checked: false,
});

const checkPassword = (rule: any, value: any, callback: any) => {
  if (value != registerForm.value.password) {
    callback(new Error("密码不一致"));
  } else {
    callback();
  }
}
const checkPasswordDifUserName = (rule: any, value: any, callback: any) => {
  if (value == registerForm.value.username) {
    callback(new Error("密码与账户名称不能一致"));
  } else {
    callback();
  }
}

const checkCheckbox = (rule: any, value: any, callback: any) => {
  if (value) {
    callback();
  } else {
    callback(new Error("请先阅读并同意"));
  }
}

const registerRules = {
  username: [
    { required: true, trigger: "blur", message: "请输入设置登录账户名称" },
    { pattern: userNamePattern, trigger: "blur", message: "长度应在4～20位，可包含字母、数字、特殊符号，且非特殊符号开头" }
  ],
  password: [
    { required: true, trigger: "blur", message: "请输入设置登录密码" },
    { pattern: passwordPattern, trigger: "blur", message: "长度应在6～20位，须包含字母、数字、特殊符号至少两种，且非特殊符号开头" },
    { validator: checkPasswordDifUserName, trigger: "blur" },
  ],
  affirmUpwd: [
    { required: true, trigger: "blur", message: "请输入确认登录密码" },
    { validator: checkPassword, trigger: "blur" },
  ],
  phonenumber: [
    { required: true, trigger: "blur", message: "请输入手机号码" },
    { pattern: mobileValidPattern, trigger: "blur", message: "请输入正确的手机号码" }
  ],
  smsCode: [{ required: true, trigger: "blur", message: "请输入验证码" }],
  checked: [{ validator: checkCheckbox, trigger: "change" }],
};
const { time, getMobileCode } = useMobileCode()
const router = useRouter()
const { $modal } = useNuxtApp()

const handleRegister = () => {
  registerRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      register(registerForm.value).then(res =>{
        $modal.msgSuccess('账号注册成功，请登录!')
        router.push('/login')
      }).finally(()=>{
        loading.value = false;
      })
    }
  })
}
const handleVerify = () => {
  if(time.value == 0) {
    registerRef.value?.validateField("phonenumber", (valid: boolean) => {
      if (valid) {
        let isSend = false; // 放重复点击
        if(isSend) return;
        
        getMobileCode(registerForm.value.phonenumber, 2).then(res => {
          isSend = true;
        }).finally(() => {
          isSend = false;
        })
      }
    });
  }
}

</script>
<style lang="scss" scoped>
.register-wrap{
  background: #F5F7FA;
  min-height: 100vh;
  .register-container{
    .register-login{
      font-size: 14px;
      color: #333333;
      height: 60px;
      line-height: 60px;
      text-align: right;
      a{
        color: $primary-color;
        text-decoration: underline;
      }
    }
  }
  .register-form-wrap{
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 50px 0 80px 0px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .register-title{
      font-weight: bold;
      font-size: 22px;
      color: #333333;
    }
    .register-form{
      margin-top: 40px;
      width: 400px;
      .check-item{
        color: #999999;
        font-size: 14px;
      }
      :deep(.el-checkbox__input.is-checked+.el-checkbox__label){
        color: #999999;
        cursor: default;
      }
      :deep(.el-checkbox__input.is-checked .el-checkbox__inner){
        background-color: $primary-color;
        border-color: $primary-color;
      }
      .check-service{
        color: $primary-color;
        a{
          color: $primary-color;
          &:hover{
            text-decoration: underline;
          }

        }
      }
      .register-btn{
        margin-top: 70px;
      }
    }
    :deep(.el-form-item) {
      width: 100%;
      height: 48px;
      line-height: 48px;
      border: none;
      margin-right: 0px !important;
      .el-input{
        height: 48px;
        line-height: 48px;
        border: none;
        
        font-size: 14px;
      }
      .el-input__wrapper,.el-input__inner{
        background: #F6F7F9;
        border: none;
        box-shadow: none;
        border-radius: 3px;
      }
      .el-input__prefix{
        margin-left: 5px;
        // width: 30px;
        .p-num{
          display: flex;
          align-items: center;
          span{
            font-size: 16px;
            color: #333333;
          }
          .line{
            width: 1px;
            height: 28px;
            background: #DCDCDC;
            margin: 0 10px;
          }
        }
      }
      .el-input-group__append{
        padding: 0px;
        .verify{
          padding: 0 12px;
          text-align: center;
          background: $primary-color;
          font-size: 14px;
          color: #ffffff;
          cursor: pointer;
        }
        .down-timer{
          padding: 0 12px;
          text-align: center;
          background: #89b3ff;
          font-size: 14px;
          color: #ffffff;
        }
      }
      
      .el-button{
        height: 48px;
        font-size: 18px;
        background: $primary-color;
        border-radius: 3px;
        font-weight: bold;
        border-color: $primary-color !important;
      }
    }
  }
  .register-footer{
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
  }
}
</style>