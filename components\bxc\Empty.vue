<template>
  <div :style="{width: props.width,height: props.height}" class="empty-wrap">
    <img :src="props.img" alt="">
    <div class="empty-text">
      {{ props.content }}
    </div>
  </div>
</template>
<script lang="ts" setup>
import defaultImg from '@/assets/images/layout/empty-data.png'
interface IEmpty {
  img?: string;
  content?: string,
  width?: string,
  height?: string,
  imgWidth?: string,
}

const props = withDefaults(defineProps<IEmpty>(), {
  img: () => defaultImg,
  content: () => '暂无数据...',
  width: () => '100%',
  height: () => '100%',
  imgWidth: () => '322px',
});
</script>
<style lang="scss" scoped>
.empty-wrap {
  --imgWidth: v-bind(props.imgWidth);

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  overflow: hidden;

  img {
    margin-top: 20px;
    width: var(--imgWidth);
  }
  .empty-text {
    font-size: 14px;
    color: #888888;
    line-height: 20px;
    margin-bottom: 30px;
  }

}
</style>