<template>
  <el-dialog
    width="620"
    v-model="props.visible"
    :title="props.data?.honorName ? '编辑标准化荣誉' : '新增标准化荣誉'"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" :label-position="'top'" :rules="rules">
      <el-form-item label="荣誉名称" prop="honorName">
        <el-input v-model="form.honorName" maxlength="50" placeholder="请输入荣誉名称" />
      </el-form-item>
      <el-form-item label="获得日期" prop="obtainDate">
        <el-date-picker
          v-model="form.obtainDate"
          clearable
          type="date"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          placeholder="年/月/日"
        />
      </el-form-item>
      <el-form-item label="附件" prop="honorFileList">
        <div>
          <BxcUploadImage
            v-model:value="form.honorFileList"
            :fileSize="5"
            :limit="2"
            :multiple="true"
            :fileType="['jpeg', 'jpg', 'png', 'bmp']"
            :responseFn="handleResponse"
            @success="handleUpload('honorFileList', formRef)"
          />
          <div class="c-ff0000 mt-10 flex flex-ai-center f-14">
            支持文件格式：jpeg、jpg、png、bmp；单个文件大小不超过5MB；最多上传文 件数：2
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex flex-jc-end">
        <el-button type="primary" plain @click="handleClose">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleConfirm(formRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import type { FormInstance } from 'element-plus';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
    },
  });

  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const form = ref<any>({
    honorName: '',
    obtainDate: '',
    honorFileList: [],
  });
  const rules = reactive({
    honorName: [{ required: true, message: '请输入荣誉名称', trigger: 'blur' }],
    obtainDate: [{ required: true, message: '请选择', trigger: 'change' }],
    honorFileList: [{ required: true, message: '请上传', trigger: 'change' }],
  });

  if (props.data?.honorName) {
    form.value = { ...props.data };
  }

  const handleResponse = (response: any) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleUpload = (type: string, formEl: FormInstance | undefined) => {
    if (!formEl) return;
    nextTick(() => {
      formEl.validateField(type);
    });
  };

  const handleConfirm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    loading.value = true;
    formEl.validate(valid => {
      if (valid) {
        emit('updateData', form.value);
        handleClose();
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'updateData']);
</script>

<style lang="scss" scoped>
  .tip {
    width: 100%;
    font-size: 14px;
    line-height: 20px;
    padding: 13px 20px;
    box-sizing: border-box;
    color: #888888;
    background-color: #f8f9fb;
  }

  :deep(.el-radio-button) {
    &:nth-child(n + 2) {
      margin-left: 20px;
    }
  }

  :deep(.el-radio-button__inner) {
    border-radius: var(--el-border-radius-base) !important;
    border: var(--el-border);
  }

  :deep(.el-radio-button__inner:hover) {
    border-color: var(--el-color-primary) !important;
  }

  :deep(.el-date-editor) {
    width: 100% !important;
  }
</style>
